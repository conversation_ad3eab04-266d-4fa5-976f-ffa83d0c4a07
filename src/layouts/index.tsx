import { Outlet } from 'react-router-dom'
import SidebarComponent from './Sidebar'
import { useState, useEffect, Suspense } from 'react'
import { ChevronLeftIcon, XMarkIcon } from '@heroicons/react/20/solid'
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  TransitionChild
} from '@headlessui/react'
import HeaderComponent from './Header'
import { classNames } from '../utils'
import { SSE_HEARTBEAT } from '../api/Endpoints'
import { AgentHeartbeatData } from '../models/agents'
import { useAppDispatch, useAppSelector } from '../stores/hooks'
import {
  selectAgentRefreshingState,
  selectListAgent,
  updateAgentHeartbeat
} from '../stores/Reducers/agentReducers'
import {
  selectListCamera,
  selectRefreshingState,
  updateHeartBeat
} from '../stores/Reducers/cameraReducers'
import { CamereHeartBeat } from '../models/camera'
import { Skeleton } from 'antd'
import { getSASToken } from '../api/Recordings'
import { getDashboardCamerasThunk } from '../stores/Reducers/dashboardReducer'

const AppLayout = () => {
  const [responsiveSidebarOpen, setResponsiveSidebarOpen] = useState(false)
  const [desktopSidebarOpen, setDesktopSidebarOpen] = useState(true)
  const listAgent = useAppSelector(selectListAgent)
  const listCameras = useAppSelector(selectListCamera)
  const cameraRefreshingState = useAppSelector(selectRefreshingState)
  const agentRefreshingState = useAppSelector(selectAgentRefreshingState)
  const dispatch = useAppDispatch()

  //handle SSE for agent/camera heartbeat status
  useEffect(() => {
    const newEventSource = new EventSource(
      import.meta.env.VITE_API_ENDPOINT + SSE_HEARTBEAT.HEART_BEAT,
      {}
    )
    //handling Agent heartbeat
    newEventSource.addEventListener(SSE_HEARTBEAT.AGENT_HEARTBEAT, (e) => {
      const agentHeartbeatData: AgentHeartbeatData = JSON.parse(e.data)
      listAgent.forEach((agent) => {
        const isAgentIncludedInList = // if the agent is in the list
          agent?.api_keys[0]?.api_key === agentHeartbeatData?.api_Key ||
          agent?.id === agentHeartbeatData?.id

        if (isAgentIncludedInList) {
          dispatch(
            updateAgentHeartbeat({
              ...agent,
              status: agentHeartbeatData.status
            })
          )
        }
      })
    })

    //handling Camera heartbeat
    newEventSource.addEventListener(SSE_HEARTBEAT.CAMERA_HEARTBEAT, (e) => {
      const heartbeatData: CamereHeartBeat = JSON.parse(e.data)
      listCameras.forEach((camera) => {
        if (camera.id === heartbeatData.id) {
          // if the camera is in the list, and the status is di   ferent
          dispatch(updateHeartBeat({ ...camera, status: heartbeatData.status }))
        }
      })
      dispatch(getDashboardCamerasThunk())
    })
    return () => {
      newEventSource?.close() // close the SSE connection when extablish a new one, or logout/close app
    }
  }, [cameraRefreshingState, agentRefreshingState]) // reruns the useefect when list camera/list agent is refetched

  useEffect(() => {
    getSASToken()
  }, [])

  return (
    <div className="h-dvh max-h-dvh relative bg-gray-50 w-screen flex">
      <Dialog
        open={responsiveSidebarOpen}
        onClose={setResponsiveSidebarOpen}
        className="relative z-50 lg:hidden"
      >
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-gray-900/80 transition-opacity duration-300 ease-linear data-[closed]:opacity-0"
        />

        <div className="fixed inset-0 flex">
          <DialogPanel
            transition
            className="relative mr-16 flex w-full max-w-xs flex-1 transform transition duration-300 ease-in-out data-[closed]:-translate-x-full"
          >
            <TransitionChild>
              <div className="absolute left-full top-0 flex w-16 justify-center pt-5 duration-300 ease-in-out data-[closed]:opacity-0">
                <button
                  type="button"
                  onClick={() => setResponsiveSidebarOpen(false)}
                  className="-m-2.5 p-2.5"
                >
                  <span className="sr-only">Close sidebar</span>
                  <XMarkIcon
                    aria-hidden="true"
                    className="h-6 w-6 text-white stroke-white fill-white"
                  />
                </button>
              </div>
            </TransitionChild>

            <aside className="lg:fixed h-full lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
              <SidebarComponent
                open={true}
                setResponsiveSidebarOpen={setResponsiveSidebarOpen}
              />
            </aside>
          </DialogPanel>
        </div>
      </Dialog>

      {/* Static sidebar for desktop */}
      <aside className="hidden lg:inset-y-0 items-center lg:flex">
        <SidebarComponent
          setResponsiveSidebarOpen={setResponsiveSidebarOpen}
          open={desktopSidebarOpen}
        />
      </aside>
      <ChevronLeftIcon
        onClick={() => setDesktopSidebarOpen((prev) => !prev)}
        cursor={'pointer'}
        className={classNames(
          'max-w-7 my-auto max-h-7 mx-1 hidden lg:flex',
          desktopSidebarOpen
            ? 'transition-all ease-linear'
            : 'rotate-180 transition-all ease-linear'
        )}
      />
      <div className="relative mx-0 lg:mr-6 lg:ml-0 lg:py-6 flex-1 h-dvh flex flex-col overflow-hidden">
        <HeaderComponent setSidebarOpen={setResponsiveSidebarOpen} />

        <main className="w-full bg-white p-4 sm:px-8 flex-1 flex flex-col overflow-y-auto overflow-x-hidden">
          <Suspense
            fallback={
              <div className="w-full flex flex-col gap-2">
                <Skeleton />
                <Skeleton />
                <Skeleton />
                <Skeleton />
                <Skeleton />
              </div>
            }
          >
            <Outlet />
          </Suspense>
        </main>
      </div>
    </div>
  )
}

export default AppLayout
