import { ChevronUpIcon } from '@heroicons/react/24/outline'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import {
  getUserByIdThunk,
  selectCurrentUser
} from '../../stores/Reducers/usersReducer'
import { KEY_STORAGE } from '../../enum/KeyStorage'
import { supabaseLogout } from '../../api/Auth'
import toast from 'react-hot-toast'
import { ROUTE_PATH } from '../../enum/RoutePath'
import { classNames, getErrorMessage } from '../../utils'
import LogoutIcon from '../../assets/svgs/LogoutIcon'
import DropdownMenus from '../../components/DropdownMenu'
import { useEffect } from 'react'
import PersonIcon from '../../assets/svgs/PersonIcon'
import ProfileIcon from '../../assets/svgs/ProfileIcon'
import cookie from 'js-cookie'

type Props = {
  context?: 'sidebar' | 'topbar' // to see if the component is in the sidebar or topbar
  sidebarCollapsed?: boolean
}

const DropdownProfileComponent = ({
  context = 'topbar',
  sidebarCollapsed
}: Props) => {
  const currentHash = window.location.hash.substring(1) // Remove the '#' character
  const params = new URLSearchParams(currentHash)
  const navigate = useNavigate()
  const currentUser = useAppSelector(selectCurrentUser)
  const dispatch = useAppDispatch()

  const handleLogout = async () => {
    const accessToken = cookie.get(KEY_STORAGE.ACCESS_TOKEN)
    try {
      await supabaseLogout(accessToken ?? '')
    } catch (error) {
      toast.error(getErrorMessage(error), { position: 'top-right' })
    } finally {
      navigate(ROUTE_PATH.Login, { replace: true })
      cookie.remove(KEY_STORAGE.ACCESS_TOKEN)
      cookie.remove(KEY_STORAGE.REFRESH_TOKEN)
      localStorage.removeItem(KEY_STORAGE.USER_ID)
    }
  }

  const profileMenus = [
    {
      id: '3',
      onClick: () => navigate(ROUTE_PATH.Profile),
      text: (
        <div className="flex items-center gap-2">
          <PersonIcon height={24} width={24} />
          <p>Profile</p>
        </div>
      )
    },

    {
      id: '2',
      onClick: handleLogout,
      text: (
        <div className="flex items-center gap-2">
          <LogoutIcon />
          <p>Log out</p>
        </div>
      )
    }
  ]

  //only run this if it's in the topbar
  useEffect(() => {
    if (context === 'sidebar') return
    const userId = localStorage.getItem(KEY_STORAGE.USER_ID)
    if (!params.get('provider_token')) {
      dispatch(getUserByIdThunk(userId ?? ''))
    } // only run when not redirected from azure
  }, [])

  return (
    <div className="flex justify-end flex-1 gap-x-4 self-stretch lg:gap-x-6">
      {/* Profile dropdown */}
      <DropdownMenus
        label={
          <div className="flex justify-between gap-2 w-full">
            <div className="flex flex-row-reverse gap-3 md:flex-row items-center">
              <ProfileIcon height={30} width={30} />
              <span
                aria-hidden="true"
                className={classNames(
                  'text-sm line-clamp-2 font-semibold leading-6 text-white'
                )}
              >
                {currentUser?.name === ''
                  ? currentUser.email.split('@')[0]
                  : currentUser?.name}
              </span>
            </div>
            <span
              className={classNames(
                'hidden lg:items-center',
                sidebarCollapsed ? 'lg:hidden' : 'lg:flex'
              )}
            >
              <ChevronUpIcon
                aria-hidden="true"
                className="ml-2 h-5 w-5 stroke-white"
              />
            </span>
          </div>
        }
        className={classNames(
          '!justify-start !w-full ring-strokeGrey !bg-transparent shadow-transparent',
          (context === 'topbar' || sidebarCollapsed) &&
            '!w-fit ring-transparent'
        )}
        popupDirection="left"
        menus={profileMenus}
      />
    </div>
  )
}

export default DropdownProfileComponent
