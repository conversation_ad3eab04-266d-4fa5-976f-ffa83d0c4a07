import { SVGAttributes, useEffect } from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import DashboardIcon from '../../assets/svgs/DashboardIcon'
import { ROUTE_PATH } from '../../enum/RoutePath'
import LiveviewIcon from '../../assets/svgs/LiveviewIcon'
import RecordingIcon from '../../assets/svgs/RecordingIcon'
import VideoIcon from '../../assets/svgs/VideoIcon'
import PersonIcon from '../../assets/svgs/PersonIcon'
import { classNames } from '../../utils'
import CameraIcon from '../../assets/svgs/CameraIcon'
import AgentManagementIcon from '../../assets/svgs/AgentManagementIcon'

import SettingsIcon from '../../assets/svgs/SettingsIcon'
import BellIcon from '../../assets/svgs/BellIcon'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import {
  fetchListNotificationThunk,
  selectUnreadNotification
} from '../../stores/Reducers/notificationsReducer'
import DropdownProfileComponent from '../DropdownProfile'
import {AppLogo} from '../../components/Logo'

type Props = {
  open?: boolean
  setResponsiveSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const SidebarComponent = ({ open, setResponsiveSidebarOpen }: Props) => {
  const appsRoutePages = [
    {
      name: 'Monitoring',
      children: [
        {
          name: 'Dashboard',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <DashboardIcon {...props} />
          ),
          route: ROUTE_PATH.Home
        },
        {
          name: 'Live View',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <LiveviewIcon {...props} />
          ),
          route:
            ROUTE_PATH.Liveview +
            `?${new URLSearchParams({ page: '1', pageSize: '4' })}`
        },
        {
          name: 'Recordings',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <RecordingIcon {...props} />
          ),
          route: ROUTE_PATH.Recording
        }
      ]
    },
    {
      name: 'Management',
      children: [
        {
          name: 'Agent Management',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <AgentManagementIcon {...props} />
          ),
          route:
            ROUTE_PATH.Agents +
            `?${new URLSearchParams({ page: '1', pageSize: '10' })}`
        },
        {
          name: 'Camera Management',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <CameraIcon {...props} />
          ),
          route:
            ROUTE_PATH.Camera +
            `?${new URLSearchParams({ page: '1', pageSize: '10' })}`
        },
        {
          name: 'Video Analytics Management',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <VideoIcon {...props} />
          ),
          route: ROUTE_PATH.VideoAnalytics
        },
        {
          name: 'Access Management',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <PersonIcon {...props} />
          ),
          route: ROUTE_PATH.Access_Management
        },
        {
          name: 'Notifications',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <BellIcon {...props} />
          ),
          route: ROUTE_PATH.Notification
        },
        {
          name: 'Settings',
          icon: (props: SVGAttributes<SVGSVGElement>) => (
            <SettingsIcon {...props} />
          ),
          route: ROUTE_PATH.Settings
        }
      ]
    }
  ]
  const unreadNotifications = useAppSelector(selectUnreadNotification)
  const location = useLocation()
  const dispatch = useAppDispatch()

  useEffect(() => {
    if (location.pathname !== ROUTE_PATH.Notification) {
      dispatch(fetchListNotificationThunk())
    }
  }, [])

  useEffect(() => {
    document.title =
      unreadNotifications === 0
        ? 'Volkswagen'
        : `Volkswagen (${unreadNotifications})`
  }, [unreadNotifications])

  return (
    <>
      <div
        className={classNames(
          'flex h-full flex-col z-0 gap-y-5 overflow-y-auto bg-gray-900 overflow-x-hidden px-6 pb-4 transition-all ease-in-out w-fit duration-200',
          open ? 'min-w-72 w-72 max-w-72' : 'min-w-20 max-w-20'
        )}
      >
        <AppLogo open={open} />
        <nav className="flex flex-1 flex-col w-full">
          <ul className={classNames('flex flex-1 flex-col gap-y-7 list-none')}>
            {appsRoutePages.map((route) => (
              <li
                key={route.name}
                className={classNames('', open ? 'mx-0' : 'mx-auto')}
              >
                <p
                  className={classNames(
                    'text-xs font-semibold leading-6 text-gray-400',
                    open ? 'flex' : 'hidden'
                  )}
                >
                  {route.name}
                </p>
                <ul className="-mx-2 space-y-1">
                  {route.children.map((item) => (
                    <li className="list-none" key={item.name}>
                      <NavLink
                        key={item.name}
                        aria-label={item.name}
                        onClick={() => setResponsiveSidebarOpen(false)}
                        className={({ isActive }) =>
                          classNames(
                            'group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6',
                            isActive
                              ? 'bg-gray-800 text-white'
                              : 'text-gray-400 hover:bg-gray-800 hover:text-white'
                          )
                        }
                        to={item.route}
                      >
                        {({ isActive }) => (
                          <div className="flex justify-between w-full">
                            <div className="flex gap-x-3">
                              <item.icon
                                className={classNames(
                                  'group-hover:fill-white',
                                  isActive ? 'fill-white' : ''
                                )}
                              />

                              <p
                                className={classNames(
                                  'group-hover:text-white transition-all ease-in-out',
                                  isActive ? 'text-white' : 'text-gray-400',
                                  !open
                                    ? 'md:hidden transition-all ease-in-out'
                                    : 'md:block transition-all ease-in-out'
                                )}
                              >
                                {item.name}
                              </p>
                            </div>
                            {item.route === ROUTE_PATH.Notification &&
                              unreadNotifications > 0 && (
                                <p
                                  className={classNames(
                                    'flex px-2 items-center justify-center size-6 rounded-full text-white bg-primary text-center',
                                    unreadNotifications > 99
                                      ? 'text-[10px]'
                                      : 'text-xs'
                                  )}
                                >
                                  {unreadNotifications > 99
                                    ? `99+`
                                    : unreadNotifications}
                                </p>
                              )}
                          </div>
                        )}
                      </NavLink>
                    </li>
                  ))}
                </ul>
              </li>
            ))}
          </ul>
        </nav>
        <div
          className={classNames(
            'hidden lg:flex justify-center w-full sticky bottom-0 bg-gray-900',
            open ? 'w-full' : 'w-fit'
          )}
        >
          <DropdownProfileComponent
            sidebarCollapsed={!open}
            context="sidebar"
          />
        </div>
      </div>
    </>
  )
}

export default SidebarComponent
