import { Bars3Icon } from '@heroicons/react/24/outline'
import { classNames, getErrorMessage } from '../../utils'
import { ROUTE_PATH } from '../../enum/RoutePath'
import toast from 'react-hot-toast'
import { useEffect, useRef } from 'react'
import { useAppDispatch } from '../../stores/hooks'
import { getWebSocketURL } from '../../api/Notifications'
import {
  addNewNotification,
  increaseUnreadNotification
} from '../../stores/Reducers/notificationsReducer'
import { INotification } from '../../models/notifications'
import NotificationItemComponent from '../../pages/Notification/ListNotificationComponent/NotificationItem'
import { Transition } from '@headlessui/react'
import DropdownProfileComponent from '../DropdownProfile'
import AlertNotificationComponent from '../../components/AlertNotification'
import { NOTIFICATION_TYPE } from '../../enum/Notification'
import { TOAST_LIST_UPDATED_ALERT } from '../../enum/AnalyticEvents'

type Props = {
  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const RECONNECT_DELAY = 5000 //5 seconds
const HeaderComponent = ({ setSidebarOpen }: Props) => {
  const dispatch = useAppDispatch()
  const socketRef = useRef<WebSocket>()
  const handleGetWebSocketURL = async () => {
    try {
      const res = await getWebSocketURL()
      if (!socketRef.current) {
        handleConnectWebsocket(res.url)
      }
    } catch (error) {
      toast.error(getErrorMessage(error))
    }
  }

  const closeWebSocket = () => {
    socketRef.current?.close()
    socketRef.current = undefined
  }

  const handleConnectWebsocket = (url: string) => {
    socketRef.current = new WebSocket(url)

    socketRef.current.onmessage = (event: MessageEvent<string>) => {
      const pathname = location.pathname
      const eventMessage: INotification = JSON.parse(event.data)
      dispatch(increaseUnreadNotification())
      toast.custom(
        (toastItem) => (
          <Transition appear show={toastItem.visible} transition>
            <div
              className={classNames(
                `max-w-96 min-w-80 transition duration-200 ease-in data-[closed]:opacity-0 rounded-lg shadow-lg border-gray-200 border border-solid`
              )}
            >
              <NotificationItemComponent isPopup notification={eventMessage} />
            </div>
          </Transition>
        ),
        {
          position: 'bottom-left',
          duration: 5000
        }
      )

      if (pathname === ROUTE_PATH.Notification) {
        dispatch(addNewNotification(eventMessage))
      }
      if (
        pathname === ROUTE_PATH.VideoAnalytics &&
        [NOTIFICATION_TYPE.VAAdded, NOTIFICATION_TYPE.VARemoved].includes(
          eventMessage.type
        )
      ) {
        toast.custom(
          (toastItem) => (
            <AlertNotificationComponent
              description="List VA has been updated by another user"
              toastItem={toastItem}
            />
          ),
          {
            position: 'top-center',
            duration: Infinity,
            id: TOAST_LIST_UPDATED_ALERT
          }
        )
      }
    }

    socketRef.current.onerror = () => {
      closeWebSocket()
      setTimeout(() => {
        handleGetWebSocketURL()
      }, RECONNECT_DELAY)
    }
  }
  useEffect(() => {
    if (!socketRef.current) {
      handleGetWebSocketURL()
    }
    return () => {
      closeWebSocket()
    }
  }, [])

  return (
    <div className="sticky lg:hidden top-0 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-gray-900 lg:bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
      <button
        type="button"
        onClick={() => setSidebarOpen(true)}
        className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
      >
        <span className="sr-only">Open sidebar</span>
        <Bars3Icon
          aria-hidden="true"
          className="h-6 w-6 stroke-white lg:stroke-black"
        />
      </button>
      <p className="text-base sm:text-xl lg:text-black text-white font-bold">
        Volkswagen
      </p>

      {/* Separator */}
      <div aria-hidden="true" className="h-6 w-px bg-gray-900/10 lg:hidden" />
      <div className="ml-auto">
        <DropdownProfileComponent />
      </div>
    </div>
  )
}

export default HeaderComponent
