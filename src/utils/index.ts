import { toast } from 'react-hot-toast'
import { MESSAGE_CONTENT } from '../enum/Notification'
export const DEFAULT_PAGE_SIZE = 10
import { IOption } from '../interfaces'
import { IRecordings } from '../models/recodings'
import moment from 'moment'
import { Dayjs } from 'dayjs'
import { AxiosError } from 'axios'
import Hls from 'hls.js'

export const ENV = import.meta.env.VITE_ENV
export const PLAYLIST = 'PLAYLIST'
export const SEGMENT = 'SEGMENT'
export const ERROR = 'ERROR'
export const TS_MIMETYPE = 'video/mp2t'
export const START_DOWNLOAD = 'Download'
export const DOWNLOAD_ERROR = 'Download Error'
export const STARTING_DOWNLOAD = 'Download starting'
export const SEGMENT_STARTING_DOWNLOAD = 'Segments downloading'
export const SEGMENT_STICHING = 'Stiching segments'
export const JOB_FINISHED = 'Ready for download'
export const SEGMENT_CHUNK_SIZE = 10
export const COOKIE_EXPIRE_TIME = 1000 * 60 * 60 * 24 * 30 //expires after 30 days

export const userRoles: IOption[] = [
  {
    label: 'User',
    value: 'user'
  },
  {
    label: 'Admin',
    value: 'admin'
  }
]
export function classNames(...classes: unknown[]): string {
  return classes.filter(Boolean).join(' ')
}

export const isIncludedInLowercase = (
  originalText: string,
  textToBeIncluded: string
) => {
  return originalText
    .toLocaleLowerCase()
    .includes(textToBeIncluded.toLocaleLowerCase())
}

export const handleCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success(MESSAGE_CONTENT.COPY_SUCCESS, { position: 'top-right' })
  } catch (err) {
    if (ENV === 'DEV') {
      console.error(err)
    }
    toast.error(MESSAGE_CONTENT.COPY_ERROR)
  }
}

export const renderInputStyle = (requirement: boolean) => {
  return classNames(
    'block w-full rounded-md border-0 py-1.5 text-mainBlack shadow-sm ring-1 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6',
    requirement ? ' ring-red-300 focus:ring-red-300' : 'ring-gray-300'
  )
}

export const saveToStorages = (
  key_storage: string,
  value: string,
  bothStorages?: boolean
) => {
  if (bothStorages) {
    localStorage.setItem(key_storage, value)
    return
  }
  sessionStorage.setItem(key_storage, value)
}

export const removeFromStorages = (key_storages: string[]) => {
  key_storages.forEach((key_storage) => {
    localStorage.removeItem(key_storage)
    sessionStorage.removeItem(key_storage)
  })
}
const isAxiosError = (error: unknown): error is AxiosError =>
  typeof error === 'object' && error !== null && 'response' in error

export const getErrorMessage = (error: unknown): string => {
  if (!isAxiosError(error)) {
    return MESSAGE_CONTENT.DEFAULT_ERROR
  }

  if (error.response?.data && typeof error.response?.data === 'object') {
    if ('message' in error.response.data) {
      return error.response?.data?.message as string
    }

    if ('msg' in error.response.data) {
      return error.response?.data?.msg as string
    }

    return MESSAGE_CONTENT.DEFAULT_ERROR
  }
  return MESSAGE_CONTENT.DEFAULT_ERROR
}

export const secondsToHMS = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  // Format to ensure two digits for minutes and seconds
  const formattedTime = [
    String(hours).padStart(2, '0'),
    String(minutes).padStart(2, '0'),
    String(remainingSeconds).padStart(2, '0')
  ].join(':')

  return formattedTime
}
export const paramsSerializer = (params: unknown): string => {
  if (!params || typeof params !== 'object') return ''

  const searchParams = new URLSearchParams()

  Object.entries(params as Record<string, unknown>).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((item) => searchParams.append(key, String(item)))
    } else {
      searchParams.append(key, String(value))
    }
  })

  return searchParams.toString()
}

export const formattedRecordingName = (recording: IRecordings) => {
  if (!recording.record_name) return recording.file_name

  return `${recording.record_name.split('/')[0]} - ${moment(
    recording.created_at
  ).format('MMMM Do YYYY, HH:mm:ss ')}` // return the recording name as <Camera_name> Local Date
}

export const renderUnauthorizedMessage = (errorMsg: string): string => {
  return errorMsg.toLocaleLowerCase() === 'access denied'
    ? "You don't have permission for this action!"
    : errorMsg
}

export const capitalizeFirstLetter = (string: string) => {
  if (!string) return '' // Handle empty strings
  return string.charAt(0).toUpperCase() + string.slice(1)
}

export const downloadFromAPI = (response: BlobPart, file_name: string) => {
  const url = window.URL.createObjectURL(new Blob([response]))

  // Create a temporary anchor element and trigger the download
  const a = document.createElement('a')
  a.href = url
  a.download = file_name // Specify the filename
  document.body.appendChild(a)
  a.click()
  a.remove()
  // Clean up the URL object
  window.URL.revokeObjectURL(url)
}

export const disabledFromDateTime = (fromDate: Dayjs, toDate: Dayjs) => {
  //disable toDate time to not exceeds toDate
  if (!fromDate || !toDate) {
    return {}
  } else {
    const isSameDay = fromDate.day() === toDate.day() // if both Date are the same
    const isSameHour = fromDate.hour() === toDate.hour() //if both hours are the same

    return {
      disabledHours: () =>
        isSameDay
          ? Array.from(
              { length: 24 - (toDate.hour() + 1) },
              (_, i) => i + (toDate.hour() + 1)
            ) // disable hours from toDate.hour() to 24
          : [],
      disabledMinutes: () =>
        isSameDay && isSameHour
          ? Array.from(
              { length: 60 - (toDate.minute() + 1) },
              (_, i) => i + (toDate.minute() + 1)
            ) // disable minutes from toDate.hour() to 60
          : []
    }
  }
}

export const disabledToDateTime = (toDate: Dayjs, fromDate: Dayjs) => {
  // disable toDate time to not below fromDate
  if (toDate && fromDate) {
    const isSameDay = toDate.day() === fromDate.day() // if both Date are the same
    const isSameHour = toDate.hour() === fromDate.hour() //if both hours are the same
    return {
      disabledHours: () =>
        isSameDay
          ? Array.from({ length: fromDate.hour() + 1 }, (_, i) => i - 1)
          : [], // Disable hours from 0 to fromDate.hoour()
      disabledMinutes: () =>
        isSameDay && isSameHour
          ? Array.from({ length: fromDate.minute() + 1 }, (_, i) => i - 1) // Disable minutes from 0 to fromDate.minute()
          : []
    }
  } else {
    return {}
  }
}

// Function to create a new AbortController and add it to the array
export const createAbortController = (
  abortControllers: AbortController[]
): AbortController => {
  const controller = new AbortController()
  abortControllers.push(controller)
  return controller
}

// Function to abort all AbortController instances
export const abortAllControllers = (abortControllers: AbortController[]) => {
  abortControllers.forEach((controller) => controller.abort())
  // Optionally clear the array if you don't need to reuse the controllers
  abortControllers.length = 0
}

export const trimText = (text: string) => text.trimStart().replace(/\s+$/, ' ')

export const getHLSThumbnail = (url: string, timestamp: number) => {
  const video = document.createElement('video')
  const hls = new Hls()
  hls.loadSource(url)
  hls.attachMedia(video)
  if (!video) {
    console.log('NO VIDEO')
    return
  }

  // Set the video to the desired timestamp
  video.currentTime = timestamp

  // Wait for the video to seek to the timestamp
  video.onseeked = () => {
    const canvas = document.createElement('canvas')
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    const ctx = canvas.getContext('2d')
    if (!ctx) return
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

    // Convert canvas to image data URL
    const imageDataUrl = canvas.toDataURL('image/jpeg')

    return imageDataUrl
  }
}

export const formatBytes = (bytes: number): string => {
  // Define conversion constants
  const KB = 1024
  const MB = KB * 1024
  const GB = MB * 1024

  // Check if bytes is negative
  if (bytes < 0) {
    return 'Invalid input: bytes cannot be negative'
  }

  // Convert based on size
  if (bytes >= GB) {
    return `${(bytes / GB).toFixed(2)} GB`
  } else if (bytes >= MB) {
    return `${(bytes / MB).toFixed(2)} MB`
  } else if (bytes >= KB) {
    return `${(bytes / KB).toFixed(2)} KB`
  } else {
    return `${bytes} bytes`
  }
}
