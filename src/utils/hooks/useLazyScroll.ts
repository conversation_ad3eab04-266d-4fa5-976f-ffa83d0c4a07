import { useEffect, useRef } from 'react'
import { useDebounce } from './useDebounce'

type Props = {
  onPageChange: (page: number) => void //change page function
  currentPage: number //current page
  searchText: string // text for searching
  fetchDataFunc: () => void //function to fetch data from api
}
const useLazyScroll = ({
  onPageChange,
  currentPage,
  searchText,
  fetchDataFunc
}: Props) => {
  const haveMoreData = useRef<boolean>(true) //flag to determine if there are more pages

  //custom hook for debounce search text
  const { isMounted } = useDebounce({
    func: () => {
      haveMoreData.current = true
      if (currentPage > 1) {
        onPageChange(1)
        return
      }
      fetchDataFunc()
    },

    searchText: searchText
  })

  const fetchMoreFunc = (e: unknown) => {
    if (!e || typeof e !== 'object' || !('target' in e)) return
    //detect when scroll to the end of the div
    const target = e.target as HTMLElement
    if (
      target.scrollTop + target.offsetHeight === target.scrollHeight &&
      haveMoreData.current
    ) {
      onPageChange(currentPage + 1)
    }
  }

  const noMoreData = () => {
    // mark the flag that there's no more data
    haveMoreData.current = false
  }

  const markAsMounted = () => {
    //mark the flag that component is mounted
    isMounted.current = true
  }

  useEffect(() => {
    if (!isMounted.current) {
      fetchDataFunc()
    }
    return () => {
      onPageChange(1) // change the page back to 1 when unmount
      //   dispatch(setProjectSearchText(''))
    }
  }, [])

  useEffect(() => {
    if (haveMoreData.current && isMounted.current) {
      fetchDataFunc()
    }
  }, [currentPage])

  return { fetchMoreFunc, noMoreData, markAsMounted }
}

export default useLazyScroll
