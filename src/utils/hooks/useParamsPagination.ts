import { useNavigate, useSearchParams } from 'react-router-dom'
type UseParamsPaginationProps = {
  navigatePath: string
}
export const useParamsPagination = ({
  navigatePath
}: UseParamsPaginationProps) => {
  const [params] = useSearchParams()
  const navigate = useNavigate()
  const page = params.get('page') ?? '1'
  const pageSize = params.get('pageSize') ?? '10'
  const navigatePage = (page: number, pageSize: number) => {
    navigate(
      navigatePath +
        `?${new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString()
        })}`
    )
  }

  return {
    page,
    pageSize,
    navigatePage
  }
}
