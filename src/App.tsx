import { lazy, useState } from 'react'
import { LoaderIcon, ToastBar, Toaster } from 'react-hot-toast'
import { ChevronDownIcon } from '@heroicons/react/24/outline'
import { selectDownloadedFiles } from './stores/Reducers/recordingReducer'
import { useAppSelector } from './stores/hooks'
import { classNames } from './utils'

const AppRouter = lazy(() => import('./router/AppRouter'))

function App() {
  const [downloadToastCollapsed, setDownloadToastCollapsed] =
    useState<boolean>(false)
  const downloadedFiles = useAppSelector(selectDownloadedFiles)
  return (
    <>
      {/* toast for general purposes */}
      <Toaster position="top-right" />

      {/* toast only for tracking record download progress */}
      <div className="relative ">
        {downloadedFiles.length > 0 && (
          <div
            className={classNames(
              'flex gap-4 px-4 items-center border border-gray-100 border-solid  justify-between absolute right-4 top-2 w-[350px] z-[35] bg-white',
              downloadToastCollapsed
                ? 'shadow-lg'
                : 'shadow-t-lg border-b-0 border-l-0 border-r-0'
            )}
          >
            <p className="text-sm my-2 font-semibold flex gap-2 items-center">
              Downloading {downloadedFiles.length} files ... <LoaderIcon />
            </p>
            <ChevronDownIcon
              onClick={() => setDownloadToastCollapsed((prev) => !prev)}
              className={classNames(
                '',
                downloadToastCollapsed
                  ? 'transition-all ease-linear'
                  : 'rotate-180 transition-all ease-linear'
              )}
              height={20}
              strokeWidth={2}
              width={20}
            />
          </div>
        )}
        <Toaster
          toasterId="recording"
          position="top-right"
          containerClassName="overflow-auto !z-30 absolute shadow-2xl overflow-x-hidden"
          reverseOrder
          toastOptions={{ removeDelay: 0 }}
          gutter={0}
          containerStyle={{
            boxShadow: 'none',
            height: downloadToastCollapsed ? '0' : '200px'
          }}
        >
          {(toast) => {
            return (
              <div className="relative w-[350px]">
                {
                  <ToastBar
                    toast={toast}
                    style={{
                      borderBottom: '1px solid lightgray ',
                      borderRadius: 0,
                      paddingTop:
                        downloadedFiles[0] === toast.id ? '25px' : '10px'
                    }}
                  />
                }
              </div>
            )
          }}
        </Toaster>
      </div>
      <AppRouter />
    </>
  )
}

export default App
