import { ReactNode } from 'react'
import { FILTER_TYPES } from '../enum/FilterTypes'

export type ReduxStatePayload<T> = {
  type: string
  payload: T
}
export type IOption = { label: string; value: string; additonalValue?: string }
export interface IPagination {
  'page-no': number
  'page-size': number
}
export type EmailPasswordAuthenication = {
  email: string
  password: string
}
export type RejectedFiles = {
  name: string
  rejectedReason: string
}

export type UpdateFilters = {
  type: FILTER_TYPES
  data: IOption
}

export interface ITabNavigationLinks {
  name: string
  href: string
  current: boolean
}
export type Menu = {
  text: string | ReactNode
  id: string
  onClick: () => void
  disabled?: boolean
}
