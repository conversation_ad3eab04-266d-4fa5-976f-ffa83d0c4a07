import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import SecondaryButton from '../../../components/SecondaryButtons'

describe('SecondaryButton', () => {
  it('renders correctly with children', () => {
    render(<SecondaryButton>Click</SecondaryButton>)
    expect(screen.getByText('Click'))
  })

  it('calls onClick when clicked', async () => {
    const handleClick = vi.fn()
    render(<SecondaryButton onClick={handleClick}>Click</SecondaryButton>)
    fireEvent.click(screen.getByText('Click'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('not call onClick when disabled', async () => {
    const handleClick = vi.fn()
    render(
      <SecondaryButton onClick={handleClick} isDisabled>
        Click
      </SecondaryButton>
    )
    fireEvent.click(screen.getByText('Click'))
    expect(handleClick).toHaveBeenCalledTimes(0)
  })

  it('applies correct styles when disabled', () => {
    render(<SecondaryButton isDisabled>Click</SecondaryButton>)
    const button = screen.getByText('Click')
    expect(button).toHaveClass('bg-opacity-30')
    expect(button).toBeDisabled()
  })

  it('uses the default button type when no type is provided', () => {
    render(<SecondaryButton>Click</SecondaryButton>)
    expect(screen.getByText('Click')).toHaveAttribute('type', 'button')
  })

  it('applies custom className', () => {
    render(<SecondaryButton className="custom-class">Click</SecondaryButton>)
    expect(screen.getByText('Click')).toHaveClass('custom-class')
  })
})
