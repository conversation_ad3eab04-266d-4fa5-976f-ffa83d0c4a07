import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import PrimaryButton from '../../../components/PrimaryButtons'

describe('PrimaryButton', () => {
  it('renders correctly with children', () => {
    render(<PrimaryButton>Click</PrimaryButton>)
    expect(screen.getByText('Click'))
  })

  it('calls onClick when clicked', async () => {
    const handleClick = vi.fn()
    render(<PrimaryButton onClick={handleClick}>Click</PrimaryButton>)
    fireEvent.click(screen.getByText('Click'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('not call onClick when disabled', async () => {
    const handleClick = vi.fn()
    render(
      <PrimaryButton onClick={handleClick} isDisabled>
        Click
      </PrimaryButton>
    )
    fireEvent.click(screen.getByText('Click'))
    expect(handleClick).toHaveBeenCalledTimes(0)
  })

  it('applies correct styles when disabled', () => {
    render(<PrimaryButton isDisabled>Click</PrimaryButton>)
    const button = screen.getByText('Click')
    expect(button).toHaveClass('bg-opacity-30')
    expect(button).toBeDisabled()
  })

  it('shows loader when loading', () => {
    const { container } = render(<PrimaryButton isLoading>Click</PrimaryButton>)
    const loaderIcon = container.querySelector('.go1858758034')
    expect(loaderIcon).toBeInTheDocument()
  })

  it('applies correct styles when loading', () => {
    render(<PrimaryButton isLoading>Click</PrimaryButton>)
    expect(screen.getByText('Click')).toHaveClass('bg-opacity-30')
    expect(screen.getByText('Click')).toBeDisabled()
  })

  it('not show loader when not loading', () => {
    const { container } = render(<PrimaryButton>Click</PrimaryButton>)
    const loaderIcon = container.querySelector('.go1858758034')
    expect(loaderIcon).not.toBeInTheDocument()
  })

  it('uses the correct button type', () => {
    render(<PrimaryButton type="submit">Click</PrimaryButton>)
    expect(screen.getByText('Click')).toHaveAttribute('type', 'submit')
  })

  it('uses the default button type when no type is provided', () => {
    render(<PrimaryButton>Click</PrimaryButton>)
    expect(screen.getByText('Click')).toHaveAttribute('type', 'button')
  })

  it('applies custom className', () => {
    render(<PrimaryButton className="custom-class">Click</PrimaryButton>)
    expect(screen.getByText('Click')).toHaveClass('custom-class')
  })
})
