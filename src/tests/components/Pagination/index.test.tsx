import { describe, expect, it, vi } from 'vitest'
import { fireEvent, render, screen } from '@testing-library/react'
import PaginationComponent from '../../../components/Pagination'

describe('PaginationComponent', () => {
  const onPageChange = vi.fn<(page: number) => void>()
  const totalItems = 2000
  const pageSize = 10
  const truncateLimit = 3
  const totalPages = Math.ceil(totalItems / pageSize)

  // ---------------- tests regarding to rendering --------------------
  it('should render previous buttons regardless of the number of pages', () => {
    const items = 1000
    render(
      <PaginationComponent
        totalItems={items}
        currentPage={1}
        onPageChange={onPageChange}
        pageSize={pageSize}
      />
    )
    expect(screen.getByTestId('previous-link')).toBeInTheDocument()
  })

  it('should render next buttons regardless of the number of pages', () => {
    const items = 1000
    render(
      <PaginationComponent
        totalItems={items}
        currentPage={1}
        onPageChange={onPageChange}
        pageSize={pageSize}
      />
    )
    expect(screen.getByTestId('next-link')).toBeInTheDocument()
  })

  it('should always show the last link', () => {
    render(
      <PaginationComponent
        totalItems={totalItems}
        currentPage={1}
        onPageChange={onPageChange}
        pageSize={pageSize}
      />
    )
    expect(screen.getByTestId('lastpage-link')).toHaveTextContent(
      totalPages.toString()
    )
  })

  it('should render only 2 more pages after the current page is 1 when total pages larger than the truncate limit', () => {
    // total pages = 200
    render(
      <PaginationComponent
        totalItems={totalItems}
        currentPage={1}
        onPageChange={onPageChange}
        pageSize={pageSize}
      />
    )
    const pageButtons = screen.queryAllByTestId('visible-link')
    expect(pageButtons).toHaveLength(truncateLimit - 1)
    expect(screen.getByTestId('truncate-link')).toBeInTheDocument()
    expect(screen.getByTestId('truncate-link')).toHaveTextContent('...')
  })

  it('should render total upto 2 more visible pages after and upto 2 more visible pages before the current page when the current page is larger than 1', () => {
    // total pages = 200

    render(
      <PaginationComponent
        totalItems={totalItems}
        currentPage={2}
        onPageChange={onPageChange}
        pageSize={pageSize}
      />
    )
    expect(screen.queryAllByTestId('visible-link').length).toBeLessThan(5)
    expect(screen.getByTestId('current-link')).toBeInTheDocument()
  })

  // ---------------- tests regarding to onClick function --------------------

  it('should redirect to the next page when clicking on the next button', () => {
    const currentPage = 2
    const handleChangePage = vi.fn<(page: number) => void>()
    render(
      <PaginationComponent
        totalItems={totalItems}
        currentPage={currentPage}
        onPageChange={handleChangePage}
        pageSize={pageSize}
      />
    )
    const nextButton = screen.getByTestId('next-link')
    fireEvent.click(nextButton)
    expect(handleChangePage).toHaveBeenCalledWith(currentPage + 1)
    expect(handleChangePage).toHaveBeenCalledTimes(1)
  })

  it('should not do anything when clicking on the next button if the current page is the last page', () => {
    const currentPage = totalPages
    const handleChangePage = vi.fn<(page: number) => void>()
    render(
      <PaginationComponent
        totalItems={totalItems}
        currentPage={currentPage}
        onPageChange={handleChangePage}
        pageSize={pageSize}
      />
    )
    const nextButton = screen.getByTestId('next-link')
    fireEvent.click(nextButton)
    expect(handleChangePage).toHaveBeenCalledTimes(0)
  })

  it('should redirect to the previous page when clicking on the previous button', () => {
    const currentPagew = 3
    const handleChangePage = vi.fn<(page: number) => void>()

    render(
      <PaginationComponent
        totalItems={totalItems}
        currentPage={currentPagew}
        onPageChange={handleChangePage}
        pageSize={pageSize}
      />
    )
    const previousButton = screen.getByTestId('previous-link')
    fireEvent.click(previousButton)
    expect(handleChangePage).toHaveBeenCalledWith(currentPagew - 1)
    expect(handleChangePage).toHaveBeenCalledTimes(1)
  })

  it('should not do anything when clicking on the previous button if the current page is 1', () => {
    const currentPage = 1
    const handleChangePage = vi.fn<(page: number) => void>()
    render(
      <PaginationComponent
        totalItems={totalItems}
        currentPage={currentPage}
        onPageChange={handleChangePage}
        pageSize={pageSize}
      />
    )
    const previousButton = screen.getByTestId('previous-link')
    fireEvent.click(previousButton)
    expect(handleChangePage).toHaveBeenCalledTimes(0)
  })

  it('should redirect to the same page value when clicking on a link', () => {
    const currentPage = 1
    const handleChangePage = vi.fn<(page: number) => void>()
    const pageValue = 3
    render(
      <PaginationComponent
        totalItems={totalItems}
        currentPage={currentPage}
        onPageChange={handleChangePage}
        pageSize={pageSize}
      />
    )
    fireEvent.click(screen.getByText(pageValue))
    expect(handleChangePage).toHaveBeenCalledWith(pageValue)
    expect(handleChangePage).toBeCalledTimes(1)
  })
})
