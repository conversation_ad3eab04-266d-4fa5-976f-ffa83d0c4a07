@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
/*CSS*/

@tailwind base;
@tailwind components;
@tailwind utilities;
html {
  font-family: theme('fontFamily.sans');
}
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: white;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
* {
  box-sizing: border-box;
  color: black;
  margin: 0;
  padding: 0;
  outline: none;
}
table {
  border-collapse: collapse;
}
::-webkit-scrollbar {
  /* Customize the scrollbar width */
  width: 8px !important;
  height: 8px;
  background-color: transparent;
}

::-webkit-scrollbar-track {
  /* Customize the scrollbar track */
  background-color: rgba(211, 211, 211, 0.1);
}

::-webkit-scrollbar-thumb {
  background-color: rgb(211, 211, 211);
  width: 3px !important;
  border-radius: 8px;

  /* Customize the scrollbar thumb */
}

button {
  border: none;
  background-color: transparent;
}

a {
  text-decoration: none;
}

#recording-month-pickers {
  font-weight: 600 !important;
}
.ant-picker-outlined:focus-within {
  outline: none !important;
  box-shadow: none !important;
}

@media (min-height: 550px) {
  #manage-dashboard {
    height: fit-content;
  }
  #manage-dashboard-table {
    height: 300px;
  }
}
