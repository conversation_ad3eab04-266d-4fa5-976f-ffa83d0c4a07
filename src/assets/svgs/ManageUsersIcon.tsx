import { SVGAttributes } from 'react'

const ManageUsersIcon = (props: SVGAttributes<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      width="512"
      height="512"
      x="0"
      y="0"
      viewBox="0 0 24 24"
      {...props}
    >
      <g>
        <circle cx="9" cy="5" r="5" opacity="1"></circle>
        <path
          d="M11.534 20.8a2.507 2.507 0 0 1 .203-2.8 2.506 2.506 0 0 1-.203-2.8l.809-1.4a2.507 2.507 0 0 1 2.527-1.224c.033-.082.075-.159.116-.237A4.717 4.717 0 0 0 13.25 12h-8.5A4.756 4.756 0 0 0 0 16.75v3.5c0 .414.336.75.75.75h10.899z"
          opacity="1"
        ></path>
        <path
          d="M21.703 18.469c.02-.155.047-.309.047-.469 0-.161-.028-.314-.047-.469l.901-.682a.5.5 0 0 0 .131-.649l-.809-1.4a.5.5 0 0 0-.627-.211l-1.037.437a3.715 3.715 0 0 0-.819-.487l-.138-1.101a.5.5 0 0 0-.496-.438h-1.617a.5.5 0 0 0-.496.438l-.138 1.101a3.731 3.731 0 0 0-.819.487l-1.037-.437a.5.5 0 0 0-.627.211l-.809 1.4a.5.5 0 0 0 .131.649l.901.682c-.02.155-.047.309-.047.469 0 .161.028.314.047.469l-.901.682a.5.5 0 0 0-.131.649l.809 1.401a.5.5 0 0 0 .627.211l1.037-.438c.253.193.522.363.819.487l.138 1.101c.031.25.243.438.495.438h1.617a.5.5 0 0 0 .496-.438l.138-1.101c.297-.124.567-.295.819-.487l1.037.437a.5.5 0 0 0 .627-.211l.809-1.401a.5.5 0 0 0-.131-.649zM18 20a2 2 0 1 1-.001-3.999A2 2 0 0 1 18 20z"
          opacity="1"
        ></path>
      </g>
    </svg>
  )
}

export default ManageUsersIcon
