import { SVGAttributes } from 'react'
interface Props extends SVGAttributes<SVGSVGElement> {
  gradientID?: string
}
const LogoIcon = (props: Props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="80"
      height="80"
      x="0"
      y="0"
      viewBox="0 0 24 24"
      {...props}
    >
      <circle
        r="20"
        cx="12"
        cy="12"
        fill="#ffffff"
        transform="matrix(0.32,0,0,0.32,8.16,8.16)"
      ></circle>
      <g>
        <linearGradient
          id={'a'}
          x1="5.11"
          x2="18.89"
          y1="18.89"
          y2="5.11"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopOpacity="1" stopColor="#6d03a8" offset="0"></stop>
          <stop stopOpacity="1" stopColor="#053cad" offset="1"></stop>
        </linearGradient>
        <path
          fill={`url(#a)`}
          d="M9.75 9 15 12l-5.25 3zm12 3A9.75 9.75 0 1 1 12 2.25 9.76 9.76 0 0 1 21.75 12zm-5 0a1.3 1.3 0 0 0-.65-1.12L10.18 7.5a1.29 1.29 0 0 0-1.29 0 1.28 1.28 0 0 0-.64 1.12v6.76a1.28 1.28 0 0 0 1.29 1.29 1.23 1.23 0 0 0 .64-.17l5.92-3.38a1.3 1.3 0 0 0 .65-1.12z"
          opacity="1"
          className="z-50"
        ></path>
      </g>
    </svg>
  )
}

export default LogoIcon
