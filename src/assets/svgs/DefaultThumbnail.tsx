import { SVGAttributes } from 'react'

const DefaultThumbnail = (props: SVGAttributes<SVGSVGElement>) => {
  return (
    <svg
      width="128"
      height="105"
      viewBox="0 0 128 105"
      xmlns="http://www.w3.org/2000/svg"
      fill="#B9B9B9"
      {...props}
    >
      <rect width="128" height="104.464" rx="8" />
      <path
        d="M83.8898 48.2624C86.5856 49.7962 86.5856 53.6817 83.8897 55.2156L56.2281 70.9546C53.5615 72.4718 50.25 70.546 50.25 67.478L50.25 36C50.25 32.932 53.5616 31.0061 56.2281 32.5234L83.8898 48.2624Z"
        fill="white"
      />
    </svg>
  )
}

export default DefaultThumbnail
