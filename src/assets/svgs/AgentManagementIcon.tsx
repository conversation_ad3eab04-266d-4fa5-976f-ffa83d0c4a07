import { SVGAttributes } from 'react'

const AgentManagementIcon = (props: SVGAttributes<SVGSVGElement>) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="#7A7A7A"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M4 6H20C20.55 6 21 6.45 21 7V11H23V6C23 4.9 22.1 4 21 4H3C1.9 4 1 4.9 1 6V18C1 19.1 1.9 20 3 20H12V18H4C3.45 18 3 17.55 3 17V7C3 6.45 3.45 6 4 6Z" />
      <path d="M15 12L9 8V16L15 12Z" />
      <path d="M22.7099 18.43C22.7399 18.14 22.75 17.85 22.72 17.57L23.79 16.72C23.89 16.64 23.91 16.51 23.85 16.4L22.82 14.61C22.7599 14.5 22.63 14.46 22.51 14.5L21.2299 15C21 14.83 20.75 14.69 20.4799 14.58L20.28 13.22C20.26 13.09 20.16 13 20.03 13H17.9599C17.84 13 17.7299 13.09 17.7099 13.21L17.51 14.57C17.25 14.68 17 14.83 16.77 14.99L15.49 14.49C15.37 14.44 15.24 14.49 15.18 14.6L14.15 16.39C14.09 16.5 14.11 16.63 14.21 16.71L15.28 17.57C15.25 17.86 15.24 18.15 15.27 18.43L14.2 19.28C14.1 19.36 14.08 19.49 14.14 19.6L15.17 21.39C15.23 21.5 15.36 21.54 15.48 21.5L16.75 21C16.9799 21.17 17.2299 21.31 17.5 21.42L17.7 22.78C17.72 22.9 17.82 22.99 17.95 22.99H20.02C20.1399 22.99 20.25 22.9 20.27 22.78L20.4699 21.42C20.7299 21.31 20.9799 21.16 21.2099 21L22.49 21.5C22.61 21.55 22.7399 21.5 22.7999 21.39L23.83 19.6C23.89 19.49 23.87 19.36 23.77 19.28L22.7099 18.43ZM19 19.5C18.17 19.5 17.5 18.83 17.5 18C17.5 17.17 18.17 16.5 19 16.5C19.83 16.5 20.5 17.17 20.5 18C20.5 18.83 19.83 19.5 19 19.5Z" />
    </svg>
  )
}

export default AgentManagementIcon
