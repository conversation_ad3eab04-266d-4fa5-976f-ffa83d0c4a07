import { SVGAttributes } from 'react'

const ResendEmailIcon = (props: SVGAttributes<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      width="512"
      height="512"
      x="0"
      y="0"
      viewBox="0 0 36 36"
      {...props}
    >
      <g>
        <path
          d="M17 23H4c-.55 0-1-.45-1-1V8.17l12.48 7.68a.98.98 0 0 0 1.04 0L29 8.17V21c0 .55.45 1 1 1s1-.45 1-1V8c0-1.65-1.35-3-3-3H4C2.35 5 1 6.35 1 8v14c0 1.65 1.35 3 3 3h13c.55 0 1-.45 1-1s-.45-1-1-1zm-1-9.17L4.91 7h22.18zM35 28v2c0 .55-.45 1-1 1s-1-.45-1-1v-2c0-1.65-1.35-3-3-3h-8.15l2.77 2.22c.43.35.5.97.16 1.41-.2.25-.49.38-.78.38-.22 0-.44-.07-.62-.22l-4.02-3.22c-.48-.38-.75-.95-.75-1.56s.27-1.18.75-1.56l4.02-3.22c.43-.35 1.06-.27 1.41.16.34.43.27 1.06-.16 1.41l-2.77 2.22h8.15c2.76 0 5 2.24 5 5z"
          fill="#000000"
          opacity="1"
          data-original="#000000"
        ></path>
      </g>
    </svg>
  )
}

export default ResendEmailIcon
