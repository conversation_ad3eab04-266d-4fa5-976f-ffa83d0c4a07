export enum NOTIFICATION_TYPE {
  UserRequest = 'UserRequest',
  UserAcceptance = 'UserAcceptance',
  UserAccess = 'UserAccess',
  CameraStatus = 'CameraStatus',
  CameraAddedRemoved = 'CameraOnboardOffBoard',
  AgentStatus = 'AgentStatus',
  AgentAddedRemoved = 'AgentOnboardOffBoard',
  GroupAdded = 'GroupOnboard',
  GroupRemoved = 'GroupOffBoard',
  GroupMember = 'GroupMember',
  GroupMemberRole = 'GroupMemberRole',
  ProjectAdded = 'ProjectOnboard',
  ProjectRemoved = 'ProjectOffBoard',
  ProjectMember = 'ProjectMember',
  ProjectMemberRole = 'ProjectMemberRole',
  VAAdded = 'VAOnboard',
  VARemoved = 'VAOffBoard',
  VAStreamProcess = 'VAStreamProcess',
  RecordingSynchronization = 'RecordingSynchronization',
  RecordingUpload = 'RecordingUpload'
}

export enum MESSAGE_CONTENT {
  DEFAULT_ERROR = 'Something went wrong! Please try again',
  COPY_SUCCESS = 'Copied to clipboard!',
  COPY_ERROR = 'Error occurred when copy'
}
