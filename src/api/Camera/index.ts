import axiosConfig from '..'
import { IPagination } from '../../interfaces'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  CameraTags,
  ICameras,
  CreateCameraInput,
  UpdateCameraInput,
  ListCameraFilterParams
} from '../../models/camera'
import { paramsSerializer } from '../../utils'
import { CAMERA_ENDPOINTS } from '../Endpoints'

export const getListCamera = async (
  data: ListCameraFilterParams,
  signal?: AbortSignal
): Promise<IResponseDataWithPage<ICameras[]>> => {
  return axiosConfig.get(CAMERA_ENDPOINTS.LIST_CAMERA, {
    params: data,
    paramsSerializer: { serialize: paramsSerializer },
    signal
  })
}

export const addNewCamera = async (
  data: CreateCameraInput
): Promise<unknown> => {
  return axiosConfig.post(CAMERA_ENDPOINTS.LIST_CAMERA, data)
}
export const editCamera = async (
  data: UpdateCameraInput,
  id: string
): Promise<unknown> => {
  return axiosConfig.put(
    CAMERA_ENDPOINTS.EDIT_CAMERA.replace('${id}', id),
    data
  )
}

export const getCameraTags = async (
  data: IPagination
): Promise<IResponseDataWithPage<CameraTags[]>> => {
  return axiosConfig.get(CAMERA_ENDPOINTS.CAMERA_TAGS, {
    params: {
      'page-no': data['page-no'],
      'page-size': data['page-size']
    }
  })
}

export const previewCamera = async (
  id: string
): Promise<IResponseDataWithPage<{ playblack_stream_url: string }>> => {
  return axiosConfig.get(CAMERA_ENDPOINTS.PREVIEW.replace('${id}', id), {
    params: {
      protocol: 'HLS'
      // protocol: 'HTTP-FLV'
    }
  })
}

export const getCameraById = async (
  id: string
): Promise<IResponseData<ICameras>> => {
  return await axiosConfig.get(CAMERA_ENDPOINTS.LIST_CAMERA + '/' + id)
}

export const getCameraHeartbeat = async (): Promise<EventSource> => {
  return axiosConfig.get(CAMERA_ENDPOINTS.HEART_BEATS, { responseType: 'text' })
}

export const deleteCamera = async (id: string) => {
  return axiosConfig.delete(CAMERA_ENDPOINTS.LIST_CAMERA + '/' + id)
}
