import axiosConfig from '..'
import { IResponseData } from '../../models/apiResponse'
import {
  AddCameraDashboard,
  IDashboardCamera,
  SwapCameraDashboard
} from '../../models/dashboard'
import { DASHBOARD_ENDPOINT } from '../Endpoints'

export const getListDashboardCameras = async (): Promise<
  IResponseData<IDashboardCamera[]>
> => {
  return axiosConfig.get(DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS)
}

export const addCameraToDashboard = async (
  params: AddCameraDashboard
): Promise<unknown> => {
  return axiosConfig.post(DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS, params)
}

export const swapDashboardCamera = async (
  params: SwapCameraDashboard
): Promise<unknown> => {
  return axiosConfig.put(DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS, params)
}

export const removeCameraDashboard = async (
  grid_id: string
): Promise<unknown> => {
  return axiosConfig.delete(
    `${DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS}/${grid_id}`
  )
}
