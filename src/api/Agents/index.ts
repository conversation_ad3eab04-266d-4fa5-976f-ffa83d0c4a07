import axiosConfig from '..'
import { OPERATING_SYSTEM } from '../../enum/OperatingSystems'
import {
  AddAgentInputs,
  AgentBinaryFileResponse,
  CreateAgentRes,
  IAgent,
  ListAgentParams
} from '../../models/agents'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import { paramsSerializer } from '../../utils'
import { AGENTS_ENDPOINTS } from '../Endpoints'

export const getListAgents = async (
  data: ListAgentParams
): Promise<IResponseDataWithPage<IAgent[]>> => {
  return axiosConfig.get(AGENTS_ENDPOINTS.LIST_AGENTS, {
    params: data,
    paramsSerializer: paramsSerializer
  })
}

export const createAgent = async (
  params: AddAgentInputs
): Promise<IResponseData<CreateAgentRes>> => {
  return axiosConfig.post(AGENTS_ENDPOINTS.LIST_AGENTS, params)
}

export const deleteAgent = async (id: string) => {
  return axiosConfig.delete(AGENTS_ENDPOINTS.LIST_AGENTS + `/${id}`)
}

export const downloadBinaryFile = async (
  operating_system: OPERATING_SYSTEM
): Promise<IResponseData<AgentBinaryFileResponse>> => {
  return await axiosConfig.get(
    `${AGENTS_ENDPOINTS.AGENT_BINARY}/${operating_system}`
  )
}

export const uploadBinaryFile = async (
  data: FormData,
  operating_system: OPERATING_SYSTEM,
  onUpdateProgress: (progress: number) => void
) => {
  return await axiosConfig.post(
    `${AGENTS_ENDPOINTS.AGENT_BINARY}/${operating_system}`,
    data,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress(progressEvent) {
        onUpdateProgress((progressEvent.progress ?? 0) * 100)
      }
    }
  )
}
