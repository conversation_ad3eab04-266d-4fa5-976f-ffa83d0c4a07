import { Parser } from 'm3u8-parser'
import { SEGMENT, getErrorMessage } from '../../utils'
import { toast } from 'react-hot-toast'

async function parseHls({ hlsUrl = '' }) {
  try {
    const url = new URL(hlsUrl)

    const response = await fetch(url.href)
    if (!response.ok) throw new Error(await response.text())
    const manifest = await response.text()
    const parser = new Parser()
    parser.push(manifest)
    parser.end()

    let path = hlsUrl

    try {
      const pathBase = url.pathname.split('/')
      pathBase.pop()
      pathBase.push('{{URL}}')
      path = pathBase.join('/')
    } catch (perror) {
      toast.error(getErrorMessage(perror))
    }

    const base = url.origin + path

    if (parser.manifest.segments?.length) {
      let segments = parser.manifest.segments
      segments = segments.map((s) => ({
        ...s,
        uri: s.uri.startsWith('http') ? s.uri : base.replace('{{URL}}', s.uri)
      }))
      return {
        type: SEGMENT,
        data: segments
      }
    }
  } catch (error) {
    toast.error(getErrorMessage(error))
  }
}

export default parseHls
