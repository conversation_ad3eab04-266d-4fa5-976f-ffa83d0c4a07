import axios from 'axios'
import axiosConfig, { axiosConfigWithoutAuth } from '..'
import { KEY_STORAGE } from '../../enum/KeyStorage'
import { AssignVARecording } from '../../models/analytics'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  DownloadRecordingQuery,
  GetAzureUrlParams,
  GetPlaybackInput,
  ICommitRecordBlocks,
  IRecordings,
  ListRecordingFilterParams
} from '../../models/recodings'
import { paramsSerializer } from '../../utils'
import { RECORDING_ENDPOINT } from '../Endpoints'

export const getCameraRecording = (data: GetPlaybackInput): Promise<string> => {
  return axiosConfig.get(
    RECORDING_ENDPOINT.GET_CAMERA_RECORDING +
      `${data.recording_id}/${data.camera_id}/${data.selected_date}/${data.type}`
  )
}
export const getListRecordings = (
  data: ListRecordingFilterParams,
  signal?: AbortSignal
): Promise<IResponseDataWithPage<IRecordings[]>> => {
  return axiosConfig.get(RECORDING_ENDPOINT.LIST_RECORDING, {
    params: data,
    paramsSerializer: paramsSerializer,
    signal: signal
  })
}

export const assignVAToRecording = async (
  data: AssignVARecording
): Promise<IResponseData<IRecordings>> => {
  return axiosConfig.put(
    `${RECORDING_ENDPOINT.LIST_RECORDING}/${data.recording_id}`,
    {
      va_plugin_ids: data.va_plugin_ids.map((va) => va.id)
    }
  )
}

export const uploadToAzure = async (
  url: string,
  updateProgress: (progress: number) => void,
  abortController?: AbortController,
  file?: File | Blob
): Promise<unknown> => {
  const res = await axiosConfigWithoutAuth.put(url, file, {
    headers: {
      'Content-Type': 'application/octet-stream',
      'x-ms-version': '2020-04-08',
      'x-ms-blob-type': 'BlockBlob'
    },
    onUploadProgress: (progressEvent) => {
      updateProgress(progressEvent.loaded)
    },
    signal: abortController?.signal
  })

  return res
}

export const getAzureURLS = async (
  params: GetAzureUrlParams
): Promise<string[]> => {
  return await axiosConfig.post(RECORDING_ENDPOINT.BLOB_UPLOAD_URL, params)
}

export const commitRecordingBlocks = async (
  params: ICommitRecordBlocks
): Promise<IRecordings> => {
  return await axiosConfig.post(RECORDING_ENDPOINT.COMMIT_BLOCK_BLOBS, params)
}

export const getUploadedRecordings = async (
  recording_id: string
): Promise<string> => {
  return await axiosConfig.get(
    `${RECORDING_ENDPOINT.PLAYBACK_UPLOADED_RECORDINGS}${recording_id}`
  )
}

export const downloadRecording = async (
  updateProgress: (progress: number) => void,
  params: DownloadRecordingQuery,
  abortController?: AbortController
): Promise<BlobPart> => {
  return await axiosConfig.post(
    `${RECORDING_ENDPOINT.DOWNLOAD_RECORDING}`,
    params,
    {
      headers: {
        'Content-Type': 'application/zip'
      },
      responseType: 'blob',

      onDownloadProgress: (progress) => {
        console.log(progress)
        updateProgress(progress.loaded)
      },
      signal: abortController?.signal
    }
  )
}

export const deleteRecording = async (recordingIds: string[]) => {
  return await axiosConfig.delete(RECORDING_ENDPOINT.LIST_RECORDING, {
    data: { ids: recordingIds }
  })
}

export const getSASToken = async () => {
  const res: string = await axiosConfig.get(
    `${RECORDING_ENDPOINT.LIST_RECORDING}/thumbnail/token`
  )
  const blobStorageURL = res.split('/:id')[0]
  const sasToken = res.split('/:id')[1]

  localStorage.setItem(KEY_STORAGE.BLOB_STORAGE_URL, blobStorageURL)
  localStorage.setItem(KEY_STORAGE.SAS_TOKEN, sasToken)
}

export const getThumbNail = async (
  id: string,
  signal?: AbortSignal
): Promise<string> => {
  const blobStorageURL = localStorage.getItem(KEY_STORAGE.BLOB_STORAGE_URL)
  const sasToken = localStorage.getItem(KEY_STORAGE.SAS_TOKEN)
  await axios.get(`${blobStorageURL}/${id}.jpg${sasToken}`, {
    signal: signal
  })
  return `${blobStorageURL}/${id}.jpg${sasToken}`
}
