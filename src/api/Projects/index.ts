import axiosConfig from '..'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  EditProjectParams,
  IProject,
  IModifyUserParams,
  ListProjectQuerys,
  ProjectDetailFormInput
} from '../../models/projects'
import { PROJECTS_ENDPOINT } from '../Endpoints'

export const getListProjects = async (
  data: ListProjectQuerys
): Promise<IResponseDataWithPage<IProject[]>> => {
  return axiosConfig.get(PROJECTS_ENDPOINT.LIST_PROJECTS, { params: data })
}

export const deleteProject = async (project_id: string) => {
  return axiosConfig.delete(PROJECTS_ENDPOINT.LIST_PROJECTS + '/' + project_id)
}

export const createProject = async (
  data: ProjectDetailFormInput
): Promise<IResponseData<IProject>> => {
  return axiosConfig.post(PROJECTS_ENDPOINT.LIST_PROJECTS, data)
}

export const editProject = async (
  data: EditProjectParams
): Promise<IResponseData<IProject>> => {
  return axiosConfig.put(
    PROJECTS_ENDPOINT.LIST_PROJECTS + `/${data.id}`,
    data.data
  )
}

export const removeProjectUser = async (
  params: IModifyUserParams
): Promise<unknown> => {
  return axiosConfig.delete(
    `${PROJECTS_ENDPOINT.LIST_PROJECTS}/${params.project_id}/users`,
    params
  )
}

export const editProjectUserRole = async (params: IModifyUserParams) => {
  return axiosConfig.put(
    `${PROJECTS_ENDPOINT.LIST_PROJECTS}/${params.project_id}/users`,
    params.data
  )
}

export const addProjectUser = async (params: IModifyUserParams) => {
  return axiosConfig.post(
    `${PROJECTS_ENDPOINT.LIST_PROJECTS}/${params.project_id}/users`,
    params.data
  )
}
