export enum AUTH_ENDPOINTS {
  LOGIN_AZURE = '/authorize?provider=azure&scopes=email',
  LOGIN = '/token?grant_type=password',
  LOGOUT = '/logout?scope=local',
  REFRESH_TOKEN = '/token?grant_type=refresh_token',
  RECOVER = '/recover',
  VERIFY_OTP = '/verify',
  CREDENTIALS = '/user',
  OTP = '/otp',
  SIGN_UP = '/signup',
  REAUTHENTICATE = '/magiclink',
  REQUEST_ACCESS = '/signup/request',
  ACCEPT_REQUEST = '/signup/${id}/accept',
  REJECT_REQUEST = '/signup/${id}/reject',
  PENDING_REQUESTS = '/registrations',
  INVITE_USER = '/signup/invite'
}
export enum CAMERA_ENDPOINTS {
  LIST_CAMERA = '/cameras',
  EDIT_CAMERA = '/cameras/${id}',
  CAMERA_TAGS = '/cameras/tags',
  PREVIEW = '/cameras/${id}/playback',
  HEART_BEATS = '/events/heartbeat'
}

export enum AGENTS_ENDPOINTS {
  LIST_AGENTS = '/agents',
  GET_AGENT = '/agents/${id}',
  AGENTS_CAMERA = '/agents/cameras',
  AGENT_BINARY = '/systems/agent-binary'
}

export enum PROJECTS_ENDPOINT {
  LIST_PROJECTS = '/projects'
}

export enum GROUP_ENDPOINT {
  LIST_GROUP = '/groups',
  GROUP_USER = '/groups/${id}/users',

  UNAUTHORIZED_LIST_GROUP = '/groups/info' // only using in auth pages
}

export enum ROLES_ENDPOINT {
  LIST_ROLE = '/roles'
}

export enum ANALYTICS_ENDPOINT {
  LIST_ANALYTICS = '/analytics',
  LIST_STREAM = '/analytics/${id}/streams',
  ANALYTICS_EVENT = '/analytics/events/assignment/',
  EXPORT_ANALYTIC_EVENTS = 'analytics/export/'
}

export enum RECORDING_ENDPOINT {
  GET_CAMERA_RECORDING = '/recordings/playback/',
  LIST_RECORDING = '/recordings',
  BLOB_UPLOAD_URL = '/recordings/blob-upload-url',
  COMMIT_BLOCK_BLOBS = '/recordings/commit-block-list',
  PLAYBACK_UPLOADED_RECORDINGS = '/recordings/uploaded-video-playback/',
  DOWNLOAD_RECORDING = '/recordings/download'
}

export enum USERS_ENDPOINT {
  LIST_USERS = '/users'
}

export enum API_KEY_ENDPOINT {
  REVOKE_API_KEY = '/apikeys/${id}/revoke'
}

export enum SSE_HEARTBEAT {
  HEART_BEAT = '/sse/heartbeat',
  CAMERA_HEARTBEAT = 'camera-heartbeat',
  AGENT_HEARTBEAT = 'agent-heartbeat'
}

export enum DASHBOARD_ENDPOINT {
  DASHBOARD_CAMERAS = '/dashboard/cameras'
}

export enum NOTIFICATIONS_ENDPOINTS {
  LIST_NOTIFICATIONS = '/notifications',
  ACTION_NOTIFICATION_ALL = '/notifications/all',
  NOTIFICATION_SETTINGS = '/notifications/setting',
  WEBSOCKET_URL = '/notifications/websocket'
}

export enum SYSTEM_LOGS_ENDPOINT {
  LIST_SYSTEM_LOGS = '/logs',
  EXPORT_SYSTEM_LOGS = '/logs/export'
}

export enum RETENTION_ENDPOINTS {
  RETENTION_SETTING = '/settings/storage'
}
