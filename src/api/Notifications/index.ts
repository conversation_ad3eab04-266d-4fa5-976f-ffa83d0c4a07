import axiosConfig from '..'
import { IPagination } from '../../interfaces'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  INotificationRes,
  INotificationSettingRes,
  INotificationSettingsDetail
} from '../../models/notifications'
import { NOTIFICATIONS_ENDPOINTS } from '../Endpoints'

export const getWebSocketURL = async (): Promise<{ url: string }> => {
  return await axiosConfig.get(NOTIFICATIONS_ENDPOINTS.WEBSOCKET_URL)
}

export const getNotificationList = async (
  params: IPagination,
  signal?: AbortSignal
): Promise<IResponseDataWithPage<INotificationRes>> => {
  return await axiosConfig.get(NOTIFICATIONS_ENDPOINTS.LIST_NOTIFICATIONS, {
    params: params,
    signal: signal
  })
}

export const updateSeenNotifications = async (
  notification_ids: string[]
): Promise<unknown> => {
  return await axiosConfig.put(
    `${NOTIFICATIONS_ENDPOINTS.LIST_NOTIFICATIONS}`,
    {
      notification_ids: notification_ids
    }
  )
}

export const seenAllNotification = async (): Promise<unknown> => {
  return await axiosConfig.put(NOTIFICATIONS_ENDPOINTS.ACTION_NOTIFICATION_ALL)
}

export const getNotificationSettings = async (): Promise<
  IResponseData<INotificationSettingRes>
> => {
  return await axiosConfig.get(NOTIFICATIONS_ENDPOINTS.NOTIFICATION_SETTINGS)
}

export const updateNotificationSettings = async (
  params: Partial<INotificationSettingsDetail>
) => {
  return axiosConfig.put(NOTIFICATIONS_ENDPOINTS.NOTIFICATION_SETTINGS, params)
}

export const deleteNotifications = async (ids: string[]) => {
  return axiosConfig.delete(NOTIFICATIONS_ENDPOINTS.LIST_NOTIFICATIONS, {
    data: {
      notification_ids: ids
    }
  })
}

export const deleteNotificationAll = async () => {
  return axiosConfig.delete(NOTIFICATIONS_ENDPOINTS.ACTION_NOTIFICATION_ALL, {})
}
