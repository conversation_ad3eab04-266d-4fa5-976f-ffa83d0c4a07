import axiosConfig from '..'
import { IResponseDataWithPage } from '../../models/apiResponse'
import {
  IExportLogQuery,
  ISystemLog,
  ISystemLogQuery
} from '../../models/systemLogs'
import { paramsSerializer } from '../../utils'
import { SYSTEM_LOGS_ENDPOINT } from '../Endpoints'

export const getSystemLogsList = async (
  params: ISystemLogQuery
): Promise<IResponseDataWithPage<ISystemLog[]>> => {
  return await axiosConfig.get(SYSTEM_LOGS_ENDPOINT.LIST_SYSTEM_LOGS, {
    params: params,
    paramsSerializer: paramsSerializer
  })
}

export const exportSystemLogs = async (
  params: IExportLogQuery
): Promise<string> => {
  return await axiosConfig.get(SYSTEM_LOGS_ENDPOINT.EXPORT_SYSTEM_LOGS, {
    params: params,
    paramsSerializer: paramsSerializer,
    headers: {
      'Content-Type': 'application/octet-stream'
    }
  })
}
