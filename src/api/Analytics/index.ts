import axiosConfig from '..'
import { IPagination } from '../../interfaces'
import {
  AddVAInputs,
  Analytics_Streams,
  IAnalyticResultsQuerys,
  IAnalytics,
  IAnalyticsResults
} from '../../models/analytics'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import { paramsSerializer } from '../../utils'
import { ANALYTICS_ENDPOINT } from '../Endpoints'

export const getListAnalytics = async (
  data: IPagination
): Promise<IResponseDataWithPage<IAnalytics[]>> => {
  return axiosConfig.get(ANALYTICS_ENDPOINT.LIST_ANALYTICS, { params: data })
}

export const getVAById = async (
  id: string
): Promise<IResponseData<IAnalytics>> => {
  return axiosConfig.get(ANALYTICS_ENDPOINT.LIST_ANALYTICS + `/${id}`)
}

export const createVA = async (
  data: AddVAInputs
): Promise<IResponseData<IAnalytics>> => {
  return axiosConfig.post(ANALYTICS_ENDPOINT.LIST_ANALYTICS, data)
}

export const deleteVA = async (analytic_id: string) => {
  return await axiosConfig.delete(
    ANALYTICS_ENDPOINT.LIST_ANALYTICS + `/${analytic_id}`
  )
}

export const getAnalyticStreams = async (
  api_key: string,
  analytic_id: string = ''
): Promise<IResponseData<Analytics_Streams[]>> => {
  return axiosConfig.get(
    ANALYTICS_ENDPOINT.LIST_STREAM.replace('${id}', analytic_id),
    {
      headers: {
        'x-api-key': api_key
        // ""
      }
    }
  )
}

export const getAnalyticsResult = async (
  assignment_id: string = '',
  params: IAnalyticResultsQuerys,
  abortController?: AbortController
): Promise<IResponseDataWithPage<IAnalyticsResults[]>> => {
  return axiosConfig.get(
    `${ANALYTICS_ENDPOINT.ANALYTICS_EVENT}${assignment_id}`,
    {
      params: params,
      signal: abortController?.signal,
      paramsSerializer
    }
  )
}

export const exportAnalyticResults = async (
  assignment_id: string = '',
  params: IAnalyticResultsQuerys
): Promise<BlobPart> => {
  return await axiosConfig.get(
    `${ANALYTICS_ENDPOINT.EXPORT_ANALYTIC_EVENTS}${assignment_id}`,
    {
      headers: {
        'Content-Type': 'application/octet-stream'
      },
      responseType: 'blob',
      params: {
        ...params,
        'page-no': 0,
        'page-size': 0
      },
      paramsSerializer: paramsSerializer
    }
  )
}

export const getProcessedStream = async (
  streamId: string
): Promise<IResponseData<{ sink_url: string }>> => {
  return await axiosConfig.get(
    `${ANALYTICS_ENDPOINT.LIST_ANALYTICS}/assignments/${streamId}/live`
  )
}
