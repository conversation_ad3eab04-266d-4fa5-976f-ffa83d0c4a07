import axiosConfig from '..'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  CreateGroupInput,
  EditGroupAPIParams,
  IGroupQuery,
  IGroupUserModifyParams,
  IGroup<PERSON>serRoleUpdateParams,
  ListGroup
} from '../../models/groups'
import { GROUP_ENDPOINT } from '../Endpoints'

export const getUnauthorizedGroupList = async (
  data: IGroupQuery
): Promise<IResponseDataWithPage<ListGroup[]>> => {
  return await axiosConfig.get(GROUP_ENDPOINT.UNAUTHORIZED_LIST_GROUP, {
    params: data
  })
}

export const getAuthorizedGroupList = async (
  data: IGroupQuery
): Promise<IResponseDataWithPage<ListGroup[]>> => {
  return axiosConfig.get(GROUP_ENDPOINT.LIST_GROUP, { params: data })
}

export const getGroupDetail = async (
  group_id: string
): Promise<IResponseData<ListGroup>> => {
  return await axiosConfig.get(`${GROUP_ENDPOINT.LIST_GROUP}/${group_id}`)
}

export const createNewGroup = async (
  data: CreateGroupInput
): Promise<unknown> => {
  return await axiosConfig.post(GROUP_ENDPOINT.LIST_GROUP, data)
}

export const editGroup = async (data: EditGroupAPIParams) => {
  return await axiosConfig.put(
    `${GROUP_ENDPOINT.LIST_GROUP}/${data.group_id}`,
    data.data
  )
}

export const deleteGroup = async (group_id: string) => {
  return await axiosConfig.delete(`${GROUP_ENDPOINT.LIST_GROUP}/${group_id}`)
}

export const updateGroupUsers = async (
  data: IGroupUserRoleUpdateParams
): Promise<unknown> => {
  return await axiosConfig.put(
    GROUP_ENDPOINT.GROUP_USER.replace('${id}', data.group_id),
    data.data
  )
}

export const removeGroupUser = async (data: IGroupUserModifyParams) => {
  return await axiosConfig.delete(
    GROUP_ENDPOINT.GROUP_USER.replace('${id}', data.group_id),
    data
  )
}

export const addGroupUser = async (data: IGroupUserModifyParams) => {
  return await axiosConfig.post(
    GROUP_ENDPOINT.GROUP_USER.replace('${id}', data.group_id),
    data.data
  )
}
