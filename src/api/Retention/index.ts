import axiosConfig from '..'
import { IResponseData } from '../../models/apiResponse'
import { IRetentionSetting, IUpdateRetention } from '../../models/retention'
import { RETENTION_ENDPOINTS } from '../Endpoints'

export const getRetentionPeriod = async (): Promise<
  IResponseData<IRetentionSetting>
> => {
  return await axiosConfig.get(RETENTION_ENDPOINTS.RETENTION_SETTING)
}

export const updateRetentionPeriod = async (
  params: IUpdateRetention,
  controller?: AbortController
) => {
  return await axiosConfig.put(RETENTION_ENDPOINTS.RETENTION_SETTING, params, {
    signal: controller?.signal
  })
}
