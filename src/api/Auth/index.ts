import axiosConfig, { axiosSupabaseConfig } from '..'
import { EmailPasswordAuthenication } from '../../interfaces'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  AuthVerifyInput,
  IPendingRequestsQuery,
  IPendingUsers,
  InvitationSignup,
  InviteUserParams,
  RequestAccessParams,
  ResendOTPParams,
  UserCredential,
  UserCredentialDetail
} from '../../models/auth'
import { paramsSerializer } from '../../utils'
import { AUTH_ENDPOINTS } from '../Endpoints'

export const supabaseLogin = async (
  data: EmailPasswordAuthenication
): Promise<UserCredential> => {
  return axiosSupabaseConfig.post(AUTH_ENDPOINTS.LOGIN, data)
}

export const supabaseLogout = async (token: string): Promise<unknown> => {
  return axiosSupabaseConfig.post(AUTH_ENDPOINTS.LOGOUT, null, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  })
}

export const supabaseRefreshToken = (
  refreshToken: string
): Promise<UserCredential> => {
  const data = {
    refresh_token: refreshToken
  }
  return axiosSupabaseConfig.post(AUTH_ENDPOINTS.REFRESH_TOKEN, data)
}

export const verifyOTP = async (
  data: AuthVerifyInput
): Promise<UserCredential> => {
  return axiosSupabaseConfig.post(AUTH_ENDPOINTS.VERIFY_OTP, data)
}

export const editUserCredential = async (
  data: EmailPasswordAuthenication,
  token: string
): Promise<unknown> => {
  return axiosSupabaseConfig.put(AUTH_ENDPOINTS.CREDENTIALS, data, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  })
}

export const resendOTP = async (data: ResendOTPParams): Promise<unknown> => {
  return axiosSupabaseConfig.post(AUTH_ENDPOINTS.OTP, data)
}

export const supabaseSignup = async (
  data: InvitationSignup
): Promise<IResponseData<UserCredential>> => {
  return axiosConfig.post(AUTH_ENDPOINTS.SIGN_UP, data)
}

export const accountRecover = async (data: {
  email: string
}): Promise<unknown> => {
  return axiosSupabaseConfig.post(AUTH_ENDPOINTS.RECOVER, data)
}

export const supabaseReauthenticate = async (data: {
  email: string
}): Promise<unknown> => {
  return axiosSupabaseConfig.post(AUTH_ENDPOINTS.REAUTHENTICATE, data)
}

export const requestAccess = async (
  data: RequestAccessParams
): Promise<unknown> => {
  return axiosConfig.post(AUTH_ENDPOINTS.REQUEST_ACCESS, data)
}

export const getListPendingRequests = async (
  params: IPendingRequestsQuery
): Promise<IResponseDataWithPage<IPendingUsers[]>> => {
  return axiosConfig.get(AUTH_ENDPOINTS.PENDING_REQUESTS, {
    params: params,
    paramsSerializer: { serialize: paramsSerializer }
  })
}

export const acceptPendingRequest = async (
  registration_id: string
): Promise<unknown> => {
  return axiosConfig.post(
    AUTH_ENDPOINTS.ACCEPT_REQUEST.replace('${id}', registration_id)
  )
}

export const rejectPendingRequest = async (
  registration_id: string
): Promise<unknown> => {
  return axiosConfig.post(
    AUTH_ENDPOINTS.REJECT_REQUEST.replace('${id}', registration_id)
  )
}

export const inviteUser = async (data: InviteUserParams): Promise<unknown> => {
  return axiosConfig.post(AUTH_ENDPOINTS.INVITE_USER, data)
}

export const getSupabaseUser = async (
  token: string
): Promise<UserCredentialDetail> => {
  return axiosSupabaseConfig.get(AUTH_ENDPOINTS.CREDENTIALS, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  })
}
