import axios, { AxiosError, AxiosResponse } from 'axios'
import { KEY_STORAGE } from '../enum/KeyStorage'
import { UserCredential } from '../models/auth'
import { supabaseRefreshToken } from './Auth'
import cookie from 'js-cookie'
import { COOKIE_EXPIRE_TIME } from '../utils'

const axiosConfig = axios.create({
  baseURL: import.meta.env.VITE_API_ENDPOINT,
  headers: {
    'Content-Type': 'application/json'
  }
})
// Add a request interceptor
axiosConfig.interceptors.request.use(
  function (config) {
    const accessToken = cookie.get(KEY_STORAGE.ACCESS_TOKEN)
    if (accessToken) {
      config.headers['Authorization'] = `Bearer ${accessToken}`
    }
    // Do something before request is sent
    return config
  },
  function (error) {
    // Do something with request error
    return Promise.reject(error)
  }
)
// Add a response interceptor
axiosConfig.interceptors.response.use(
  function (response: AxiosResponse) {
    return response.data
  },
  async function (error) {
    if (!error.response) {
      return Promise.reject(error)
    }
    switch (error.response.status) {
      case 403:
        break
      // return await handleRenewToken(error)
      case 404:
        break
      case 401:
        // Refetch token with access token
        return await handleRenewToken(error)
      case 500:
        break
      default:
        break
    }

    return Promise.reject(error)
  }
)

export const axiosConfigWithoutAuth = axios.create({
  baseURL: import.meta.env.VITE_API_ENDPOINT,
  headers: {
    'Content-Type': 'application/json'
  }
})

export const axiosSupabaseConfig = axios.create({
  baseURL: import.meta.env.VITE_SUPABASE_ENDPOINT,
  headers: {
    'Content-Type': 'application/json'
  }
})

axiosSupabaseConfig.interceptors.response.use(
  function (response: AxiosResponse) {
    return response.data
  },
  async function (error) {
    return Promise.reject(error)
  }
)

const renewToken = async () => {
  const refreshToken = cookie.get(KEY_STORAGE.REFRESH_TOKEN) ?? ''

  const res: UserCredential = await supabaseRefreshToken(refreshToken)
  cookie.set(KEY_STORAGE.ACCESS_TOKEN, res.access_token, {
    expires: new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
  })
  cookie.set(KEY_STORAGE.REFRESH_TOKEN, res.refresh_token, {
    expires: new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
  })

  return res.access_token
}

const handleRenewToken = async (error: unknown) => {
  if (!(error instanceof AxiosError)) {
    return
  }
  const originalRequest = error.config
  if (!originalRequest) {
    return
  }
  const newAccessToken = await renewToken()
  originalRequest.headers.Authorization = `Bearer ${newAccessToken}`
  return await axiosConfig(originalRequest)
}

export default axiosConfig
