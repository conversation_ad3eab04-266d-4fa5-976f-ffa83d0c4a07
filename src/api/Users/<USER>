import axiosConfig from '..'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import { ICurrentUser, IUserQuery } from '../../models/users'
import { USERS_ENDPOINT } from '../Endpoints'

export const getUsersList = async (
  data: IUserQuery,
  abortController?: AbortController
): Promise<IResponseDataWithPage<ICurrentUser[]>> => {
  return axiosConfig.get(USERS_ENDPOINT.LIST_USERS, {
    params: data,
    signal: abortController?.signal
  })
}

export const getUserById = async (
  user_id: string
): Promise<IResponseData<ICurrentUser>> => {
  return await axiosConfig.get(`${USERS_ENDPOINT.LIST_USERS}/${user_id}`)
}

export const removeUser = async (userID: string) => {
  return await axiosConfig.delete(`${USERS_ENDPOINT.LIST_USERS}/${userID}`)
}
