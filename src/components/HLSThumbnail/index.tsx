import { useState, useEffect, useRef } from 'react'
import { createFFmpeg } from '@ffmpeg/ffmpeg'
import { Skeleton } from 'antd'
type Props = {
  segmentUrl: string
}
const HLSSegmentThumbnailWithFFmpeg = ({ segmentUrl }: Props) => {
  const [thumbnail, setThumbnail] = useState<string>('')
  const abortController = useRef<AbortController>()
  // Initialize FFmpeg
  const ffmpeg = createFFmpeg({ log: true }) // Set log: true for debugging

  const generateThumbnail = async () => {
    abortController.current = new AbortController()
    try {
      // Load FFmpeg if not already loaded
      if (!ffmpeg.isLoaded()) {
        await ffmpeg.load()
      }

      // Step 2: Fetch the .ts segment
      const tsResponse = await fetch(segmentUrl, {
        signal: abortController.current.signal
      })
      const arrayBuffer = await tsResponse.arrayBuffer()

      // Step 5: Convert to Uint8Array
      const uint8Array = new Uint8Array(arrayBuffer)
      // Step 3: Write the segment to FFmpeg's filesystem
      ffmpeg.FS('writeFile', 'input.ts', uint8Array)

      // Step 4: Extract thumbnail from the .ts segment
      await ffmpeg.run(
        '-i',
        'input.ts',
        '-ss',
        String(0), // Seek within the segment
        '-frames:v',
        '1', // Extract 1 frame
        '-q:v',
        '2', // Quality setting
        'output.jpg' // Output file
      )

      // Step 5: Read and display the thumbnail
      const outputData = ffmpeg.FS('readFile', 'output.jpg')
      const blob = new Blob([outputData.buffer], { type: 'image/jpeg' })
      const url = URL.createObjectURL(blob)
      setThumbnail(url)
    } catch (error) {
      setThumbnail('')
      console.error('Error generating thumbnail:', error)
    }
  }

  useEffect(() => {
    const timeOut = setTimeout(() => generateThumbnail(), 500)
    // Clean up FFmpeg instance on unmount (optional)
    return () => {
      clearTimeout(timeOut)
      setThumbnail('')
      abortController.current?.abort()
    }
  }, [segmentUrl])

  useEffect(() => {
    // Clean up FFmpeg instance on unmount (optional)
    return () => {
      if (ffmpeg.isLoaded()) {
        ffmpeg.exit()
      }
    }
  }, [])
  if (thumbnail === '') {
    return <Skeleton.Image className="min-w-full min-h-[300px]" active />
  }
  return (
    <div>
      <div>
        <img src={thumbnail} alt="HLS Thumbnail" className="w-full" />
      </div>
    </div>
  )
}

export default HLSSegmentThumbnailWithFFmpeg
