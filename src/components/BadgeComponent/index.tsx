import { classNames } from '../../utils'

type Props = {
  text: string
  className?: string
  textStyle?: string
}

const BadgeComponent = ({ text, className, textStyle }: Props) => {
  return (
    <div
      className={classNames(
        className && className,
        'bg-transparent capitalize w-fit items-center rounded-[4px] sm:bg-gray-100 px-4 py-1 text-xs font-medium text-gray-600'
      )}
    >
      <span className={classNames(textStyle && textStyle, 'text-gray-600')}>
        {text}
      </span>
    </div>
  )
}

export default BadgeComponent
