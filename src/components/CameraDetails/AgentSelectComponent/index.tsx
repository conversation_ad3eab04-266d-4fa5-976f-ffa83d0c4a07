import { Control, FieldErrors, UseFormSetValue } from 'react-hook-form'
import { IAgent, ListAgentParams } from '../../../models/agents'
import { DEFAULT_PAGE_SIZE, ENV } from '../../../utils'
import { ICameraDetail, ICameras } from '../../../models/camera'
import { useState } from 'react'
import { getListAgents } from '../../../api/Agents'
import FormInputContainer from '../../FormInputComponent'
import LazyScrollSelectComponent from '../../LazyScrollSelect'
import useLazyScroll from '../../../utils/hooks/useLazyScroll'

type Props = {
  errors?: FieldErrors | undefined
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<ICameraDetail, any>
  defaultAgent?: IAgent
  defaultValue?: ICameras
  setValue: UseFormSetValue<ICameraDetail>
}

const AgentSelectComponent = ({
  errors,
  control,
  setValue,
  defaultValue,
  defaultAgent
}: Props) => {
  const [listAgents, setListAgent] = useState<IAgent[]>([])
  const [searchAgent, setSearchAgent] = useState<string>('')
  const [agentPage, setAgentPage] = useState<number>(1)
  const [isFetchingMoreAgent, setIsFetchingMoreAgent] = useState<boolean>(false)
  const {
    markAsMounted,
    noMoreData,
    fetchMoreFunc: fetchMoreAgentList
  } = useLazyScroll({
    currentPage: agentPage,
    searchText: searchAgent,
    fetchDataFunc: () => fetchListAgents(),
    onPageChange: (page) => setAgentPage(page)
  })

  const fetchListAgents = async () => {
    const data: ListAgentParams = {
      'page-no': agentPage,
      'page-size': DEFAULT_PAGE_SIZE,
      name: searchAgent.trim()
    }
    setIsFetchingMoreAgent(true)
    try {
      const res = await getListAgents(data)
      const totalPages = Math.floor(res.total / DEFAULT_PAGE_SIZE)
      setListAgent((prev) =>
        agentPage === 1 ? res.data : prev.concat(res.data)
      )
      if (agentPage > Math.floor(totalPages)) {
        // if the current page is the last page
        noMoreData()
      }
      markAsMounted()
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    } finally {
      setIsFetchingMoreAgent(false)
    }
  }

  const onSelectAgent = (
    option: string,
    onChange: (...event: string[]) => void
  ) => {
    onChange(option)
    const selectedAgent = listAgents.find((agent) => agent.id === option)
    if (selectedAgent) {
      setValue('projects', {
        label: selectedAgent?.project?.name ?? '',
        value: selectedAgent?.project?.id ?? ''
      })
    }
  }

  return (
    <FormInputContainer<ICameraDetail>
      label={'Agent'}
      name={'agent'}
      control={control}
      vertialAlign
      errors={errors}
      required
      rules={{ required: 'Required' }}
      render={({ field }) => (
        <LazyScrollSelectComponent<ICameraDetail>
          field={field}
          options={listAgents.map((agent) => ({
            label: agent.name,
            value: agent.id
          }))}
          disabled={Boolean(defaultValue || defaultAgent)}
          onChange={(e) => onSelectAgent(e, field.onChange)}
          fetchMoreData={fetchMoreAgentList}
          onSearch={(e) => setSearchAgent(e.target.value)}
          isFetchMore={isFetchingMoreAgent}
        />
      )}
    />
  )
}

export default AgentSelectComponent
