import { Control, FieldErrors } from 'react-hook-form'
import { ICameraDetail } from '../../../models/camera'
import FormInputContainer from '../../FormInputComponent'
import TextInputComponent from '../../TextInputComponent'
import { IOption } from '../../../interfaces'

type Props = {
  errors: FieldErrors<ICameraDetail>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<ICameraDetail, any>
  selectedProject: IOption
}

const ProjectSelectComponent = ({
  errors,
  control,
  selectedProject
}: Props) => {
  return (
    <FormInputContainer<ICameraDetail>
      label={'Project name'}
      name={'projects'}
      control={control}
      vertialAlign
      errors={errors}
      required
      rules={{ required: 'Required' }}
      render={({ field }) => (
        <TextInputComponent
          defaultValue={selectedProject?.label ?? ''}
          disabled
          placeholder="Select an agent to assign a project"
          onChange={(e) => field.onChange(e.target.value)}
        />
      )}
    />
  )
}

export default ProjectSelectComponent
