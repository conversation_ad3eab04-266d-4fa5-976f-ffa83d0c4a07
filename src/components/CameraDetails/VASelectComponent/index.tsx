import { useEffect, useMemo, useState } from 'react'
import { IAnalytics } from '../../../models/analytics'
import { IOption, IPagination } from '../../../interfaces'
import { getListAnalytics } from '../../../api/Analytics'
import { Select, Tag } from 'antd'
import TextInputComponent from '../../TextInputComponent'
import SearchIcon from '../../../assets/svgs/SearchIcon'
import { ENV } from '../../../utils'

type Props = {
  onChange: (item: IOption[]) => void
  selectedValue: IOption[]
}

const VASelectComponent = ({ onChange, selectedValue }: Props) => {
  const [listVA, setListVA] = useState<IAnalytics[]>([])
  const [searchText, setSearchText] = useState<string>('')
  const fetchListVAs = async () => {
    const data: IPagination = {
      'page-no': 1,
      'page-size': 50
    }
    try {
      const res = await getListAnalytics(data)
      setListVA(res.data.filter((va) => !va.api_keys?.[0].revoked))
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    }
  }

  const listVASelect = useMemo(
    () =>
      listVA
        .filter((analytic) =>
          analytic.name
            .toLocaleLowerCase()
            .includes(searchText.toLocaleLowerCase())
        )
        .map((va) => ({
          label: va.name,
          value: va.id
        })),
    [listVA, searchText]
  )

  useEffect(() => {
    fetchListVAs()
  }, [])
  return (
    <Select
      className="w-full h-9"
      options={listVASelect}
      labelInValue
      mode="multiple"
      placeholder="Select VA Plugins"
      value={selectedValue ?? []}
      onChange={onChange}
      showSearch={false}
      maxTagTextLength={15}
      maxTagCount={'responsive'}
      dropdownRender={(menu) => (
        <div className="w-full flex flex-col gap-2 px-3 py-4">
          <TextInputComponent
            prefixIcon={<SearchIcon />}
            onChange={(e) => setSearchText(e.target.value)}
            placeholder="Search plugins"
          />
          {menu}
        </div>
      )}
      tagRender={({ label, closable, onClose }) => (
        <Tag
          color="#EFF6FF"
          closable={closable}
          onClose={onClose}
          className="!text-black"
        >
          {label}
        </Tag>
      )}
    />
  )
}

export default VASelectComponent
