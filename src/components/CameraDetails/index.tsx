import { useForm } from 'react-hook-form'
import { useEffect, useState } from 'react'
import { LoaderIcon, toast } from 'react-hot-toast'
import { addNewCamera, editCamera, previewCamera } from '../../api/Camera'
import {
  CreateCameraInput,
  ICameraDetail,
  ICameras,
  UpdateCameraInput
} from '../../models/camera'
import { ENV, getErrorMessage } from '../../utils'
import PrimaryButton from '../PrimaryButtons'
import SecondaryButton from '../SecondaryButtons'
import { IOption } from '../../interfaces'
import { IAgent } from '../../models/agents'
import { useLocation } from 'react-router-dom'
import { ROUTE_PATH } from '../../enum/RoutePath'
import {
  REGEX_CAMERA_NAME,
  REGEX_PREVENT_ONLY_WHITESPACES
} from '../../enum/Regex'
import AgentSelectComponent from './AgentSelectComponent'
import TagSelectComponent from './TagSelectComponent'
import ProjectSelectComponent from './ProjectSelectComponent'
import HlsPlayer from '../HLSPlayer'
import TextInputComponent from '../TextInputComponent'
import { Switch } from '@headlessui/react'
import FormInputContainer from '../FormInputComponent'
import ConfirmModal from '../ConfirmModal'

type Props = {
  defaultValue?: ICameras
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  refetchListCamera?: (page?: number) => void
  defaultAgent?: IAgent
  refetchAgentCamera?: VoidFunction // refetch camera list when edited via agent management
  modalOpen?: boolean
  isFormDirty: boolean
  setIsFormDirty: React.Dispatch<React.SetStateAction<boolean>>
}

const CameraDetailComponent = ({
  isFormDirty,
  setIsFormDirty,
  defaultValue,
  setModalOpen,
  refetchListCamera = () => {},
  defaultAgent, // when user create camera in agent Management
  refetchAgentCamera = () => {} // refetch camera list when edited via agent management
}: Props) => {
  const location = useLocation()
  const [previewUrl, setPreviewUrl] = useState<string>('')
  const [toBeAssignedTags, setToBeAssignedTags] = useState<IOption[]>([])
  const [unassignedTags, setUnassignedTags] = useState<string[]>([])
  const [confirmCloseModal, setConfirmCloseModal] = useState<boolean>(false)
  const {
    control,
    formState: { errors, isSubmitting, isValid, isDirty },
    handleSubmit,
    setValue,
    watch
  } = useForm<ICameraDetail>({
    mode: 'onChange',
    defaultValues: defaultValue
      ? {
          // case where editing camera in camera management page
          name: defaultValue?.name,
          notes: defaultValue?.notes,
          projects: {
            label: defaultValue?.project?.name ?? '',
            value: defaultValue?.project?.id ?? ''
          },
          rtsp: defaultValue?.rtsp,
          tags: defaultValue?.tags.map((tag) => ({
            label: tag.name,
            value: tag.id
          })),
          agent: defaultValue.agent?.name ?? '',
          va_plugins: defaultValue?.va_plugins?.map((va) => ({
            label: va.name,
            value: va.id
          })),
          recording: defaultValue?.recording
        }
      : defaultAgent
      ? {
          //case when edit/create camera in agent management page
          agent: defaultAgent.name ?? '',
          projects: {
            label: defaultAgent.project.name ?? '',
            value: defaultAgent.project.id ?? ''
          },
          name: undefined,
          rtsp: undefined,
          va_plugins: undefined,
          notes: undefined
        }
      : {
          name: undefined,
          rtsp: undefined,
          agent: undefined,
          va_plugins: undefined,
          projects: undefined,
          notes: undefined
        }
  })
  const cameraNameValue = watch('name')
  const rtspValue = watch('rtsp')
  const descriptionValue = watch('notes')
  const selectedProject = watch('projects')

  const handleCreateCamera = async (data: ICameraDetail) => {
    const createCameraInput: CreateCameraInput = {
      agent_id: defaultAgent?.id ?? defaultValue?.agent.id ?? data.agent,
      name: data.name.trim(),
      notes: data.notes,
      project_id: data.projects?.value ?? '',
      rtsp: data.rtsp.trim(),
      tags: toBeAssignedTags.map((tag) => tag.label),
      va_plugin_ids: data.va_plugins?.map((va) => va?.value) ?? []
    }
    try {
      await addNewCamera(createCameraInput)
      toast.success('New camera is added successfully!', {
        position: 'top-right'
      })
      refetchListCamera()
      setModalOpen(false)
    } catch (error) {
      toast.error(getErrorMessage(error), { position: 'top-right' })
    }
  }

  const handleEditCamera = async (data: ICameraDetail) => {
    const updateCameraData: UpdateCameraInput = {
      agent_id: defaultAgent?.id ?? defaultValue?.agent.id ?? '',
      name: data.name.trim(),
      project_id: data.projects?.value ?? '',
      notes: data.notes,
      remove_tag_ids: unassignedTags.length > 0 ? unassignedTags : undefined,
      remove_va_plugin_ids: undefined,
      rtsp: data.rtsp.trim(),
      tags:
        toBeAssignedTags.length > 0
          ? toBeAssignedTags.map((tag) => tag.label)
          : undefined,
      va_plugin_ids: undefined,
      recording: data.recording ? 'On' : 'Off'
    }
    try {
      await editCamera(updateCameraData, defaultValue?.id ?? '')
      toast.success('Your changes are saved!', {
        position: 'top-right'
      })
      refetchListCamera()
      if (location.pathname === ROUTE_PATH.Agents) {
        //refetch camera list when edited via agent management
        refetchAgentCamera()
      }
      setModalOpen(false)
    } catch (error) {
      if (
        error &&
        typeof error === 'object' &&
        'status' in error &&
        error?.status === 403
      ) {
        toast.error('You do not have permission on this camera')
      } else {
        toast.error(getErrorMessage(error), { position: 'top-right' })
      }
    }
  }

  const onSubmit = async (data: ICameraDetail) =>
    defaultValue ? await handleEditCamera(data) : await handleCreateCamera(data)
  const fetchCameraPreview = async () => {
    try {
      const res = await previewCamera(defaultValue?.id ?? '')
      setPreviewUrl(res.data.playblack_stream_url)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
      toast.error('Error Preview Camera stream, please try again!', {
        position: 'top-right'
      })
    }
  }

  useEffect(() => {
    if (defaultValue) {
      fetchCameraPreview()
    }
  }, [])

  useEffect(() => {
    setIsFormDirty(
      isDirty || unassignedTags.length > 0 || toBeAssignedTags.length > 0
    )
  }, [isDirty, unassignedTags, toBeAssignedTags])

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="max-w-full relative h-full flex flex-col gap-[10px]"
    >
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <FormInputContainer<ICameraDetail>
          label={'Camera name'}
          name={'name'}
          control={control}
          vertialAlign
          errors={errors}
          required
          maxCharactersText={`${cameraNameValue?.length ?? 0}/50 characters`}
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_CAMERA_NAME,
              message:
                'Camera name cannot contain only spaces and the symbol "/"'
            }
          }}
          render={({ field }) => (
            <TextInputComponent
              disabled={defaultValue?.status.toLocaleLowerCase() === 'online'}
              defaultValue={defaultValue?.name}
              title={
                defaultValue?.status.toLocaleLowerCase() === 'online'
                  ? 'Can not be edited while online'
                  : ''
              }
              maxLength={50}
              onChange={(e) => field.onChange(e.target.value)}
              placeholder="Enter your camera's name"
            />
          )}
        />
        <FormInputContainer<ICameraDetail>
          label={'RTSP'}
          name={'rtsp'}
          control={control}
          vertialAlign
          maxCharactersText={`${rtspValue?.length ?? 0}/150 characters`}
          errors={errors}
          required
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_PREVENT_ONLY_WHITESPACES,
              message: 'RTSP cannot contain only spaces'
            }
          }}
          render={({ field }) => (
            <TextInputComponent
              maxLength={150}
              defaultValue={defaultValue?.rtsp}
              onChange={(e) => field.onChange(e.target.value)}
              placeholder="Enter your camera's rtsp"
            />
          )}
        />
      </div>
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex-1 max-w-[49%]">
          <AgentSelectComponent
            errors={errors}
            control={control}
            defaultAgent={defaultAgent}
            defaultValue={defaultValue}
            setValue={setValue}
          />
        </div>

        <ProjectSelectComponent
          selectedProject={selectedProject}
          errors={errors}
          control={control}
        />
      </div>
      <TagSelectComponent
        defaultValue={defaultValue}
        toBeAssignedTagsState={[toBeAssignedTags, setToBeAssignedTags]}
        unassignedTagsState={[unassignedTags, setUnassignedTags]}
      />

      <FormInputContainer
        control={control}
        errors={errors}
        label={'Notes'}
        name={'notes'}
        maxCharactersText={`${descriptionValue?.length ?? 0}/250 characters`}
        vertialAlign
        render={({ field }) => (
          <textarea
            rows={4}
            maxLength={250}
            placeholder="Enter your notes"
            className="block w-full bg-[#F3F4F6] rounded-md border-0 py-1.5 text-mainBlack shadow-sm ring-0 focus:ring-0 placeholder:text-gray-400 sm:text-sm sm:leading-6"
            defaultValue={defaultValue?.notes}
            onChange={(e) => field.onChange(e.target.value)}
          />
        )}
      />

      {defaultValue && (
        <>
          <div className="flex items-center gap-4 justify-between w-full">
            <p className="text-sm flex-1 font-semibold">Camera Preview</p>
            <div className="w-[4 0%] flex">
              <FormInputContainer
                control={control}
                errors={errors}
                name={'recording'}
                label="Records stream"
                render={({ field }) => (
                  <Switch
                    onChange={field.onChange}
                    defaultChecked={defaultValue.recording === 'On'}
                    className="group relative items-center flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-solid border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 data-[checked]:bg-primary"
                  >
                    <span
                      aria-hidden="true"
                      className="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out group-data-[checked]:translate-x-5"
                    />
                  </Switch>
                )}
              />
            </div>
          </div>

          {previewUrl !== '' ? (
            <HlsPlayer isStreaming src={previewUrl} />
          ) : (
            <LoaderIcon className="mx-auto min-h-10 min-w-10" />
          )}
        </>
      )}
      <div className="flex ml-auto sticky bottom-0 bg-white py-4 justify-end gap-3 w-full">
        <SecondaryButton
          className="sm:max-w-[200px]"
          onClick={() =>
            isFormDirty ? setConfirmCloseModal(true) : setModalOpen(false)
          }
        >
          Cancel
        </SecondaryButton>
        <PrimaryButton
          isDisabled={
            (isSubmitting || !isValid || !isDirty) &&
            toBeAssignedTags.length === 0 &&
            unassignedTags.length === 0
          }
          className="sm:max-w-[200px]"
          isLoading={isSubmitting}
          type="submit"
        >
          {defaultValue ? 'Save' : 'Add'}
        </PrimaryButton>
      </div>
      <ConfirmModal
        onConfirm={() => {
          setConfirmCloseModal(false)
          setModalOpen(false)
        }}
        text={
          'You have unsaved changes that will be lost if you leave this page. Do you want to continue?'
        }
        openState={[confirmCloseModal, setConfirmCloseModal]}
        title={'Unsaved Changes'}
      />
    </form>
  )
}

export default CameraDetailComponent
