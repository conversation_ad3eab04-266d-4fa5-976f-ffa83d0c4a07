import React, { useEffect, useState } from 'react'
import PrimaryButton from '../../PrimaryButtons'
import DropdownMenus from '../../DropdownMenu'
import { PlusIcon } from '@heroicons/react/20/solid'
import { CameraTags, ICameras } from '../../../models/camera'
import TagComponent from '../../Tags'
import { IOption, IPagination } from '../../../interfaces'
import toast from 'react-hot-toast'
import { getCameraTags } from '../../../api/Camera'
import TextInputComponent from '../../TextInputComponent'
import { ENV } from '../../../utils'

type Props = {
  toBeAssignedTagsState: [
    IOption[],
    React.Dispatch<React.SetStateAction<IOption[]>>
  ]
  unassignedTagsState: [
    string[],
    React.Dispatch<React.SetStateAction<string[]>>
  ]
  defaultValue?: ICameras
}

const TagSelectComponent = ({
  defaultValue,
  toBeAssignedTagsState,
  unassignedTagsState
}: Props) => {
  const [listCameraTags, setListCameraTags] = useState<CameraTags[]>([])
  const [createTagName, setCreateTagName] = useState<string>('')
  const [creatingTag, setCreatingTag] = useState<boolean>(false) //create a new tag instead of assign a new one
  const [toBeAssignedTags, setToBeAssignedTags] = toBeAssignedTagsState
  const [unassignedTags, setUnassignedTags] = unassignedTagsState
  const [totalAssignedTags, setTotalAssignedTags] = useState<IOption[]>(
    defaultValue?.tags.map((tag) => {
      return {
        label: tag.name ?? '',
        value: tag.id ?? ''
      }
    }) ?? []
  )
  const handleCreateNewTag = (tag: string) => {
    const isTagAlreadyAssigned = defaultValue?.tags.some(
      (defaultTag) => defaultTag.name === tag
    )
    const isTagsAboutToAssign = toBeAssignedTags.some(
      (toBeAssignedTag) => toBeAssignedTag.label === tag
    )
    if (isTagAlreadyAssigned || isTagsAboutToAssign) {
      toast.error('This tag is already assigned', { position: 'top-right' })
      return
    }

    if (tag) {
      setToBeAssignedTags((prev) => [{ label: tag, value: tag }, ...prev])
      if (unassignedTags.includes(tag)) {
        setUnassignedTags((prev) => prev.filter((item) => item !== tag))
      }
      setCreateTagName('')
    }
    setCreatingTag(false)
  }
  const handleAssignExistTag = (tag: IOption) => {
    const isTagAlreadyAssigned =
      totalAssignedTags.some(
        (assignedTag) => assignedTag.value === tag.value
      ) ||
      toBeAssignedTags.some(
        // prevent tags with duplicated names and ids with the already assigned tag
        (tobeAssignedTag) =>
          tobeAssignedTag.value === tag.value ||
          tobeAssignedTag.label === tag.label
      )
    if (isTagAlreadyAssigned) {
      toast.error('This tag is already assigned', { position: 'top-right' })
      return
    } else {
      setToBeAssignedTags((prev) => [tag, ...prev])
      if (unassignedTags.includes(tag.value)) {
        setUnassignedTags((prev) => prev.filter((item) => item !== tag.value))
      }
    }
  }
  const handleRemoveTags = (tag: IOption) => {
    const isTagAlreadyAssigned = totalAssignedTags.some(
      (assignedTag) => assignedTag.label === tag.label
    )
    if (isTagAlreadyAssigned) {
      setTotalAssignedTags((prev) =>
        prev.filter((item) => item.label !== tag.label)
      )
      setUnassignedTags((prev) => [...prev, tag.value])
    } else {
      setToBeAssignedTags((prev) => prev.filter((item) => item !== tag))
    }
  }
  const fetchListTags = async () => {
    const data: IPagination = {
      'page-no': 1,
      'page-size': 50
    }
    try {
      const res = await getCameraTags(data)
      setListCameraTags(res.data)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
      toast.error('Error getting tags list', { position: 'top-right' })
    }
  }
  useEffect(() => {
    fetchListTags()
  }, [])
  return (
    <div className="w-full flex-1 flex justify-center flex-col mb-4">
      <p className="font-semibold mb-2 text-black text-sm w-fit">Assign Tags</p>
      {creatingTag && (
        <div className="flex items-start mb-4 gap-4">
          <div className="flex flex-col gap-1 flex-1">
            <TextInputComponent
              autoFocus
              maxLength={50}
              placeholder="Enter Tag name"
              onChange={(e) => setCreateTagName(e.target.value)}
            />
            <span className="text-xs text-gray-400 ml-auto">{`${
              createTagName.length ?? 0
            }/50 characters`}</span>
          </div>

          <PrimaryButton
            onClick={() => handleCreateNewTag(createTagName.trim())}
            className="max-w-[200px]"
          >
            {createTagName.trim() === '' ? 'Cancel' : 'Add'}
          </PrimaryButton>
        </div>
      )}
      <div className="flex gap-2 flex-wrap">
        <div className="flex">
          <DropdownMenus
            label={
              <div className="flex items-center gap-1">
                <PlusIcon height={20} width={20} />
                <p>Add</p>
              </div>
            }
            menus={[
              {
                id: '-1',
                text: 'Create a new Tag',
                onClick: () => setCreatingTag(true)
              }
            ].concat(
              listCameraTags.map((tag) => {
                return {
                  id: tag.id ?? '',
                  text: tag.name ?? '',
                  onClick: () =>
                    handleAssignExistTag({
                      label: tag.name ?? '',
                      value: tag.id ?? ''
                    })
                }
              })
            )}
          />
        </div>
        {totalAssignedTags.concat(toBeAssignedTags).map((displayedTag) => (
          <TagComponent
            key={displayedTag.value}
            removable
            onRemove={() => handleRemoveTags(displayedTag)}
            text={displayedTag.label ?? ''}
            textColor="#272727"
            backgroundColor="white"
            border="1px solid #E3E3E3"
          />
        ))}
      </div>
    </div>
  )
}

export default TagSelectComponent
