type Props = {
  type: string
}
export default function StatusBadgeComponent({ type }: Props) {
  switch (type) {
    case 'offline':
      return (
        <span className="inline-flex bg-transparent capitalize items-center gap-x-1.5 rounded-full sm:bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">
          <svg
            viewBox="0 0 6 6"
            aria-hidden="true"
            className="h-1.5 w-1.5 fill-gray-400"
          >
            <circle r={3} cx={3} cy={3} />
          </svg>
          <span className="text-gray-600 hidden sm:block">{type}</span>
        </span>
      )
    case 'online':
      return (
        <span className="inline-flex capitalize bg-transparent items-center gap-x-1.5 rounded-full sm:bg-green-100 px-2 py-1 text-xs font-medium text-green-700">
          <svg
            viewBox="0 0 6 6"
            aria-hidden="true"
            className="h-1.5 w-1.5 fill-green-500"
          >
            <circle r={3} cx={3} cy={3} />
          </svg>
          <span className="text-green-700 hidden sm:block">{type}</span>
        </span>
      )
    default:
      return (
        <span className="inline-flex items-center gap-x-1.5 rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">
          <svg
            viewBox="0 0 6 6"
            aria-hidden="true"
            className="h-1.5 w-1.5 fill-gray-400"
          >
            <circle r={3} cx={3} cy={3} />
          </svg>
          {type}
        </span>
      )
  }
}
