import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { FILTER_TYPES } from '../../enum/FilterTypes'
import { IAgent, ListAgentParams } from '../../models/agents'
import { IAnalytics } from '../../models/analytics'
import { CameraTags } from '../../models/camera'
import { IProject, ListProjectQuerys } from '../../models/projects'
import { IOption, IPagination, UpdateFilters } from '../../interfaces'
import { getListAgents } from '../../api/Agents'
import { getListAnalytics } from '../../api/Analytics'
import { getCameraTags } from '../../api/Camera'
import { getListProjects } from '../../api/Projects'
import SecondaryButton from '../SecondaryButtons'
import PrimaryButton from '../PrimaryButtons'
import TagComponent from '../Tags'
import TabNavigationComponent from '../TabNavigationComponent'
import ListFilterOptionsComponent from './ListFilterOptions'
import { ENV } from '../../utils'

type Props = {
  onAddFilters: (params: UpdateFilters) => void
  onRemoveFilters: (params: UpdateFilters) => void
  closeModal: () => void
  onApply: () => void
  clearAllFilters: () => void
  filteredAgent: IOption[]
  filteredProject: IOption[]
  filteredTag: IOption[]
  filteredVA: IOption[]
}

const FilterModalComponent = ({
  onAddFilters,
  onApply,
  onRemoveFilters,
  closeModal,
  clearAllFilters,
  filteredAgent,
  filteredProject,
  filteredTag,
  filteredVA
}: Props) => {
  const [listCameraTags, setListCameraTags] = useState<CameraTags[]>([])
  const [listAgents, setListAgents] = useState<IAgent[]>([])
  const [listProjects, setListProjects] = useState<IProject[]>([])
  const [listVA, setListVA] = useState<IAnalytics[]>([])
  const [selectedFilter, setSelectedFilter] = useState<FILTER_TYPES>(
    FILTER_TYPES.AGENTS
  )
  const [isFetching, setIsFetching] = useState<boolean>(false)
  const [page, setPage] = useState<number>(1)

  const endOfTheListRef = useRef<HTMLDivElement | null>(null)
  const totalPages = useRef<number>(0)
  const isMounted = useRef<boolean>(false) //flag to check if the component mounted
  const filters: IOption[] = [
    {
      label: 'Camera Agents',
      value: FILTER_TYPES.AGENTS
    },
    {
      label: 'Projects',
      value: FILTER_TYPES.PROJECTS
    },
    {
      label: 'Tags',
      value: FILTER_TYPES.TAGS
    },
    {
      label: 'VA Plugins',
      value: FILTER_TYPES.VA_PLUGINS
    }
  ]

  const fetchListTags = async () => {
    setIsFetching(true)
    const data: IPagination = {
      'page-no': page,
      'page-size': 10
    }
    try {
      const res = await getCameraTags(data)
      setListCameraTags((prev) =>
        data['page-no'] === 1 ? res.data : prev.concat(res.data)
      )
      totalPages.current = Math.ceil(res.total / 10)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    } finally {
      setIsFetching(false)
    }
  }

  const fetchListAgents = async () => {
    setIsFetching(true)
    const data: ListAgentParams = {
      'page-no': page,
      'page-size': 10
    }
    isMounted.current = true // mark the component as mounted because Camera agents is the default filter to appear when modal rendered
    try {
      const res = await getListAgents(data)
      setListAgents((prev) =>
        data['page-no'] === 1 ? res.data : prev.concat(res.data)
      )
      totalPages.current = Math.ceil(res.total / 10)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    } finally {
      setIsFetching(false)
    }
  }

  const fetchListProjects = async () => {
    setIsFetching(true)
    const data: ListProjectQuerys = {
      'page-no': page,
      'page-size': 10,
      name: ''
    }
    try {
      const res = await getListProjects(data)
      setListProjects((prev) =>
        data['page-no'] === 1 ? res.data : prev.concat(res.data)
      )
      totalPages.current = Math.ceil(res.total / 10)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    } finally {
      setIsFetching(false)
    }
  }

  const fetchListVAs = async () => {
    setIsFetching(true)
    const data: IPagination = {
      'page-no': page,
      'page-size': 10
    }
    try {
      const res = await getListAnalytics(data)
      setListVA((prev) =>
        data['page-no'] === 1
          ? res.data.filter((va) => !va.api_keys?.[0].revoked)
          : prev.concat(res.data.filter((va) => !va.api_keys?.[0].revoked))
      )
      totalPages.current = Math.ceil(res.total / 10)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    } finally {
      setIsFetching(false)
    }
  }

  const handleSwitchFilter = (type: FILTER_TYPES) => {
    setSelectedFilter(type)
    if (page > 1) {
      setPage(1)
    } else {
      switch (type) {
        case FILTER_TYPES.AGENTS:
          fetchListAgents()
          break
        case FILTER_TYPES.PROJECTS:
          fetchListProjects()
          break
        case FILTER_TYPES.TAGS:
          fetchListTags()
          break
        case FILTER_TYPES.VA_PLUGINS:
          fetchListVAs()
          break
        default:
          break
      }
    }
  }

  const renderListFilterOptions: IOption[] = useMemo(() => {
    switch (selectedFilter) {
      case FILTER_TYPES.AGENTS:
        return listAgents.map((agent) => ({
          label: agent.name,
          value: agent.id,
          additonalValue: FILTER_TYPES.AGENTS
        }))
      case FILTER_TYPES.TAGS:
        return listCameraTags.map((tag) => ({
          label: tag.name ?? '',
          value: tag.id ?? '',
          additonalValue: FILTER_TYPES.TAGS
        }))
      case FILTER_TYPES.VA_PLUGINS:
        return listVA.map((va) => ({
          label: va.name ?? '',
          value: va.id ?? '',
          additonalValue: FILTER_TYPES.VA_PLUGINS
        }))
      case FILTER_TYPES.PROJECTS:
        return listProjects.map((project) => ({
          label: project?.name ?? '',
          value: project?.id ?? '',
          additonalValue: FILTER_TYPES.PROJECTS
        }))
      default:
        return []
    }
  }, [listAgents, listCameraTags, listProjects, listVA, selectedFilter])

  const renderSelectedFilterNumber = useCallback(
    (type: FILTER_TYPES): number => {
      switch (type) {
        case FILTER_TYPES.AGENTS:
          return filteredAgent.length
        case FILTER_TYPES.TAGS:
          return filteredTag.length
        case FILTER_TYPES.VA_PLUGINS:
          return filteredVA.length
        case FILTER_TYPES.PROJECTS:
          return filteredProject.length
        default:
          return 0
      }
    },
    [filteredAgent, filteredProject, filteredTag, filteredVA]
  )

  const renderCheckedStatus = useCallback(
    (type: FILTER_TYPES, value: string): boolean => {
      switch (type) {
        case FILTER_TYPES.AGENTS:
          return filteredAgent.some((item) => item.value === value)
        case FILTER_TYPES.TAGS:
          return filteredTag.some((item) => item.value === value)
        case FILTER_TYPES.VA_PLUGINS:
          return filteredVA.some((item) => item.value === value)
        case FILTER_TYPES.PROJECTS:
          return filteredProject.some((item) => item.value === value)
        default:
          return false
      }
    },
    [filteredAgent, filteredProject, filteredTag, filteredVA]
  )

  const handleCheckFilter = (checked: boolean, item: IOption) => {
    const updatedData: UpdateFilters = {
      type: selectedFilter,
      data: item
    }
    if (checked) {
      onAddFilters(updatedData)
    } else {
      onRemoveFilters(updatedData)
    }
  }

  useEffect(() => {
    // fetch data when switching page/when modal first popup
    if (selectedFilter === FILTER_TYPES.TAGS) {
      fetchListTags()
    }
    if (selectedFilter === FILTER_TYPES.AGENTS) {
      fetchListAgents()
    }
    if (selectedFilter === FILTER_TYPES.PROJECTS) {
      fetchListProjects()
    }
    if (selectedFilter === FILTER_TYPES.VA_PLUGINS) {
      fetchListVAs()
    }
  }, [page])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && page < totalPages.current) {
          setPage((prevPage) => prevPage + 1)
        }
      },
      { threshold: 0.5 }
    )

    if (endOfTheListRef.current) observer.observe(endOfTheListRef.current)

    return () => {
      if (endOfTheListRef.current) observer.unobserve(endOfTheListRef.current)
    }
  }, [renderListFilterOptions])

  return (
    <div className="w-full min-h-[380px] pt-6 h-full flex flex-col">
      <div className="flex gap-8 sm:flex-row flex-col flex-1 overflow-auto w-full">
        <TabNavigationComponent
          filters={filters}
          selectedFilter={selectedFilter}
          setSelectedFilter={handleSwitchFilter}
          renderSelectedFilterNumber={renderSelectedFilterNumber}
        />

        <ListFilterOptionsComponent
          ref={endOfTheListRef}
          renderListFilterOptions={renderListFilterOptions}
          renderCheckedStatus={renderCheckedStatus}
          selectedFilter={selectedFilter}
          handleCheckFilter={handleCheckFilter}
          isFetching={isFetching}
        />
      </div>
      <div className="flex flex-col my-4">
        <p className="text-xs font-medium mb-2">
          {filteredAgent.length +
            filteredProject.length +
            filteredTag.length +
            filteredVA.length}{' '}
          filters selected
        </p>
        <div className="flex flex-wrap max-w-full gap-2">
          {filteredAgent.map((item) => (
            <TagComponent
              key={item.value}
              text={item.label}
              removable
              textColor="black"
              onRemove={() =>
                onRemoveFilters({
                  type: FILTER_TYPES.AGENTS,
                  data: item
                })
              }
            />
          ))}
          {filteredProject.map((item) => (
            <TagComponent
              key={item.value}
              text={item.label}
              removable
              textColor="black"
              onRemove={() =>
                onRemoveFilters({
                  type: FILTER_TYPES.PROJECTS,
                  data: item
                })
              }
            />
          ))}
          {filteredTag.map((item) => (
            <TagComponent
              key={item.value}
              text={item.label}
              removable
              textColor="black"
              onRemove={() =>
                onRemoveFilters({
                  type: FILTER_TYPES.TAGS,
                  data: item
                })
              }
            />
          ))}
          {filteredVA.map((item) => (
            <TagComponent
              key={item.value}
              text={item.label}
              onRemove={() =>
                onRemoveFilters({
                  type: FILTER_TYPES.VA_PLUGINS,
                  data: item
                })
              }
              removable
              textColor="black"
            />
          ))}
        </div>
      </div>
      <div className="w-full bg-white sticky bottom-0 flex-col-reverse flex sm:flex-row gap-2 items-center justify-between border-0 py-4 border-t border-solid border-t-[#E3E3E3]">
        <p
          onClick={clearAllFilters}
          className="text-sm text-primary font-medium cursor-pointer hover:underline"
        >
          Clear all
        </p>
        <div className="flex gap-2 w-2/3">
          <SecondaryButton
            onClick={() => {
              clearAllFilters()
              closeModal()
            }}
          >
            Cancel
          </SecondaryButton>
          <PrimaryButton
            onClick={() => {
              onApply()
              closeModal()
            }}
          >
            Apply
          </PrimaryButton>
        </div>
      </div>
    </div>
  )
}

export default FilterModalComponent
