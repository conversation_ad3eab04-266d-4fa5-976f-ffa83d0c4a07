import { forwardRef } from 'react'
import { IOption } from '../../../interfaces'
import { LoaderIcon } from 'react-hot-toast'
import { FILTER_TYPES } from '../../../enum/FilterTypes'
import NoDataComponent from '../../NoDataComponent'

type Props = {
  renderListFilterOptions: IOption[]
  renderCheckedStatus: (type: FILTER_TYPES, value: string) => boolean
  selectedFilter: FILTER_TYPES
  handleCheckFilter: (checked: boolean, item: IOption) => void
  isFetching: boolean
}

const ListFilterOptionsComponent = forwardRef<HTMLDivElement | null, Props>(
  (
    {
      renderListFilterOptions,
      renderCheckedStatus,
      selectedFilter,
      handleCheckFilter,
      isFetching
    },
    ref
  ) => {
    if (renderListFilterOptions.length === 0) {
      return (
        <div className="w-full h-full my-auto">
          <NoDataComponent />
        </div>
      )
    }
    return (
      <div className="flex-1 flex overflow-auto max-h-[250px] flex-col gap-2 p-2">
        {renderListFilterOptions.map((filter) => (
          <div
            key={filter.value}
            className="flex min-h-[18px] w-full p-1 truncate items-center gap-3"
          >
            <input
              type="checkbox"
              name=""
              checked={renderCheckedStatus(selectedFilter, filter.value)}
              id={'option' + filter.value}
              onChange={(e) => handleCheckFilter(e.target.checked, filter)}
              className="h-4 w-4 rounded border-solid border-gray-300 text-primary ring-0 focus:ring-0"
            />
            <label
              htmlFor={'option' + filter.value}
              className="text-sm truncate text-[#272727]"
            >
              {filter.label}
            </label>
          </div>
        ))}
        <div ref={ref} style={{ height: '20px' }}></div>
        {isFetching && (
          <LoaderIcon className="mx-auto min-h-[20px] min-w-[20px]" />
        )}
      </div>
    )
  }
)

export default ListFilterOptionsComponent
