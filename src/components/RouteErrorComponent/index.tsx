import { Link, useRouteError } from 'react-router-dom'
import { ROUTE_PATH } from '../../enum/RoutePath'

export default function RouteErrorPage() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const error: any = useRouteError()

  return (
    <div className="h-full w-full flex-col justify-center items-center flex bg-white text-black">
      <h1>Oops!</h1>
      <p>Sorry, an unexpected error has occurred.</p>
      <p>
        <i>{error.statusText || error.message}</i>
      </p>
      <Link
        to={ROUTE_PATH.Home}
        className="inline-flex cursor-pointer border-none justify-center rounded-md px-5 py-2 items-center text-sm bg-[#557afc] font-semibold text-white hover:opacity-70 hover:text-white sm:w-auto"
      >
        Go to Home
      </Link>
    </div>
  )
}
