import { XMarkIcon } from '@heroicons/react/24/solid'
import { classNames } from '../../utils'
import { ReactNode } from 'react'

type Props = {
  text: string | ReactNode
  backgroundColor?: string
  textColor?: string
  removable?: boolean
  onRemove?: () => void
  border?: string
  onClick?: () => void
  fontSize?: string
  className?: string
}

const TagComponent = ({
  text,
  backgroundColor,
  textColor,
  removable,
  onRemove,
  border,
  onClick,
  fontSize,
  className
}: Props) => {
  return (
    <div
      style={{
        backgroundColor: backgroundColor ?? '#F4F4F4',
        border: border ?? 'none'
      }}
      onClick={onClick && onClick}
      className={classNames(
        className ?? className,
        ' py-2 flex gap-2 px-4 w-fit truncate max-w-[200px] sm:max-w-[400px] rounded-3xl cursor-pointer'
      )}
    >
      <p
        style={{
          color: textColor ?? 'white',
          fontSize: fontSize ?? '14px'
        }}
        className={classNames(`text-sm truncate text-center w-fit`)}
      >
        {text}
      </p>
      {removable && (
        <XMarkIcon
          cursor={'pointer'}
          onClick={() => onRemove && onRemove()}
          height={20}
          width={20}
          className="min-w-5 min-h-5"
          fill={textColor ?? 'white'}
        />
      )}
    </div>
  )
}

export default TagComponent
