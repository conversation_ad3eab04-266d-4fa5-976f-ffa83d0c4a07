import { TrashIcon as TrashIconOutline } from '@heroicons/react/24/outline'

type Props = {
  title: string
  description: string
}

const DeleteModalHeader = ({ title, description }: Props) => {
  return (
    <div className="flex items-center gap-3">
      <div className="flex h-11 w-11 items-center justify-center rounded-full bg-red-100">
        <TrashIconOutline className="h-5 w-5 stroke-red-600 fill-transparent" />
      </div>
      <div>
        <p className="text-left text-lg font-bold text-gray-900">{title}</p>
        <p className="text-left font-normal text-sm text-gray-600">
          {description}
        </p>
      </div>
    </div>
  )
}

export default DeleteModalHeader
