import { ControllerRenderProps, FieldValues, Path } from 'react-hook-form'
import { IOption } from '../../interfaces'
import { Select } from 'antd'
import TextInputComponent from '../TextInputComponent'
import SearchIcon from '../../assets/svgs/SearchIcon'
import { LoaderIcon } from 'react-hot-toast'

interface Props<T extends FieldValues> {
  field?: ControllerRenderProps<T, Path<T>>
  options: IOption[]
  fetchMoreData: (e: React.UIEvent<HTMLDivElement, UIEvent>) => void
  onSearch?: (e: React.ChangeEvent<HTMLInputElement>) => void
  isFetchMore?: boolean
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange?: (e: any) => void
  disabled?: boolean
  placeholder?: string
  searchInputPlaceholder?: string
  className?: string
}

const LazyScrollSelectComponent = <T extends FieldValues>({
  field,
  options = [],
  fetchMoreData,
  onSearch = () => {},
  isFetchMore,
  onChange,
  disabled,
  placeholder,
  searchInputPlaceholder,
  className = ''
}: Props<T>) => {
  return (
    <Select
      {...field}
      options={options}
      disabled={disabled}
      className={`w-full flex-1 h-[36px] truncate ${className}`}
      onPopupScroll={fetchMoreData}
      placeholder={placeholder ?? 'Select an option...'}
      onChange={(value) =>
        onChange ? onChange(value) : field?.onChange(value)
      } // ensure value updates
      dropdownRender={(menu) => (
        <div className={`w-full flex flex-col gap-2 px-3 py-4`}>
          {onSearch && (
            <TextInputComponent
              prefixIcon={<SearchIcon />}
              onChange={onSearch}
              placeholder={searchInputPlaceholder ?? 'Search...'}
            />
          )}
          {menu}
          {isFetchMore && <LoaderIcon className="mx-auto min-h-5 min-w-5" />}
        </div>
      )}
    />
  )
}

export default LazyScrollSelectComponent
