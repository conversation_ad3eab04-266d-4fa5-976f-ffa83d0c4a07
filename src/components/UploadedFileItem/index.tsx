import { classNames } from '../../utils'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { Progress } from 'antd'
import { CheckCircleIcon } from '@heroicons/react/24/solid'

type Props = {
  file: File
  failedFile?: string
  isUploading?: boolean
  onRemoveFile: (file: File) => void
  uploadProgress?: number
}

const UploadedFileItem = ({
  file,
  failedFile,
  isUploading,
  onRemoveFile,
  uploadProgress = 0
}: Props) => {
  return (
    <div
      key={file.name}
      className={classNames(
        'w-full py-3 px-6 rounded-xl bg-[#F1F1F1] border border-solid',
        failedFile && failedFile !== ''
          ? 'border-red-400'
          : 'border-transparent'
      )}
    >
      <div className="flex items-center gap-4 justify-between truncate">
        <p className="text-sm font-medium truncate">
          {file.name
            .replace(/\s+/g, '_') // Replace all whitespace with "_"
            .replace(/[^a-zA-Z0-9_.]/g, '')}
        </p>
        {!isUploading && (
          <XMarkIcon
            onClick={() => onRemoveFile(file)}
            cursor={'pointer'}
            height={20}
            width={20}
          />
        )}
      </div>
      <p className="text-sm">{(file.size / (1024 * 1024)).toFixed(2)} MB</p>
      {isUploading && (
        <>
          <Progress
            format={(percent) =>
              (percent ?? 0) < 100 ? (
                Math.floor(percent ?? 0) + '%'
              ) : (
                <CheckCircleIcon
                  className="fill-green-400"
                  height={20}
                  width={20}
                />
              )
            }
            percent={uploadProgress}
            strokeColor={uploadProgress === 100 ? '#79DA4B' : '#843BAC'}
          />
          <span className="text-xs text-gray-400">
            Please do not close this window until your upload is completed.
          </span>
        </>
      )}
      {failedFile && failedFile !== '' && (
        <span className="text-xs text-red-400">
          Your upload failed, please try again
        </span>
      )}
    </div>
  )
}

export default UploadedFileItem
