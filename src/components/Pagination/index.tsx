import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/20/solid'
import { classNames } from '../../utils'
import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon
} from '@heroicons/react/24/solid'
import { useMemo } from 'react'
type Props = {
  totalItems: number
  currentPage: number
  onPageChange: (page: number) => void
  pageSize: number
}
const TRUNCATE_LIMIT = 3

const PaginationComponent = ({
  totalItems,
  currentPage,
  onPageChange,
  pageSize
}: Props) => {
  const pagesArray = useMemo(() => {
    if (pageSize === 0) return []
    return Array.from({ length: Math.ceil(totalItems / pageSize) }, (_, i) => i)
  }, [totalItems, pageSize])

  const withinTruncateRange = (page: number) =>
    page > currentPage - TRUNCATE_LIMIT &&
    page < currentPage + TRUNCATE_LIMIT &&
    page > 0

  if (totalItems === 0) return null

  return (
    <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
      <div
        data-testid="mobile-navigation"
        className="flex flex-1 justify-between sm:hidden"
      >
        <a
          onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
          className="cursor-pointer flex gap-2  items-center rounded-md border border-gray-300 bg-white p-0 sm:px-4 sm:py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          <ChevronDoubleLeftIcon height={16} width={16} />
          Previous
        </a>
        <a
          onClick={() =>
            currentPage < pagesArray.length && onPageChange(currentPage + 1)
          }
          className=" cursor-pointer ml-3 flex gap-2 items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Next <ChevronDoubleRightIcon height={16} width={16} />
        </a>
      </div>
      <div
        data-testid="desktop-navigation"
        className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between"
      >
        <div>
          <p className="text-sm text-gray-700">
            Showing{' '}
            <span className="font-medium">
              {(currentPage - 1) * pageSize + 1}
            </span>{' '}
            to{' '}
            <span className="font-medium">
              {totalItems < currentPage * pageSize
                ? totalItems
                : currentPage * pageSize}
            </span>{' '}
            of <span className="font-medium">{totalItems}</span> results
          </p>
        </div>
        <div>
          <nav
            aria-label="Pagination"
            className="isolate inline-flex -space-x-px rounded-md shadow-sm"
          >
            <a
              data-testid="previous-link"
              onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
              className="relative cursor-pointer inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
            >
              <ChevronLeftIcon aria-hidden="true" className="size-5" />
            </a>

            {currentPage > TRUNCATE_LIMIT && (
              <>
                <a
                  data-testid="firstpage-link"
                  onClick={() => onPageChange(1)}
                  aria-current="page"
                  className={classNames(
                    'relative inline-flex cursor-pointer items-center px-4 py-2 text-sm font-semibold text-mainBlack ring-1 ring-inset ring-gray-300 focus:z-20 focus:outline-offset-0',
                    currentPage === 1
                      ? 'bg-indigo-600 text-white'
                      : 'bg-transparent text-black hover:bg-gray-50'
                  )}
                >
                  1
                </a>
                <span className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">
                  ...
                </span>
              </>
            )}
            {pagesArray.map((page) => {
              if (withinTruncateRange(page)) {
                return (
                  <a
                    data-testid={
                      page === currentPage ? 'current-link' : 'visible-link'
                    }
                    onClick={() => onPageChange(page)}
                    aria-current="page"
                    key={page}
                    className={classNames(
                      'relative inline-flex cursor-pointer items-center px-4 py-2 text-sm font-semibold text-mainBlack ring-1 ring-inset ring-gray-300 focus:z-20 focus:outline-offset-0',
                      page === currentPage
                        ? 'bg-indigo-600 text-white'
                        : 'bg-transparent text-black hover:bg-gray-50'
                    )}
                  >
                    {page}
                  </a>
                )
              }
            })}

            {currentPage < pagesArray.length - TRUNCATE_LIMIT && (
              <span
                data-testid="truncate-link"
                className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0"
              >
                ...
              </span>
            )}

            <a
              data-testid="lastpage-link"
              onClick={() => onPageChange(pagesArray.length)}
              aria-current="page"
              className={classNames(
                'relative inline-flex cursor-pointer items-center px-4 py-2 text-sm font-semibold text-mainBlack ring-1 ring-inset ring-gray-300 focus:z-20 focus:outline-offset-0',
                currentPage === (pagesArray.length || 1)
                  ? 'bg-indigo-600 text-white'
                  : 'bg-transparent text-black hover:bg-gray-50'
              )}
            >
              {pagesArray.length || 1}
            </a>
            <a
              data-testid="next-link"
              onClick={() =>
                currentPage < pagesArray.length && onPageChange(currentPage + 1)
              }
              className="relative cursor-pointer inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
            >
              <ChevronRightIcon aria-hidden="true" className="size-5" />
            </a>
          </nav>
        </div>
      </div>
    </div>
  )
}

export default PaginationComponent
