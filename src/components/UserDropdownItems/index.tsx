import { PropsWithChildren } from 'react'
import { IOption } from '../../interfaces'
import ProfileIcon from '../../assets/svgs/ProfileIcon'

interface Props extends PropsWithChildren {
  item: IOption
}

const UserDropdownItemComponent = ({ item, children }: Props) => {
  return (
    <div className="w-full flex justify-between items-center gap-4">
      <div className="flex gap-2 items-center">
        <ProfileIcon fill="white" width={40} height={40} />
        <div>
          <p className="group-data-[focus]:text-white font-semibold text-sm">
            {item.label}
          </p>
          <p className="group-data-[focus]:text-white text-xs text-gray-500">
            {item.additonalValue}
          </p>
        </div>
      </div>
      {children}
    </div>
  )
}

export default UserDropdownItemComponent
