import { ReactNode } from 'react'
import {
  Control,
  Controller,
  ControllerFieldState,
  ControllerRenderProps,
  FieldErrors,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormStateReturn
} from 'react-hook-form'
import { ErrorMessage } from '@hookform/error-message'
import { classNames } from '../../utils'
import { ExclamationCircleIcon } from '@heroicons/react/20/solid'
interface Props<T extends FieldValues> {
  maxCharactersText?: string | ReactNode
  endfixIcon?: ReactNode
  label: string | ReactNode
  name: Path<T> //names of the form items in react-hook-form
  required?: boolean
  errors?: FieldErrors //errors from react-hook-form
  vertialAlign?: boolean
  containerClassName?: string
  control: Control<T, Path<T>> //control the form item with react hook form
  rules?: // rules to validate
  | Omit<
        RegisterOptions<T>,
        'disabled' | 'valueAsNumber' | 'valueAsDate' | 'setValueAs'
      >
    | undefined
  render: ({
    // render the input child
    field,
    fieldState,
    formState
  }: {
    field: ControllerRenderProps<T, Path<T>>
    fieldState: ControllerFieldState
    formState: UseFormStateReturn<T>
  }) => React.ReactElement
}

const FormInputContainer = <T extends FieldValues>({
  label,
  required,
  name,
  errors,
  vertialAlign,
  containerClassName,
  control,
  rules,
  render,
  endfixIcon,
  maxCharactersText
}: Props<T>) => {
  return (
    <div
      className={classNames(
        'w-full flex-1 flex justify-center',
        vertialAlign ? 'flex-col' : 'flex-row gap-2',
        containerClassName && containerClassName
      )}
    >
      <p
        className={classNames(
          'font-semibold mb-0 text-black text-sm',
          vertialAlign ? 'w-fit' : 'mt-3'
        )}
      >
        {label}{' '}
        {required && label !== '' && <span className="text-red-400">*</span>}
      </p>
      <div className="flex-1 w-full gap-2 flex flex-col">
        <div className="relative mt-2 w-full max-w-full">
          <Controller
            control={control}
            rules={rules}
            name={name}
            render={render}
          />
          {errors?.[name]?.message && (
            <div
              className={classNames(
                'pointer-events-none absolute inset-y-0 flex items-center',
                endfixIcon ? 'right-8' : 'right-2'
              )}
            >
              <ExclamationCircleIcon
                aria-hidden="true"
                color="red"
                fill="red"
                className="h-5 w-5 text-red-500"
              />
            </div>
          )}
          {endfixIcon && (
            <div className="absolute inset-y-0 right-2 flex items-center">
              {endfixIcon}
            </div>
          )}
        </div>

        {errors && (
          <div className="flex w-full justify-between">
            <ErrorMessage
              errors={errors}
              name={name as string}
              render={({ message }) => {
                return <p className="text-xs text-red-400 w-full">{message}</p>
              }}
            />
            {maxCharactersText && (
              <p className="text-end w-full text-gray-400 text-xs font-semibold">
                {' '}
                {maxCharactersText}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default FormInputContainer
