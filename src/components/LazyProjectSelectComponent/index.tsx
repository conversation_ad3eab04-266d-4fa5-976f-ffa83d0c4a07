import { useMemo, useState } from 'react'
import { IProject, ListProjectQuerys } from '../../models/projects'
import useLazyScroll from '../../utils/hooks/useLazyScroll'
import { DEFAULT_PAGE_SIZE, ENV } from '../../utils'
import { getListProjects } from '../../api/Projects'
import LazyScrollSelectComponent from '../LazyScrollSelect'
import { ControllerRenderProps, FieldValues, Path } from 'react-hook-form'

interface Props<T extends FieldValues> {
  field?: ControllerRenderProps<T, Path<T>>
  onChange?: (value: string) => void
  className?: string
}
const LazyProjectSelectComponent = <T extends FieldValues>({
  field,
  onChange,
  className = ''
}: Props<T>) => {
  const [listProjects, setListProject] = useState<IProject[]>([])
  const [searchProject, setSearchProject] = useState<string>('')
  const [projectPage, setProjectPage] = useState<number>(1)
  const [isFetchingMoreProject, setIsFetchingMoreProject] =
    useState<boolean>(false)
  const {
    markAsMounted,
    noMoreData,
    fetchMoreFunc: fetchMoreProjectList
  } = useLazyScroll({
    currentPage: projectPage,
    searchText: searchProject.trim(),
    fetchDataFunc: () => fetchListProjects(),
    onPageChange: (page) => setProjectPage(page)
  })

  const fetchListProjects = async () => {
    const data: ListProjectQuerys = {
      'page-no': projectPage,
      'page-size': DEFAULT_PAGE_SIZE,
      name: searchProject
    }
    setIsFetchingMoreProject(true)
    try {
      const res = await getListProjects(data)
      const totalPages = Math.floor(res.total / DEFAULT_PAGE_SIZE)
      setListProject((prev) =>
        projectPage === 1 ? res.data : prev.concat(res.data)
      )
      if (projectPage > Math.floor(totalPages)) {
        // if the current page is the last page
        noMoreData()
      }
      markAsMounted()
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    } finally {
      setIsFetchingMoreProject(false)
    }
  }
  const memoizedListProject = useMemo(
    () =>
      listProjects.map((project) => ({
        label: project.name,
        value: project.id
      })),
    [listProjects]
  )

  return (
    <LazyScrollSelectComponent
      options={memoizedListProject}
      onChange={onChange}
      className={className}
      field={field}
      fetchMoreData={fetchMoreProjectList}
      onSearch={(e) => setSearchProject(e.target.value)}
      isFetchMore={isFetchingMoreProject}
    />
  )
}

export default LazyProjectSelectComponent
