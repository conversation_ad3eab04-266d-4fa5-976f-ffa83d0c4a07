import { Menu, <PERSON>uButton, MenuItem, MenuItems } from '@headlessui/react'
import { ReactNode } from 'react'
import { classNames } from '../../utils'
import { Menu as MenuItemType } from '../../interfaces'

type Props = {
  label: string | ReactNode
  menus: MenuItemType[]
  className?: string
  popupDirection?: 'left' | 'right'
  disableTransition?: boolean
}

export default function DropdownMenus({
  label,
  menus,
  className,
  popupDirection,
  disableTransition
}: Props) {
  return (
    <Menu
      as="div"
      className="relative flex flex-col flex-1 text-left justify-center items-center"
    >
      <MenuButton
        className={classNames(
          'shadow-sm ring-1 inline-flex w-full items-center justify-center gap-x-1.5 rounded-2xl cursor-pointer bg-transparent lg:bg-white px-3 py-2 text-sm text-mainBlackring-inset ring-gray-300 lg:hover:bg-gray-50',
          className && className
        )}
      >
        {label}
      </MenuButton>

      <MenuItems
        anchor={popupDirection === 'left' ? 'bottom end' : 'bottom start'}
        transition={!disableTransition}
        className={classNames(
          'absolute mt-2 z-10 bottom-auto w-56 max-h-[200px] overflow-auto origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in',
          Boolean(popupDirection === 'left') && 'right-[-12px]'
        )}
      >
        <div className="py-1 h-full max-h-[200px] overflow-auto">
          {menus.map((menu) => (
            <MenuItem disabled={menu.disabled} key={menu.id}>
              <a
                onClick={menu.onClick}
                className={classNames(
                  'block px-4 py-2 text-sm text-black data-[focus]:bg-gray-100 data-[focus]:font-semibold data-[focus]:text-mainBlack hover:cursor-pointer',
                  menu.disabled && 'opacity-40 !cursor-not-allowed'
                )}
              >
                {menu.text}
              </a>
            </MenuItem>
          ))}
        </div>
      </MenuItems>
    </Menu>
  )
}
