import { Popover, PopoverPanel } from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import { IOption } from '../../interfaces'
import SortIcon from '../../assets/svgs/SortIcon'
import CustomPopoverButton from './CustomPopoverButton'
import { classNames } from '../../utils'
import { ReactNode } from 'react'
type Props = {
  label: string
  options: IOption[]
  onSelect: (value: string) => void
  selected?: string | string[]
  type?: 'radio' | 'checkbox'
  onlyButtonIcon?: boolean
  labelIcon?: ReactNode
  className?: string
}

const DropdownOptions = ({
  label,
  options,
  onSelect,
  selected,
  type,
  onlyButtonIcon,
  labelIcon,
  className
}: Props) => {
  return (
    <Popover
      key={label}
      className={classNames(
        'relative flex-1 inline-flex !focus:outline-0 min-w-fit sm:w-fit text-left data-[open]:ring-0',
        onlyButtonIcon ? 'sm:min-w-fit' : 'sm:min-w-[200px] sm:max-w-[200px]',
        className && className
      )}
    >
      <CustomPopoverButton labelIcon={labelIcon} onlyIcon={onlyButtonIcon}>
        <div className="flex w-full min-w-fit sm:min-w-24 items-center gap-2">
          {labelIcon ?? <SortIcon />}
          <p className="hidden sm:inline">{label}</p>
        </div>

        <ChevronDownIcon
          aria-hidden="true"
          className="-mr-1 ml-1 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500"
        />
      </CustomPopoverButton>

      <PopoverPanel
        anchor="bottom end"
        transition
        className="z-10 !focus:outline-0 mt-2 origin-top-right rounded-md bg-white p-4 shadow-2xl ring-1 ring-black ring-opacity-5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
      >
        <form className="space-y-4">
          {type === 'checkbox' && (
            <div key={'all'} className="flex z-20 items-center">
              <input
                defaultValue={''}
                name={'All'}
                id={`filter-${'all'}`}
                checked={selected?.length === options.length}
                onChange={() => onSelect('')}
                type={'checkbox'}
                className="h-4 w-4 border border-solid border-gray-300"
              />
              <label
                htmlFor={`filter-${'all'}`}
                className="ml-3 whitespace-nowrap pr-6 capitalize text-sm font-medium text-mainBlack"
              >
                All
              </label>
            </div>
          )}
          {options.map((option, optionIdx) => (
            <div key={option.value} className="flex z-20 items-center">
              <input
                defaultValue={option.value}
                name={label}
                id={`filter-${label}-${optionIdx}`}
                checked={
                  type === 'radio'
                    ? selected === option.value
                    : selected?.includes(option.value)
                }
                onChange={() => onSelect(option.value)}
                type={type ?? 'radio'}
                className="h-4 w-4 border border-solid border-gray-300"
              />
              <label
                htmlFor={`filter-${label}-${optionIdx}`}
                className="ml-3 whitespace-nowrap pr-6 capitalize text-sm font-medium text-mainBlack"
              >
                {option.label.toLocaleLowerCase()}
              </label>
            </div>
          ))}
        </form>
      </PopoverPanel>
    </Popover>
  )
}

export default DropdownOptions
