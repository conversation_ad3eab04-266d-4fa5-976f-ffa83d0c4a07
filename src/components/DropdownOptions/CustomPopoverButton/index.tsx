import { PopoverButton } from '@headlessui/react'
import { PropsWithChildren, ReactNode } from 'react'
import { classNames } from '../../../utils'
import { ChevronDownIcon } from '@heroicons/react/20/solid'

interface Props extends PropsWithChildren {
  onlyIcon?: boolean
  labelIcon?: ReactNode
}

const CustomPopoverButton = ({ children, onlyIcon, labelIcon }: Props) => {
  return onlyIcon ? (
    <PopoverButton className={'flex items-center'}>
      {labelIcon ?? (
        <ChevronDownIcon
          aria-hidden="true"
          className="ml-1 h-5 w-5 !focus:outline-0 ring-solid text-gray-400 group-hover:text-gray-500 ring ring-transparent focus:ring-transparent"
        />
      )}
    </PopoverButton>
  ) : (
    <PopoverButton
      className={classNames(
        'group rounded-3xl flex-1 flex border-none cursor-pointer !focus:outline-0 ring-0 gap-[10px] bg-[#F4F4F4] items-center w-full py-2 px-3 justify-between text-sm font-medium text-gray-700 hover:text-mainBlack'
      )}
    >
      {children}
    </PopoverButton>
  )
}

export default CustomPopoverButton
