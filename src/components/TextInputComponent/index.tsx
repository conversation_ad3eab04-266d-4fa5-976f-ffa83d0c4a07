import { InputHTMLAttributes, ReactNode } from 'react'
import { classNames } from '../../utils'
interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  prefixIcon?: ReactNode
  isError?: boolean
  endfixIcon?: ReactNode
  customClassName?: string
}
export default function TextInputComponent(props: InputProps) {
  const { prefixIcon, customClassName, disabled, endfixIcon } = props
  return (
    <div className="relative z-0 bg-white flex-1 flex items-center rounded-xl w-full">
      {prefixIcon && (
        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          {prefixIcon}
        </div>
      )}

      <input
        className={classNames(
          disabled && 'bg-gray-50',
          `block font-sans flex-1 w-full min-h-9 min-w-[150px] truncate items-center rounded-xl border-0 py-1.5 text-mainBlack ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 text-xs sm:text-sm sm:leading-6`,
          prefixIcon ? 'pl-10' : 'pl-4',
          customClassName && customClassName
        )}
        {...props}
      />
      {endfixIcon && (
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {endfixIcon}
        </div>
      )}
    </div>
  )
}
