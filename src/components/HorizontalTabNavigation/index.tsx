import { NavLink } from 'react-router-dom'
import { classNames } from '../../utils'
import { ITabNavigationLinks } from '../../interfaces'

type Props = {
  tabs: (ITabNavigationLinks | undefined)[]
}

const HorizontalTabNavigation = ({ tabs }: Props) => {
  return (
    <div className="mt-4 border-b border-gray-200 border-solid border-x-0 border-t-0">
      <div className="block">
        <nav className="flex space-x-8">
          {tabs?.map(
            (tab) =>
              tab && (
                <NavLink
                  key={tab?.name}
                  to={tab?.href}
                  aria-current={tab?.current ? 'page' : undefined}
                  className={classNames(
                    tab?.current
                      ? 'border-[#853bac] text-primary border-solid  border-t-0 border-x-0'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                    'whitespace-nowrap border-b-2 pr-1 pb-2 text-sm font-medium '
                  )}
                >
                  {tab?.name}
                </NavLink>
              )
          )}
        </nav>
      </div>
    </div>
  )
}

export default HorizontalTabNavigation
