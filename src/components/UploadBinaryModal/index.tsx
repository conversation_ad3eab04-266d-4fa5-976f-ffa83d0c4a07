import { useEffect, useState } from 'react'
import UploadComponent from '../UploadComponent'
import { Select } from 'antd'
import { OPERATING_SYSTEM } from '../../enum/OperatingSystems'
import SecondaryButton from '../SecondaryButtons'
import PrimaryButton from '../PrimaryButtons'
import { uploadBinaryFile } from '../../api/Agents'
import { toast } from 'react-hot-toast'
import { getErrorMessage } from '../../utils'
import UploadedFileItem from '../UploadedFileItem'

type Props = {
  closeModal: () => void
  setDisableBackdrop: React.Dispatch<React.SetStateAction<boolean>>
}

const UploadBinaryFileModal = ({ closeModal, setDisableBackdrop }: Props) => {
  const OPERATING_SYSTEMS = Object.values(OPERATING_SYSTEM)
  const [files, setFiles] = useState<File[]>([])
  const [selectedOS, setSelectedOS] = useState<OPERATING_SYSTEM>()
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [failedFile, setFailedFile] = useState<string>('')
  const handleUploadBinaryFile = async () => {
    const data = new FormData()
    files.forEach((file) => {
      const uploadedFile = new File(
        [file],
        file.name
          .replace(/\s+/g, '_') // Replace all whitespace with "_"
          .replace(/[^a-zA-Z0-9_.]/g, ''), // Remove all special characters except "_" and "."
        { type: file.type }
      )
      data.append('file', uploadedFile)
    })
    setFailedFile('')
    setIsUploading(true)
    setDisableBackdrop(true)
    try {
      await uploadBinaryFile(
        data,
        selectedOS ?? OPERATING_SYSTEM.DARWIN_ARM64,
        (progress: number) => setUploadProgress(progress)
      )
      toast.success('Your file is uploaded successfully!')
      closeModal()
    } catch (error) {
      setFailedFile(files[0].name)
      toast.error(getErrorMessage(error))
    } finally {
      setIsUploading(false)
      setDisableBackdrop(false)
    }
  }

  useEffect(() => {
    setFailedFile('')
  }, [files])

  return (
    <div>
      <UploadComponent
        loading={isUploading}
        supportedFormats={['application/x-msdownload', '']}
        setSelectedFiles={setFiles}
        selectedFiles={files}
        maxSize={50 * 1024 * 1024} // 50 MB
        maxSizeString="50 MB"
      />
      <p className="text-sm font-semibold mt-4 mb-2">
        Choose an operating system:
      </p>
      <Select
        options={OPERATING_SYSTEMS.map((os) => {
          return {
            label: os,
            value: os
          }
        })}
        placeholder="Select an operating system"
        className="w-full min-h-[40px]"
        onChange={(e) => setSelectedOS(e)}
      />
      {files.map((file) => (
        <UploadedFileItem
          key={file.name}
          file={file}
          failedFile={failedFile}
          isUploading={isUploading}
          uploadProgress={uploadProgress}
          onRemoveFile={() =>
            setFiles((prev) =>
              prev.filter((removedFile) => removedFile.name !== file.name)
            )
          }
        />
      ))}
      <div className="flex items-center py-4 bg-white gap-4 w-full sm:justify-end">
        <SecondaryButton
          isDisabled={isUploading}
          onClick={closeModal}
          className="sm:max-w-[200px]"
        >
          Cancel
        </SecondaryButton>
        <PrimaryButton
          onClick={handleUploadBinaryFile}
          className="sm:max-w-[200px]"
          isLoading={isUploading}
          isDisabled={files.length === 0 || !selectedOS}
        >
          Upload
        </PrimaryButton>
      </div>
    </div>
  )
}

export default UploadBinaryFileModal
