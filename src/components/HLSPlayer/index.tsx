import React, { memo, useEffect, useRef } from 'react'
import Hls from 'hls.js'

interface HlsPlayerProps {
  src: string
  className?: string
  isStreaming?: boolean
  autoPlay?: boolean
}

const HlsPlayer: React.FC<HlsPlayerProps> = ({
  src,
  className,
  isStreaming,
  autoPlay = true
}) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  useEffect(() => {
    const video = videoRef.current
    console.log('.')
    if (Hls.isSupported() && video) {
      const hls = new Hls({
        liveSyncDurationCount: 0,
        autoStartLoad: true
      })
      hls.loadSource(src)
      hls.attachMedia(video)

      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        video.play()
      })

      video.addEventListener('play', () => {
        if (hls.liveSyncPosition && isStreaming) {
          video.currentTime = hls.liveSyncPosition // Seek to the latest segment
        }
      })

      // Cleanup on component unmount
      return () => {
        hls.destroy()
      }
    } else if (video?.canPlayType('application/vnd.apple.mpegurl')) {
      video.src = src
      video.addEventListener('loadedmetadata', () => {
        video.play()
      })
    }
  }, [src])

  return (
    <video
      ref={videoRef}
      autoPlay={autoPlay}
      controls
      height={300}
      width={'100%'}
      className={className}
    />
  )
}

export default memo(HlsPlayer)
