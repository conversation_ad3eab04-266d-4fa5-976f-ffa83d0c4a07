import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  TransitionChild
} from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { ReactNode } from 'react'

type Props = {
  children: ReactNode
  openState: [boolean, React.Dispatch<React.SetStateAction<boolean>>]
}

const DrawerComponent = ({ children, openState }: Props) => {
  const [sidebarOpen, setSidebarOpen] = openState
  return (
    <Dialog
      open={sidebarOpen}
      onClose={setSidebarOpen}
      className="relative z-50 sm:hidden"
    >
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-gray-900/80 transition-opacity duration-300 ease-linear data-[closed]:opacity-0"
      />

      <div className="fixed inset-0 flex">
        <DialogPanel
          transition
          className="relative mr-16 flex min-w-[300px] bg-white w-full max-w-xs flex-1 transform transition duration-300 ease-in-out data-[closed]:-translate-x-full"
        >
          <XMarkIcon
            aria-hidden="true"
            onClick={() => setSidebarOpen(false)}
            className="h-6 w-6 absolute right-4 top-4 cursor-pointer text-white"
          />
          <TransitionChild></TransitionChild>
          {children}
        </DialogPanel>
      </div>
    </Dialog>
  )
}

export default DrawerComponent
