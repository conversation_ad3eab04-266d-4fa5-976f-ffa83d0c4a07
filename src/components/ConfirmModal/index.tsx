import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react'
import PrimaryButton from '../PrimaryButtons'
import SecondaryButton from '../SecondaryButtons'
import { classNames } from '../../utils'
import { PropsWithChildren, ReactNode } from 'react'

type Props = PropsWithChildren & {
  onConfirm: () => void
  text: string | ReactNode
  openState: [boolean, React.Dispatch<React.SetStateAction<boolean>>]
  title: string | ReactNode
  disable?: boolean
  onClose?: () => void
  isOpen?: boolean
}

const ConfirmModal = ({
  onConfirm,
  openState,
  text,
  title,
  disable,
  isOpen,
  children = (
    <p className="text-sm">{text} This action cannot be undone. Confirm?</p>
  )
}: Props) => {
  const [open, setOpen] = openState
  return (
    <Dialog open={isOpen ?? open} onClose={setOpen} className="relative z-10">
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-black bg-opacity-60 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
      />

      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
          <DialogPanel
            transition
            className={classNames(
              'relative transform overflow-auto overflow-x-hidden max-h-[80vh] min-w-[40vw] rounded-lg min-h-fit over bg-white p-6 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-fit  data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95'
            )}
          >
            <div className="w-full">
              <p className="font-bold mb-4 text-xl">{title}</p>
              <div className="w-full h-full flex flex-col line-clamp-2 max-w-[700px]">
                {children}
                <div className="flex items-center ml-auto mt-4 gap-3 w-full sm:w-1/2">
                  <SecondaryButton onClick={() => setOpen(false)}>
                    Cancel
                  </SecondaryButton>
                  <PrimaryButton
                    isLoading={disable}
                    isDisabled={disable}
                    onClick={onConfirm}
                  >
                    Confirm
                  </PrimaryButton>
                </div>
              </div>
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  )
}

export default ConfirmModal
