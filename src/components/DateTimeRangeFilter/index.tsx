import type { TimeRangePickerProps } from 'antd'
import { DatePicker, Space } from 'antd'
import { RangePickerProps } from 'antd/es/date-picker'
import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import { classNames } from '../../utils'
type Props = {
  endDate?: Dayjs | null | undefined
  startDate?: Dayjs | null | undefined
  onDateRangeChange: (dateRange: null | (Dayjs | null)[]) => void
  error?: boolean
}

dayjs.extend(isoWeek)

// all dates of this week
const currentWeekDays = Array.from({ length: 7 }, (_, i) =>
  dayjs().startOf('isoWeek').add(i, 'day')
)

// All dates of last week
const lastWeekDates = Array.from({ length: 7 }, (_, i) =>
  dayjs().startOf('isoWeek').subtract(1, 'week').add(i, 'day')
)

// Number of days in the current month
const daysInMonth = dayjs().daysInMonth()

// All dates in the current month
const currentMonthDates = Array.from({ length: daysInMonth }, (_, i) =>
  dayjs().startOf('month').add(i, 'day')
)

const { RangePicker } = DatePicker

const rangePresets: TimeRangePickerProps['presets'] = [
  {
    label: 'This week',
    value: [currentWeekDays[0], currentWeekDays[6]]
  },
  { label: 'Last week', value: [lastWeekDates[0], lastWeekDates[6]] },
  { label: 'This month', value: [currentMonthDates[0], dayjs()] },
  { label: 'This year', value: [dayjs().startOf('year'), dayjs()] },
  {
    label: 'Last year',
    value: [
      dayjs().subtract(1, 'year').startOf('year'),
      dayjs().subtract(1, 'year').endOf('year')
    ]
  },
  {
    label: 'All time',
    value: [null, null]
  }
]

const DateTimeRangeFilter = ({
  startDate,
  endDate,
  onDateRangeChange,
  error
}: Props) => {
  const onRangeChange = (dates: null | (Dayjs | null)[]) => {
    onDateRangeChange(dates)
  }

  const disabledStartDateTime = (date: Dayjs) => {
    //disable endDate time to not exceeds endDate
    if (!date || !endDate) {
      return {}
    } else {
      const isSameDay = date.day() === endDate.day() // if both Date are the same
      const isSameHour = date.hour() === endDate.hour() //if both hours are the same

      return {
        disabledHours: () =>
          isSameDay
            ? Array.from(
                { length: 24 - (endDate.hour() + 1) },
                (_, i) => i + (endDate.hour() + 1)
              ) // disable hours from endDate.hour() to 24
            : [],
        disabledMinutes: () =>
          isSameDay && isSameHour
            ? Array.from(
                { length: 60 - (endDate.minute() + 1) },
                (_, i) => i + (endDate.minute() + 1)
              ) // disable minutes from endDate.hour() to 60
            : []
        // Disable minutes 30-34
      }
    }
  }

  const disabledEndDateTime = (date: Dayjs) => {
    // disable endDate time to not below startDate
    if (date && startDate) {
      const isSameDay = date.day() === startDate.day() // if both Date are the same
      const isSameHour = date.hour() === startDate.hour() //if both hours are the same
      return {
        disabledHours: () =>
          isSameDay
            ? Array.from({ length: startDate.hour() + 1 }, (_, i) => i - 1)
            : [], // Disable hours from 0 to startDate.hoour()
        disabledMinutes: () =>
          isSameDay && isSameHour
            ? Array.from({ length: startDate.minute() + 1 }, (_, i) => i - 1) // Disable minutes from 0 to startDate.minute()
            : []
      }
    } else {
      return {}
    }
  }

  const disabledRangeTime: RangePickerProps['disabledTime'] = (date, type) => {
    if (type === 'start') {
      return disabledStartDateTime(date)
    }
    return disabledEndDateTime(date)
  }

  return (
    <Space direction="vertical" size={12}>
      <RangePicker
        presets={rangePresets}
        className={classNames('min-h-9', error && 'border border-red-400')}
        showTime
        disabledTime={disabledRangeTime}
        allowEmpty
        value={[startDate, endDate]}
        inputReadOnly={false}
        format={{
          format: 'YYYY/MM/DD HH:mm',
          type: 'mask'
        }}
        needConfirm={false}
        onChange={onRangeChange}
      />
    </Space>
  )
}

export default DateTimeRangeFilter
