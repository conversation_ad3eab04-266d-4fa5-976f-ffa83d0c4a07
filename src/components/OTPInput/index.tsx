import React, { useEffect, useRef } from 'react'
type Props = {
  otpState: [string[], React.Dispatch<React.SetStateAction<string[]>>]
  onOTPFilled: () => void
}
const OTPInput = ({ otpState, onOTPFilled }: Props) => {
  const [otp, setOtp] = otpState
  const inputRefs = useRef<HTMLInputElement[]>([])
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const { value } = e.target
    if (/^[0-9]$/.test(value)) {
      const newOtp = [...otp]
      newOtp[index] = value
      setOtp(newOtp)

      // Move to the next input field if available
      if (index < 5 && value) {
        inputRefs.current[index + 1].focus()
      }
    } else if (value === '') {
      const newOtp = [...otp]
      newOtp[index] = ''
      setOtp(newOtp)
    }
  }

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === 'Backspace' && otp[index] === '') {
      if (index > 0) {
        inputRefs.current[index - 1].focus()
      }
    }
  }
  useEffect(() => {
    if (otp.every((otpItem) => otpItem !== '')) {
      onOTPFilled()
    }
  }, [otp])

  return (
    <div className="flex justify-center min-w-full space-x-4">
      {otp.map((digit, index) => (
        <input
          key={index}
          type="text"
          autoFocus={index === 0}
          value={digit}
          maxLength={1}
          className="w-1/6 max-w-20 aspect-square flex-1 bg-[#F3F4F6] text-center text-xl border border-solid border-gray-300 rounded-xl focus:outline-none focus:ring-1 focus:ring-[#843BAC]"
          onChange={(e) => handleChange(e, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          ref={(el) => (inputRefs.current[index] = el!)}
        />
      ))}
    </div>
  )
}

export default OTPInput
