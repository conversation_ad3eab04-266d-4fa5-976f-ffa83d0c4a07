import React, { useEffect, useRef } from 'react'
import flvjs from 'flv.js'

interface FlvPlayerProps {
  videoUrl: string
}

const ReactFlvPlayer: React.FC<FlvPlayerProps> = ({ videoUrl }) => {
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const flvPlayerRef = useRef<flvjs.Player | null>(null)

  useEffect(() => {
    // Initialize the player if supported and video URL is provided
    if (flvjs.isSupported() && videoUrl && videoRef.current) {
      flvPlayerRef.current = flvjs.createPlayer(
        {
          type: 'flv',
          url: videoUrl
        },
        {
          enableStashBuffer: true,
          autoCleanupSourceBuffer: true,
          fixAudioTimestampGap: true
        }
      )

      if (flvPlayerRef.current) {
        flvPlayerRef.current.attachMediaElement(videoRef.current)
        flvPlayerRef.current.load()

        // Event listeners for loading state and errors
        flvPlayerRef.current.on('loadingstatechange', () => {})
        flvPlayerRef.current.on('playing', () => {})
        flvPlayerRef.current.on('error', () => {})

        flvPlayerRef.current.play()
      }
    }

    // Cleanup function to destroy the player on component unmount
    return () => {
      if (flvPlayerRef.current) {
        flvPlayerRef.current.pause()
        flvPlayerRef.current.unload()
        flvPlayerRef.current.detachMediaElement()

        flvPlayerRef.current.destroy()
        flvPlayerRef.current = null
      }
    }
  }, [videoUrl])

  return (
    <div>
      <video ref={videoRef} controls style={{ width: '100%' }} />
    </div>
  )
}

export default ReactFlvPlayer
