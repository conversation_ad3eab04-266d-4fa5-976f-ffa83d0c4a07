import {
  Combobox,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions
} from '@headlessui/react'
import { MagnifyingGlassIcon } from '@heroicons/react/20/solid'
import NoDataComponent from '../NoDataComponent'
import { IOption } from '../../interfaces'
import { ReactNode, useRef } from 'react'
import { LoaderIcon } from 'react-hot-toast'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { classNames } from '../../utils'

type Props = {
  option: IOption[]
  onSearch: (search: string) => void
  loading?: boolean
  render?: (item: IOption) => ReactNode
  placeholder?: string
  withIcon?: boolean
  onSelect: (item: IOption) => void
  searchText: string
  isError?: boolean
  onClearText?: () => void
}

const CommandPaletteComponent = ({
  loading,
  option,
  onSearch,
  placeholder,
  render,
  onSelect,
  searchText,
  isError,
  onClearText = () => {}
}: Props) => {
  const inputRef = useRef<HTMLInputElement | null>(null)
  return (
    <div className="!relative w-full min-w-full">
      <Combobox onChange={(e: IOption) => e && onSelect(e)}>
        <div className="relative">
          <MagnifyingGlassIcon
            className="pointer-events-none absolute left-4 top-2.5 size-5 stroke-gray-300"
            aria-hidden="true"
          />
          <ComboboxInput
            ref={inputRef}
            autoFocus
            className={classNames(
              'h-10 w-full border border-solid rounded-lg bg-transparent px-11 text-mainBlack placeholder:text-gray-400 focus:ring-0 sm:text-sm',
              isError ? 'border-red-400' : 'border-gray-300'
            )}
            placeholder={placeholder ?? 'Search...'}
            displayValue={() => searchText}
            onChange={(event) => onSearch(event.target.value)}
            onBlur={(e) => e.preventDefault()}
          />
          {inputRef.current?.value !== '' && (
            <XMarkIcon
              onClick={() => {
                if (inputRef.current) {
                  inputRef.current.value = ''
                }
                onClearText()
              }}
              cursor={'pointer'}
              className="absolute stroke-gray-400 right-4 top-2.5 size-5 text-gray-400"
            />
          )}
        </div>

        {searchText?.length > 0 && option?.length > 0 && loading && (
          <ComboboxOptions
            transition
            anchor="bottom start"
            className="max-h-72 min-h-[40px] flex justify-center min-w-[400px] scroll-py-2 z-50 bg-white shadow-lg overflow-y-auto py-2 text-sm text-gray-800"
          >
            <LoaderIcon className="mt-2" />
          </ComboboxOptions>
        )}
        {searchText?.length > 0 && option?.length > 0 && !loading && (
          <ComboboxOptions
            transition
            anchor="bottom start"
            className="!max-h-60 scroll-py-2 z-50 bg-white shadow-lg overflow-y-auto py-2 text-sm text-gray-800"
          >
            {option?.map((item) => (
              <ComboboxOption
                key={item.value}
                value={item}
                className="cursor-default flex flex-col gap-2 hover:cursor-pointer group select-none px-4 py-2 data-[focus]:bg-indigo-600 data-[focus]:text-white data-[focus]:outline-none"
              >
                {render ? render(item) : item.label}
              </ComboboxOption>
            ))}
          </ComboboxOptions>
        )}
        {option?.length === 0 && searchText?.length > 0 && (
          <ComboboxOptions
            anchor="bottom start"
            transition
            className="min-h-72 flex items-center shadow-lg scroll-py-2 min-w-[400px] bg-white z-50 overflow-y-auto py-2 text-sm text-gray-800"
          >
            <NoDataComponent text="No users found, please select a group first" />
          </ComboboxOptions>
        )}
      </Combobox>
    </div>
  )
}

export default CommandPaletteComponent
