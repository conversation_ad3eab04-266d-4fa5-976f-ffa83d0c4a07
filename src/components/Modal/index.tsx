import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react'
import { ReactNode, useState } from 'react'
import { classNames } from '../../utils'
import ConfirmModal from '../ConfirmModal'
type Props = {
  openState: [boolean, (param: boolean) => void]
  children: ReactNode
  className?: string
  title: ReactNode
  disableBackdropClick?: boolean
  isModalDirty?: boolean
}
export default function CustomModal({
  openState,
  children,
  className,
  title,
  disableBackdropClick,
  isModalDirty
}: Props) {
  const [open, setOpen] = openState
  const [openConfirmCloseModal, setOpenConfirmCloseModal] =
    useState<boolean>(false)

  const onCloseDialog = (open: boolean) => {
    if (disableBackdropClick) {
      return
    }
    if (isModalDirty) {
      setOpenConfirmCloseModal(true)
      return
    } else setOpen(open)
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={onCloseDialog}
        className="relative z-10 min-h-full"
      >
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-black bg-opacity-60 min-h-full transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
        />

        <div className="fixed inset-0 z-10 w-screen h-full">
          <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
            <DialogPanel
              transition
              className={classNames(
                className && className,
                'relative transform h-fit flex flex-col min-w-[90vw] md:min-w-[50vw] max-w-[20vw] max-h-[80vh] overflow-auto rounded-lg bg-white px-6 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-fit  data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95'
              )}
            >
              {title !== '' && (
                <div className="font-bold py-4 text-xl w-full bg-white z-20 sticky top-0">
                  {title}
                </div>
              )}
              {children}
            </DialogPanel>
          </div>
        </div>
      </Dialog>
      <ConfirmModal
        onConfirm={() => {
          setOpen(false)
          setOpenConfirmCloseModal(false)
        }}
        text={
          'You have unsaved changes that will be lost if you leave this page. Do you want to continue?'
        }
        openState={[openConfirmCloseModal, setOpenConfirmCloseModal]}
        title={'Unsaved Changes'}
      ></ConfirmModal>
    </>
  )
}
