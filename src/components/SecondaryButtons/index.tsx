import { ReactNode } from 'react'
import { classNames } from '../../utils'

type Props = {
  className?: string
  children: ReactNode
  onClick?: () => void
  isDisabled?: boolean
  backgroundColor?: string
}

const SecondaryButton = ({
  className,
  children,
  onClick,
  isDisabled,
  backgroundColor
}: Props) => {
  return (
    <button
      type="button"
      style={{ backgroundColor: backgroundColor ?? 'white' }}
      disabled={isDisabled}
      className={classNames(
        `bg-white inline-flex cursor-pointer gap-3 border-[#E3E3E3] group border border-solid w-full justify-center text-[#797979] rounded-3xl px-5 py-2 items-center text-sm font-semibold hover:opacity-50 ${
          className && className
        }`,
        isDisabled && 'bg-opacity-30 pointer-events-none'
      )}
      onClick={() => onClick && onClick()}
    >
      {children}
    </button>
  )
}

export default SecondaryButton
