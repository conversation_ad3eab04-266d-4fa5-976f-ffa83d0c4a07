import React, { useRef, useState } from 'react'
import { DocumentArrowUpIcon } from '@heroicons/react/24/solid'
import toast from 'react-hot-toast'
type Props = {
  loading?: boolean
  setSelectedFiles: (files: File[]) => void
  selectedFiles: File[]
  multiple?: boolean
  supportedFormats?: string[]
  maxSize?: number
  maxSizeString?: string
}

const UploadComponent = ({
  loading,
  setSelectedFiles,
  selectedFiles,
  multiple,
  supportedFormats,
  maxSize,
  maxSizeString
}: Props) => {
  const [dragging, setDragging] = useState<boolean>(false)
  const documentRef = useRef<HTMLInputElement | null>(null)
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setDragging(true)
  }

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setDragging(true)
  }

  const handleDragLeave = () => {
    setDragging(false)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setDragging(false)
    handleFileChange(Object.values(e.dataTransfer.files))
  }
  const handleFileChange = (files: File[]) => {
    if ([selectedFiles].flat()?.concat(files).length > 10) {
      toast.error('Maximum number of files per upload session is 10!', {
        position: 'top-right'
      })
    } else {
      files.forEach((file) => {
        const isFileDuplicated = selectedFiles?.some(
          (selected) => selected.name === file.name
        )

        const isFormatSupported =
          !supportedFormats?.includes(file.type) && supportedFormats // if we pass in the formats array and if it includes the file type

        if (maxSize && file.size > maxSize) {
          toast.error(
            'File size exceeds the maximum allowed size. Maximum allowed size: ' +
              maxSizeString
          )
        } else if (isFormatSupported) {
          toast.error('File format not supported')
        } else if (isFileDuplicated) {
          toast.error(`File ${file.name} is duplicated`, {
            position: 'top-right'
          })
        } else {
          setSelectedFiles(multiple ? [file, ...selectedFiles] : [file])
        }
      })
    }
  }

  return (
    <div
      className={`mt-2 flex justify-center hover:bg-gray-50 cursor-pointer rounded-lg border border-dashed border-gray-900/25 px-6 py-10 ${
        dragging ? 'bg-gray-100' : ''
      }`}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => {
        if (documentRef.current) {
          documentRef.current.value = ''
          documentRef.current?.click()
        }
      }}
    >
      <div className="text-center w-full">
        <DocumentArrowUpIcon
          className="mx-auto h-12 w-12 text-gray-300 fill-[#7A7A7A]"
          aria-hidden="true"
        />
        <div className="mt-4 flex text-sm leading-6 text-gray-600 justify-center">
          <span className="font-semibold text-primary">
            Upload a file{' '}
            <span className="text-gray-400">or drag and drop</span>{' '}
          </span>
          <input
            type="file"
            disabled={loading}
            multiple={multiple}
            ref={documentRef}
            className="sr-only hidden"
            onChange={(event) => {
              event.preventDefault()
              handleFileChange(Object.values(event.target.files ?? {}))
            }}
            onClick={() => {
              if (documentRef.current) {
                documentRef.current.value = ''
              }
            }}
          />
        </div>
        {maxSize && (
          <p className="text-xs/5 text-gray-600">Files up to {maxSizeString}</p>
        )}
      </div>
    </div>
  )
}

export default UploadComponent
