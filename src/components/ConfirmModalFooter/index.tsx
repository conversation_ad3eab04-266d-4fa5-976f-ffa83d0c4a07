import PrimaryButton from '../PrimaryButtons'
import SecondaryButton from '../SecondaryButtons'

type Props = {
  onCancel: () => void
  onConfirm: () => void
  isLoading: boolean
  buttonText?: string
}

const ConfirmModalFooter = ({
  onCancel,
  onConfirm,
  isLoading,
  buttonText = 'Delete'
}: Props) => {
  return (
    <div className="flex w-full gap-2 bg-white py-4">
      <SecondaryButton onClick={onCancel}>Cancel</SecondaryButton>
      <PrimaryButton
        isDisabled={isLoading}
        isLoading={isLoading}
        onClick={onConfirm}
        className="bg-red-400"
      >
        {buttonText}
      </PrimaryButton>
    </div>
  )
}

export default ConfirmModalFooter
