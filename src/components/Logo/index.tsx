import { classNames } from '../../utils'

interface LogoProps {
  open?: boolean
}

export const AppLogo = ({ open }: LogoProps) => {
  return (
    <img
      src="/htx-logo-white.png"
      className={classNames(
        'z-50 mx-auto mt-4',
        open === false
          ? 'min-w-[40px] w-[40px] min-h-[40px]'
          : 'min-w-[80px] w-[80px] min-h-[80px]'
      )}
      alt="App Logo"
    />
  )
}

export const LandingPageLogo = ({ open }: LogoProps) => {
  // You can adjust the logic and source for this logo as needed
  return (
    <img
      src="/vite.png"
      className={classNames(
        'mx-auto mt-4',
        open === false
          ? 'min-w-[50px] w-[50px] min-h-[50px]'
          : 'min-w-[100px] w-[100px] min-h-[100px]'
      )}
      alt="Landing Page Logo"
    />
  )
}

