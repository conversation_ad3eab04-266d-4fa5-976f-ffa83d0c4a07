import { IOption } from '../../interfaces'
import {
  removeQueryEventCode,
  removeQueryObjectLabel,
  selectAnalyticResultQuery
} from '../../stores/Reducers/analyticsReducer'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import TagComponent from '../Tags'

type Props = {
  onRemoveFilter: (item: IOption) => void
  clearAllFilter: () => void
  listActiveFilters: IOption[]
}

const ActiveFiltersComponent = ({
  onRemoveFilter,
  clearAllFilter,
  listActiveFilters
}: Props) => {
  const dispatch = useAppDispatch()
  const queryParams = useAppSelector(selectAnalyticResultQuery)

  return (
    <div className="bg-gray-100 w-full flex justify-between items-center">
      <div className="max-w-7xl px-4 py-3 sm:flex sm:items-center sm:px-6 lg:px-8">
        <h3 className="text-sm font-medium text-gray-500">
          Filters
          <span className="sr-only">, active</span>
        </h3>

        <div
          aria-hidden="true"
          className="hidden h-5 w-px bg-gray-300 sm:ml-4 sm:block"
        />

        <div className="mt-2 sm:ml-4 sm:mt-0">
          <div className="-m-1 flex gap-2 overflow-x-auto flex-wrap items-center">
            {queryParams.label.map(
              (label) =>
                label !== '' && (
                  <TagComponent
                    key={label}
                    text={`Object: ${label}`}
                    textColor="#6B7280"
                    backgroundColor="white"
                    removable
                    onRemove={() => dispatch(removeQueryObjectLabel(label))}
                  />
                )
            )}
            {queryParams.event_code.map(
              (event_code) =>
                event_code !== '' && (
                  <TagComponent
                    key={event_code}
                    text={`Event code: ${event_code}`}
                    textColor="#6B7280"
                    backgroundColor="white"
                    removable
                    onRemove={() => dispatch(removeQueryEventCode(event_code))}
                  />
                )
            )}
            {listActiveFilters.map(
              (activeFilter) =>
                activeFilter.label !== '' && (
                  <TagComponent
                    key={activeFilter.value}
                    text={activeFilter.label}
                    textColor="#6B7280"
                    backgroundColor="white"
                    removable
                    onRemove={() => onRemoveFilter(activeFilter)}
                  />
                )
            )}
          </div>
        </div>
      </div>
      <p
        onClick={() => {
          clearAllFilter()
        }}
        className="px-4 min-w-fit pb-4 sm:pb-0 cursor-pointer text-xs text-gray-500 underline"
      >
        Clear all
      </p>
    </div>
  )
}

export default ActiveFiltersComponent
