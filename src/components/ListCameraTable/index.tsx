import { Skeleton } from 'antd'
import NoDataComponent from '../NoDataComponent'
import { classNames } from '../../utils'
import { ICameras } from '../../models/camera'
import CameraItemComponent from './camera-item-component'

type Props = {
  page: number
  listCamera: ICameras[]
  loading: boolean
  refetchAgentCamera?: () => void // refetch camera list when edited via agent management
  refetchCameraList?: () => void // refetch camera list when edited in camera management
  refetchCameraListAfterDelete: () => void // refetch camera list when deleted in camera management
}
const ListCameraTable = ({
  listCamera,
  loading,
  refetchAgentCamera = () => {},
  refetchCameraList,
  refetchCameraListAfterDelete
}: Props) => {
  return (
    <>
      <div className="min-h-[300px] w-full flex-1 overflow-auto">
        <div className="-my-2 overflow-x-auto w-full h-full">
          <div className="min-w-full py-2 align-middle">
            <table className="min-w-full relative table w-full divide-gray-300">
              <thead className={classNames('sticky top-0 z-[1] bg-gray-50')}>
                <tr>
                  <th
                    scope="col"
                    className="py-3.5 px-3 text-center text-sm font-semibold text-mainBlack"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    Camera
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 min-w-fit w-fit text-left text-sm font-semibold text-mainBlack"
                  >
                    RTSP
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    Project
                  </th>
                  <th
                    scope="col"
                    className="px-3 pl-6 py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    Tag
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    VA Plugin
                  </th>
                  <th
                    scope="col"
                    className="py-3.5 px-3.5 text-center text-sm font-semibold text-mainBlack"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white overflow-auto">
                {loading ? (
                  <tr key={''}>
                    <td colSpan={7}>
                      <Skeleton active className="my-4" />
                      <Skeleton active />
                      <Skeleton active className="my-4" />
                      <Skeleton active />
                    </td>
                  </tr>
                ) : listCamera.length > 0 ? (
                  listCamera.map((camera) => (
                    <CameraItemComponent
                      refetchCameraListAfterDelete={
                        refetchCameraListAfterDelete
                      }
                      key={camera.id}
                      refetchAgentCamera={refetchAgentCamera}
                      refetchCameraList={refetchCameraList}
                      camera={camera}
                    />
                  ))
                ) : (
                  <tr className="h-[300px]">
                    <td colSpan={7}>
                      <NoDataComponent />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  )
}

export default ListCameraTable
