import { useRef, useState } from 'react'
import { CameraTags, ICameras, UpdateCameraInput } from '../../models/camera'
import StatusBadgeComponent from '../StatusBadge'
import EditIcon from '../../assets/svgs/EditIcon'
import CustomModal from '../Modal'
import CameraDetailComponent from '../CameraDetails'
import TagComponent from '../Tags'
import { deleteCamera, editCamera } from '../../api/Camera'
import toast from 'react-hot-toast'
import { getErrorMessage } from '../../utils'
import ConfirmDeleteCameraModal from './confirm-delete-camera-modal'
import TrashIcon from '../../assets/svgs/TrashIcon'
import AssignVAModal from '../../pages/Recording/RecordingItem/AssignVAModal'
import { AssignedVA } from '../../models/recodings'
import ConfirmModalFooter from '../ConfirmModalFooter'
import DeleteModalHeader from '../DeleteModalHeader'

type Props = {
  camera: ICameras
  refetchAgentCamera?: () => void // refetch camera list when edited via agent management
  refetchCameraList?: () => void // refetch camera list when edited in camera management
  refetchCameraListAfterDelete: () => void // refetch camera list when deleted in camera management
}

const MAXIMUM_DISPLAYED_TAGS_INDEX = 2 //limit the number of tags show: 3 (0,1,2)
const CameraItemComponent = ({
  camera,
  refetchCameraListAfterDelete,
  refetchAgentCamera,
  refetchCameraList
}: Props) => {
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState<boolean>(false)
  const [isDeleting, setIsDeleting] = useState<boolean>(false)
  const [isEditFormDirty, setIsEditFormDirty] = useState<boolean>(false)
  const [openRunVAModal, setOpenRunVAModal] = useState<boolean>(false)
  const selectedCamera = useRef<ICameras>()
  const handleOpenEditModal = (camera: ICameras) => {
    selectedCamera.current = camera
    setEditModalOpen(true)
  }

  const handleOpenDeleteModal = (camera: ICameras) => {
    selectedCamera.current = camera
    setDeleteModalOpen(true)
  }

  const numberOfUnshowedTags = (listTags: CameraTags[]) => {
    return listTags.filter((_, index) => index >= MAXIMUM_DISPLAYED_TAGS_INDEX)
      .length
  }
  const displayListTag = (camera: ICameras) =>
    camera.tags.map((tag, index) => {
      if (index < MAXIMUM_DISPLAYED_TAGS_INDEX) {
        // limit the number of tags show: 2
        return (
          <TagComponent
            key={tag.id}
            text={tag.name ?? ''}
            textColor="#272727"
            border="1px solid #E3E3E3"
            backgroundColor="white"
          />
        )
      }
    })
  const handleDeleteCamera = async () => {
    setIsDeleting(true)

    try {
      await deleteCamera(selectedCamera?.current?.id ?? '')
      toast.success(
        `Successfully deleted Camera ${selectedCamera?.current?.name}`
      )
      setDeleteModalOpen(false)
      refetchCameraListAfterDelete()
      // refetchCameraList && refetchCameraList()
    } catch (error) {
      toast.error(() => getErrorMessage(error))
    } finally {
      setIsDeleting(false)
    }
  }

  //compare 2 VA_plugins array to return the va got added/removed

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const findExtraItems = (originalArray: any[], compareArray: any[]) => {
    return originalArray.filter(
      (item1) => !compareArray.some((item2) => item2.id === item1.id)
    )
  }

  const onConfirmAssignVA = async (selectedVA: AssignedVA[]) => {
    const editedVa = selectedVA.map((va) => {
      return {
        id: va.id,
        name: va.name
      }
    })
    const unassignedVA = findExtraItems(
      camera.va_plugins.map((va) => {
        return {
          name: va.name,
          id: va.id
        }
      }) ?? [],
      editedVa
    )
    const assignedVA = findExtraItems(
      editedVa,
      camera.va_plugins.map((va) => {
        return {
          name: va.name,
          id: va.id
        }
      }) ?? []
    )
    const updateCameraData: UpdateCameraInput = {
      agent_id: camera.agent.id,
      name: camera.name,
      project_id: camera.project.id,
      notes: camera.notes,
      remove_tag_ids: undefined,
      remove_va_plugin_ids:
        unassignedVA.length > 0 ? unassignedVA.map((va) => va.id) : undefined,
      rtsp: camera.rtsp.trim(),
      tags: undefined,
      va_plugin_ids:
        assignedVA.length > 0 ? assignedVA.map((va) => va.id) : undefined,
      recording: camera.recording ? 'On' : 'Off'
    }
    try {
      await editCamera(updateCameraData, camera.id)
      toast.success('Your changes are saved!', {
        position: 'top-right'
      })
      refetchCameraList?.()
      setOpenRunVAModal(false)
    } catch (error) {
      if (
        error &&
        typeof error === 'object' &&
        'status' in error &&
        error?.status === 403
      ) {
        toast.error('You do not have permission on this camera')
      } else {
        toast.error(getErrorMessage(error), { position: 'top-right' })
      }
    }
  }

  return (
    <>
      <tr key={camera.id} className="even:bg-gray-50">
        <td className="whitespace-nowrap flex items-center justify-center py-4 pt-5 px-3 text-sm font-medium text-mainBlack">
          <StatusBadgeComponent type={camera.status.toLocaleLowerCase()} />
        </td>
        <td className="whitespace-nowrap min-w-fit w-fit max-w-[150px] px-3 py-4 text-sm truncate text-mainBlack">
          {camera.name}
        </td>
        <td className="whitespace-nowrap truncate min-w-fit w-fit max-w-[150px] px-3 py-4 text-sm text-mainBlack">
          {camera.rtsp}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-mainBlack">
          {camera?.project?.name}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-mainBlack">
          <div
            className="flex gap-2 items-center"
            onClick={() => handleOpenEditModal(camera)}
          >
            {displayListTag(camera)}
            {numberOfUnshowedTags(camera.tags) > 0 && (
              <p className="bg-[#F4F4F4] cursor-pointer text-xs p-2 rounded-full">
                {'+' + numberOfUnshowedTags(camera?.tags)}
              </p>
            )}
          </div>
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-mainBlack">
          <span
            onClick={() => setOpenRunVAModal(true)}
            className="text-blue-400 font-medium underline cursor-pointer"
          >
            View
          </span>
        </td>
        <td className="relative whitespace-nowrap py-4 px-3.5 text-center text-sm font-medium ">
          <div className="flex justify-center items-center w-full gap-2">
            <EditIcon
              onClick={() => handleOpenEditModal(camera)}
              className="cursor-pointer"
            />
            <TrashIcon
              onClick={() => handleOpenDeleteModal(camera)}
              cursor={'pointer'}
            />
          </div>
        </td>
      </tr>
      <CustomModal
        openState={[deleteModalOpen, setDeleteModalOpen]}
        title={
          <DeleteModalHeader title="Delete Camera" description={camera.name} />
        }
        className="md:!min-w-[28rem] md:!max-w-md max-h-[90vh]"
      >
        <ConfirmDeleteCameraModal
          assignedPlugins={selectedCamera.current?.va_plugins}
          selectedCamera={selectedCamera.current}
        />
        <ConfirmModalFooter
          onCancel={() => setDeleteModalOpen(false)}
          onConfirm={handleDeleteCamera}
          isLoading={isDeleting}
        />
      </CustomModal>

      <CustomModal
        className="min-w-[50vw] max-w-[50vw]"
        title="Edit Camera"
        isModalDirty={isEditFormDirty}
        openState={[editModalOpen, setEditModalOpen]}
      >
        <CameraDetailComponent
          setIsFormDirty={setIsEditFormDirty}
          isFormDirty={isEditFormDirty}
          setModalOpen={setEditModalOpen}
          defaultValue={selectedCamera.current}
          refetchAgentCamera={refetchAgentCamera}
          modalOpen={editModalOpen}
          refetchListCamera={refetchCameraList}
        />
      </CustomModal>
      <CustomModal
        openState={[openRunVAModal, setOpenRunVAModal]}
        title={'Assign VA Plugins'}
      >
        <AssignVAModal
          onCancel={() => setOpenRunVAModal(false)}
          vaPlugins={camera.va_plugins ?? []}
          onConfirmAssignVA={onConfirmAssignVA}
        />
      </CustomModal>
    </>
  )
}

export default CameraItemComponent
