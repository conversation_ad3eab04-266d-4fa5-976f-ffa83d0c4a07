import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { ICameras } from '../../models/camera'
import CameraIcon from '../../assets/svgs/CameraIcon'
import RecordingIcon from '../../assets/svgs/RecordingIcon'
import VideoIcon from '../../assets/svgs/VideoIcon'
import { AssignedVA } from '../../models/recodings'
import { NavLink } from 'react-router-dom'

import { ROUTE_PATH } from '../../enum/RoutePath'
import NoDataComponent from '../NoDataComponent'

type Props = {
  selectedCamera?: ICameras
  assignedPlugins?: AssignedVA[]
}

const ConfirmDeleteCameraModal = ({
  selectedCamera,
  assignedPlugins = []
}: Props) => {
  return (
    <div className="pr-2 flex-1 h-full overflow-auto flex flex-col gap-4">
      <div className="border-red-200 gap-3 bg-red-50 p-4 flex border border-solid rounded-lg">
        <ExclamationTriangleIcon className="min-h-5 min-w-5 max-h-5 max-w-5 stroke-red-600 mt-1" />
        <p className="text-red-800 text-sm font-medium">
          <strong className="text-red-800 font-bold">Warning:</strong> Deleting
          this camera is an irreversible action and will result in the
          following:
        </p>
      </div>
      <div className="flex items-start gap-3">
        <CameraIcon className="h-5 w-5 text-gray-500 mt-0.5" />
        <div>
          <p className="font-semibold text-sm">
            Camera: {selectedCamera?.name}
          </p>
          <p className="text-sm text-gray-600 font-medium">
            Will be permanently removed from the system
          </p>
        </div>
      </div>
      <div className="flex items-start gap-3">
        <RecordingIcon className="min-h-5 min-w-5 text-gray-500 mt-0.5" />
        <div>
          <p className="font-semibold text-sm">Recording Files</p>
          <p className="text-sm text-gray-600 font-medium">
            Recordings will remain accessible on the recordings page
          </p>
        </div>
      </div>
      <div className="flex items-start gap-3 flex-1">
        <VideoIcon className="h-5 w-5 text-gray-500 mt-0.5" />
        <div className="flex-1 h-full overflow-auto flex flex-col">
          <p className="font-semibold text-sm">Video Analytics Plugins</p>
          <p className="text-sm text-gray-600 font-medium mb-2">
            The following assignments to the VA plugins will be removed:
          </p>
          <div className="space-y-1 h-full overflow-auto flex-1">
            {assignedPlugins.length > 0 ? (
              assignedPlugins.map((plugin) => (
                <div
                  key={plugin.id}
                  className="flex items-center justify-between bg-gray-50 rounded p-2 px-4"
                >
                  <span className="text-sm font-medium">{plugin.name}</span>
                  <NavLink
                    className={'text-xs underline text-blue-400'}
                    target="_blank"
                    to={`${ROUTE_PATH.VideoAnalytics}/${plugin.id}`}
                  >
                    View analytics
                  </NavLink>
                </div>
              ))
            ) : (
              <NoDataComponent />
            )}
          </div>
        </div>
      </div>
      <div className="h-[1px] border-solid border-0 border-b border-b-gray-200"></div>
      <div className="border-amber-200 items-center gap-3 bg-amber-50 p-4 flex border border-solid rounded-lg">
        <ExclamationTriangleIcon className="min-h-5 min-w-5 max-h-5 max-w-5 stroke-amber-600 mt-1" />
        <p className="text-amber-800 text-sm font-medium">
          This action{' '}
          <strong className="text-amber-800 font-bold">cannot be undone</strong>
          .
        </p>
      </div>
    </div>
  )
}

export default ConfirmDeleteCameraModal
