import { Select } from 'antd'
import { IOption } from '../../interfaces'
import { FILTER_TYPES } from '../../enum/FilterTypes'
import { classNames } from '../../utils'

type Props = {
  filters: IOption[]
  selectedFilter: FILTER_TYPES
  setSelectedFilter: (e: FILTER_TYPES) => void
  renderSelectedFilterNumber: (type: FILTER_TYPES) => number
}

const TabNavigationComponent = ({
  filters,
  selectedFilter,
  setSelectedFilter,
  renderSelectedFilterNumber
}: Props) => {
  return (
    <>
      <Select
        options={filters}
        className="w-full sm:hidden min-h-[40px]"
        placeholder="Select filter type"
        value={selectedFilter}
        onChange={(e) => setSelectedFilter(e)}
      />
      <div className="hidden sm:block min-w-[150px] lg:min-w-[200px] w-1/5">
        <nav aria-label="Tabs" className="flex items-center flex-col gap-4">
          {filters.map((tab) => (
            <div
              onClick={() => setSelectedFilter(tab.value as FILTER_TYPES)}
              key={tab.label}
              className={classNames(
                tab.value === selectedFilter
                  ? 'bg-indigo-100'
                  : ' hover:text-gray-700',
                'rounded-md group cursor-pointer items-center flex w-full justify-between gap-2 px-3 py-2 text-sm font-medium'
              )}
            >
              <p
                className={classNames(
                  'w-fit',
                  tab.value === selectedFilter
                    ? 'text-primary'
                    : 'text-gray-500 group-hover:text-gray-700'
                )}
              >
                {tab.label}
              </p>
              {renderSelectedFilterNumber(tab.value as FILTER_TYPES) > 0 && (
                <span className="w-[22px] text-center text-xs flex justify-center items-center text-primary h-[22px] rounded-full border border-[#843BAC] border-solid">
                  {renderSelectedFilterNumber(tab.value as FILTER_TYPES)}
                </span>
              )}
            </div>
          ))}
        </nav>
      </div>
    </>
  )
}

export default TabNavigationComponent
