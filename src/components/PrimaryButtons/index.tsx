import { ReactNode } from 'react'
import { classNames } from '../../utils'
import { LoaderIcon } from 'react-hot-toast'

type Props = {
  className?: string
  children: ReactNode
  onClick?: () => void
  isDisabled?: boolean
  isLoading?: boolean
  type?: 'button' | 'reset' | 'submit'
}

const PrimaryButton = ({
  className,
  children,
  onClick,
  isDisabled,
  type,
  isLoading
}: Props) => {
  return (
    <button
      name="button"
      type={type ?? 'button'}
      disabled={isDisabled || isLoading}
      className={classNames(
        'bg-primary text-white font-sans font-medium gap-3 inline-flex cursor-pointer border-none w-full justify-center rounded-3xl px-5 py-2 items-center text-sm leading-[22px] hover:opacity-70 hover:text-white',
        className && className,
        isDisabled
          ? 'bg-opacity-30 pointer-events-none'
          : 'opacity-100 pointer-events-auto',
        isLoading && 'bg-opacity-30 pointer-events-none'
      )}
      onClick={() => onClick && onClick()}
    >
      {children}
      {isLoading && <LoaderIcon />}
    </button>
  )
}

export default PrimaryButton
