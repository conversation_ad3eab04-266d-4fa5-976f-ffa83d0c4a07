import { UserIcon, XMarkIcon } from '@heroicons/react/24/outline'
import PrimaryButton from '../PrimaryButtons'
import { ArrowUturnRightIcon } from '@heroicons/react/24/solid'
import { Transition } from '@headlessui/react'
import toast, { Toast } from 'react-hot-toast'
type Props = {
  toastItem: Toast
  description?: string
  actionButtonText?: string
}
const AlertNotificationComponent = ({
  toastItem,
  description = 'List updated by another user',
  actionButtonText = 'Refresh to see the latest changes'
}: Props) => {
  const pathname = location.pathname
  const handleRefresh = () => {
    window.location.replace(pathname)
  }
  const handleDismiss = () => {
    toast.dismiss(toastItem?.id)
  }
  return (
    <Transition appear show={toastItem?.visible} transition>
      <div className="flex gap-4 transform translate-y-1/4 z-50 w-full shadow-lg max-w-md p-4 bg-purple-50 rounded-lg border-purple-200 border border-solid">
        <UserIcon className="h-4 w-4 fill-purple-600 stroke-purple-600" />
        <div className="flex items-center justify-between gap-3 flex-1">
          <div className="flex-1">
            <p className="text-sm font-medium text-purple-900">{description}</p>
            <p className="text-xs text-purple-700 mt-1">{actionButtonText}</p>
          </div>
          <div className="flex items-center gap-2 ml-auto ">
            <PrimaryButton
              onClick={handleRefresh}
              className=" text-white h-8 px-3"
            >
              <ArrowUturnRightIcon
                stroke="white"
                fill="white"
                className="h-3 w-3 mr-1"
              />
              Refresh
            </PrimaryButton>

            <XMarkIcon
              onClick={handleDismiss}
              className="min-h-8 min-w-8 p-2 hover:bg-gray-200 hover:cursor-pointer rounded-full"
            />
          </div>
        </div>
      </div>
    </Transition>
  )
}

export default AlertNotificationComponent
