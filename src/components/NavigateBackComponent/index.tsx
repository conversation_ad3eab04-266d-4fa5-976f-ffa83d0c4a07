import { ArrowLeftIcon } from '@heroicons/react/20/solid'
import { useNavigate } from 'react-router-dom'

const NavigateBackComponent = () => {
  const navigate = useNavigate()
  return (
    <div className="sticky -top-4 z-10 bg-white p-2 pl-0 w-full">
      <div
        onClick={() => navigate(-1)}
        className="flex items-center gap-1 group cursor-pointer w-fit"
      >
        <ArrowLeftIcon height={24} width={24} fill="#843BAC" />
        <p className="text-primary font-semibold group-hover:underline">Back</p>
      </div>
    </div>
  )
}

export default NavigateBackComponent
