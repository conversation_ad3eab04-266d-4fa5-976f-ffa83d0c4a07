import { lazy } from 'react'
import {
  <PERSON>O<PERSON>,
  RouterProvider,
  create<PERSON>rowserRouter
} from 'react-router-dom'
import { ROUTE_PATH } from '../enum/RoutePath'
const RouteErrorPage = lazy(() => import('../components/RouteErrorComponent'))

const LoginRoute = lazy(() => import('./LoginRoutes'))
const PrivateRoute = lazy(() => import('./PrivateRoutes'))

const AppLayout = lazy(() => import('../layouts'))
const Login = lazy(() => import('../pages/Auth/Login'))
const ForgetPassword = lazy(() => import('../pages/Auth/ResetPassword'))
const RegisterPage = lazy(() => import('../pages/Auth/Register'))
const Home = lazy(() => import('../pages/Home'))
const AccessManagement = lazy(() => import('../pages/AccessManagement'))
const CameraManagement = lazy(() => import('../pages/CameraManagement'))
const AgentManagementPage = lazy(() => import('../pages/AgentsManagement'))
const LiveView = lazy(() => import('../pages/LiveView'))
const Recording = lazy(() => import('../pages/Recording'))
const RecordingPageLayout = lazy(() => import('../pages/Recording/layout'))
const RecordingDetailComponent = lazy(
  () => import('../pages/Recording/RecordingDetail')
)
const VideoAnalyticsLayout = lazy(
  () => import('../pages/VideoAnalyticsManagement/layout')
)
const VideoAnalyticsManagement = lazy(
  () => import('../pages/VideoAnalyticsManagement')
)
const VideoAnalyticsDetail = lazy(
  () => import('../pages/VideoAnalyticsManagement/VADetailsComponent')
)
const RequestsManagementPage = lazy(
  () => import('../pages/AccessManagement/PendingRequests')
)
const GroupsManagementPage = lazy(
  () => import('../pages/AccessManagement/Groups')
)
const ProjectsManagementPage = lazy(
  () => import('../pages/AccessManagement/Projects')
)
const ChangePasswordPage = lazy(() => import('../pages/ChangePassword'))
const AnalyticResultsPage = lazy(
  () => import('../pages/VideoAnalyticsManagement/AnalyticsResult')
)
const Settings = lazy(() => import('../pages/Settings'))
const StorageManagementPage = lazy(
  () => import('../pages/Settings/StorageManagement')
)
const SystemLogPage = lazy(() => import('../pages/Settings/SystemLog'))
const NotificationPage = lazy(() => import('../pages/Notification'))
const UserProfilePage = lazy(() => import('../pages/Profile'))
const UserManagementPage = lazy(() => import('../pages/AccessManagement/Users'))
const privateRoutes: RouteObject[] | undefined = [
  {
    path: ROUTE_PATH.Home,
    element: <Home />,
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Settings,
    element: <Settings />,
    children: [
      {
        path: ROUTE_PATH.Settings,
        element: <StorageManagementPage />,
        errorElement: <RouteErrorPage />
      },
      {
        path: ROUTE_PATH.System_Logs,
        element: <SystemLogPage />,
        errorElement: <RouteErrorPage />
      }
    ]
  },
  {
    path: ROUTE_PATH.Access_Management,
    element: <AccessManagement />,
    children: [
      {
        path: ROUTE_PATH.Access_Management,
        element: <RequestsManagementPage />,
        errorElement: <RouteErrorPage />
      },
      {
        path: ROUTE_PATH.Access_Projects,
        element: <ProjectsManagementPage />,
        errorElement: <RouteErrorPage />
      },
      {
        path: ROUTE_PATH.Access_Groups,
        element: <GroupsManagementPage />,
        errorElement: <RouteErrorPage />
      },
      {
        path: ROUTE_PATH.Access_Users,
        element: <UserManagementPage />,
        errorElement: <RouteErrorPage />
      }
    ],
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Camera,
    element: <CameraManagement />,
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Liveview,
    element: <LiveView />,
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Recording,
    element: <RecordingPageLayout />,
    errorElement: <RouteErrorPage />,
    children: [
      {
        path: `${ROUTE_PATH.Recording}`,
        element: <Recording />,
        errorElement: <RouteErrorPage />
      },
      {
        path: `${ROUTE_PATH.Recording}/:camera_id/:recording_id/:selected_date/:type`,
        element: <RecordingDetailComponent />,
        errorElement: <RouteErrorPage />
      }
    ]
  },

  {
    path: ROUTE_PATH.VideoAnalytics,
    element: <VideoAnalyticsLayout />,
    errorElement: <RouteErrorPage />,
    children: [
      {
        path: ROUTE_PATH.VideoAnalytics,
        element: <VideoAnalyticsManagement />,
        errorElement: <RouteErrorPage />
      },
      {
        path: `${ROUTE_PATH.VideoAnalytics}/:analytic_id`,
        element: <VideoAnalyticsDetail />,
        errorElement: <RouteErrorPage />
      },
      {
        path: `${ROUTE_PATH.VideoAnalytics}/:analytic_id/:assignment_id/:camera_name`,
        element: <AnalyticResultsPage />,
        errorElement: <RouteErrorPage />
      }
    ]
  },

  {
    path: ROUTE_PATH.Agents,
    element: <AgentManagementPage />,
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Change_Password,
    element: <ChangePasswordPage />,
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Notification,
    element: <NotificationPage />,
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Profile,
    element: <UserProfilePage />,
    errorElement: <RouteErrorPage />
  }
]

const routes = createBrowserRouter([
  {
    path: ROUTE_PATH.Login,
    element: (
      <LoginRoute>
        <Login />
      </LoginRoute>
    ),
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Forget_Password,
    element: (
      <LoginRoute>
        <ForgetPassword />
      </LoginRoute>
    ),
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Register,
    element: (
      <LoginRoute>
        <RegisterPage />
      </LoginRoute>
    ),
    errorElement: <RouteErrorPage />
  },
  {
    path: ROUTE_PATH.Home,
    element: (
      <PrivateRoute>
        <AppLayout />
      </PrivateRoute>
    ),
    children: privateRoutes
  }
])
const AppRouter = () => {
  return <RouterProvider router={routes}></RouterProvider>
}

export default AppRouter
