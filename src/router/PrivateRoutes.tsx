import { Navigate } from 'react-router-dom'
import { KEY_STORAGE } from '../enum/KeyStorage'
import { ROUTE_PATH } from '../enum/RoutePath'
import { getSupabaseUser } from '../api/Auth'
import { PropsWithChildren } from 'react'
import { COOKIE_EXPIRE_TIME, ENV } from '../utils'
import cookie from 'js-cookie'
const PrivateRoute = ({ children }: PropsWithChildren) => {
  const accessToken = cookie.get(KEY_STORAGE.ACCESS_TOKEN)
  const currentHash = window.location.hash.substring(1) // Remove the '#' character
  const params = new URLSearchParams(currentHash)
  const emailAccessToken = params.get('access_token')
  const emailProviderToken = params.get('provider_token')
  const emailRefreshToken = params.get('refresh_token')

  const handleGetSupabaseUser = async () => {
    cookie.set(KEY_STORAGE.ACCESS_TOKEN, emailAccessToken ?? '', {
      expires: new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
    })
    cookie.set(KEY_STORAGE.REFRESH_TOKEN, emailRefreshToken ?? '', {
      expires: new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
    })
    try {
      // mainly to get the userId to get the userDetail api for roles and check if super admin
      const res = await getSupabaseUser(emailAccessToken ?? '')
      localStorage.setItem(KEY_STORAGE.USER_ID, res.id)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    }
  }

  const handleRedirectFromAzure = async () => {
    await handleGetSupabaseUser()
    window.location.href = ROUTE_PATH.Home //remove the hash params to prevent access token leak
  }

  if (!accessToken) {
    //if not logged in
    if (emailProviderToken) {
      //if accessing through Azure, automatically log in with access and refresh token
      handleRedirectFromAzure()
      return null
    } else {
      return params.get('access_token') ? (
        // if accessing through email to change password. navigate to forget password page
        <Navigate
          to={ROUTE_PATH.Forget_Password}
          state={{ accessToken: params.get('access_token') }}
        />
      ) : (
        <Navigate to={ROUTE_PATH.Login} />
      )
    }
  }

  return emailAccessToken && !emailProviderToken ? (
    //if accessing through email to change password and logged in, redirect to change password page
    <>
      <Navigate
        to={ROUTE_PATH.Change_Password}
        state={{
          accessToken: params.get('access_token'),
          refreshToken: params.get('refresh_token')
        }}
      />
      {children}
    </>
  ) : (
    children
  )
}
export default PrivateRoute
