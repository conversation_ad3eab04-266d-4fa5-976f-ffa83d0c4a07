import { ReactNode } from 'react'
import { Navigate } from 'react-router-dom'
import { KEY_STORAGE } from '../enum/KeyStorage'
import { ROUTE_PATH } from '../enum/RoutePath'
import cookie from 'js-cookie'
import {LandingPageLogo} from '../components/Logo'

type Props = {
  children: ReactNode
}

const LoginRoute = ({ children }: Props) => {
  const accessToken = cookie.get(KEY_STORAGE.ACCESS_TOKEN)
  if (accessToken) {
    return <Navigate to={ROUTE_PATH.Home} replace />
  }

  return (
    <div className="flex bg-gray-50 min-h-dvh max-h-dvh flex-1 flex-col justify-center py-8 px-4 sm:px-6 lg:px-8">
      <LandingPageLogo />
      <div className="h-full w-full">{children}</div>
    </div>
  )
}

export default LoginRoute
