import { useCallback, useState } from 'react'
import PrimaryButton from '../../components/PrimaryButtons'
import DragAndDropCameraView from './DragAndDropView'
import PageHeaderText from '../../components/PageHeaderText'

const Home = () => {
  const [openManageDashboard, setOpenManageDashboard] = useState<boolean>(false)
  const onManageDashboard = useCallback(() => {
    setOpenManageDashboard((prev) => !prev)
  }, [])

  return (
    <div className="h-full w-full flex flex-col overflow-auto overflow-x-hidden">
      <div className="flex flex-col sm:flex-row flex-wrap w-full items-center justify-between mb-4 gap-8">
        <PageHeaderText>Dashboard</PageHeaderText>
        <PrimaryButton
          onClick={onManageDashboard}
          className="sm:max-w-[200px] py-[10px]"
        >
          Manage Dashboard
        </PrimaryButton>
      </div>
      <DragAndDropCameraView
        manageDashboardState={[openManageDashboard, setOpenManageDashboard]}
      />
    </div>
  )
}

export default Home
