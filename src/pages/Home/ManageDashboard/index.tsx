import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import { classNames } from '../../../utils'
import { createPortal } from 'react-dom'
import {
  getDashboardManageCamerasThunk,
  selectManageDashboardCameraPage,
  selectManageDashboardCameraSearchText,
  setManageCameraPage,
  setManageCameraSearchText
} from '../../../stores/Reducers/dashboardReducer'
import TextInputComponent from '../../../components/TextInputComponent'
import SearchIcon from '../../../assets/svgs/SearchIcon'
import { useCallback, useEffect, useState } from 'react'
import PrimaryButton from '../../../components/PrimaryButtons'
import { useDebounce } from '../../../utils/hooks/useDebounce'
import { IOption } from '../../../interfaces'
import DropdownOptions from '../../../components/DropdownOptions'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import ManageDashboardCameraTable from './ManageDashboardCameraTable'

type Props = {
  setOpenManageDashboard: React.Dispatch<React.SetStateAction<boolean>>
  draggedItemId?: string
}

const MANAGE_DASHBOARD_OPTIONS: IOption[] = [
  {
    label: 'All',
    value: 'all'
  },
  {
    label: 'Selected',
    value: 'selected'
  }
]

const ManageDashboardComponenent = ({
  setOpenManageDashboard,
  draggedItemId = ''
}: Props) => {
  const cameraSearchText = useAppSelector(selectManageDashboardCameraSearchText)
  const cameraPage = useAppSelector(selectManageDashboardCameraPage)
  const dispatch = useAppDispatch()
  const [dashboardCameraType, setDashboardCameraText] = useState<string>(
    MANAGE_DASHBOARD_OPTIONS[0].value
  )

  const onSelectCameraType = useCallback((value: string) => {
    setDashboardCameraText(value)
  }, [])

  const { isMounted } = useDebounce({
    func: () =>
      cameraPage > 1
        ? dispatch(setManageCameraPage(1))
        : dispatch(getDashboardManageCamerasThunk()),
    searchText: cameraSearchText
  })
  const handleFetchListCameras = async () => {
    await dispatch(getDashboardManageCamerasThunk())
    isMounted.current = true
  }

  useEffect(() => {
    handleFetchListCameras()
  }, [cameraPage])

  return createPortal(
    <div
      id="manage-dashboard"
      className={classNames(
        'flex flex-col h-full md:h-fit gap-2 md:w-[744px] p-4 sm:p-6 absolute bottom-0 rounded-t-2xl shadow-2xl bg-white left-4 right-4',
        draggedItemId !== '' && 'opacity-25'
      )}
    >
      <p className="font-bold text-sm sm:text-2xl max-h-fit h-fit">
        Manage Dashboard
      </p>
      <p className="my-2 hidden sm:block text-sm">
        Drag and drop or select the camera feeds to display on your dashboard.
      </p>
      {dashboardCameraType === 'all' && (
        <TextInputComponent
          prefixIcon={<SearchIcon />}
          onChange={(e) => dispatch(setManageCameraSearchText(e.target.value))}
          placeholder="Search cameras by name..."
        />
      )}
      <DropdownOptions
        onlyButtonIcon
        label={''}
        labelIcon={
          <div className="flex gap-2 mx-auto items-center">
            <p className="font-bold capitalize">
              {dashboardCameraType.replace('_', ' ')}
            </p>
            <ChevronDownIcon height={20} width={20} />
          </div>
        }
        options={MANAGE_DASHBOARD_OPTIONS}
        onSelect={onSelectCameraType}
        className="ml-auto max-h-fit"
        selected={dashboardCameraType}
      />
      <ManageDashboardCameraTable dashboardCameraType={dashboardCameraType} />

      <PrimaryButton
        className="sm:max-w-[200px] ml-auto mt-auto"
        onClick={() => setOpenManageDashboard(false)}
      >
        Close
      </PrimaryButton>
    </div>,
    document.body
  )
}

export default ManageDashboardComponenent
