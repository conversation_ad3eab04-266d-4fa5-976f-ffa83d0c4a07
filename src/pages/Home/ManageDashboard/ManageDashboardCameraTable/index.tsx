import { useMemo } from 'react'
import { DEFAULT_PAGE_SIZE } from '../../../../utils'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  selectListDashboardCamera,
  selectLiveViewData,
  selectManageDashboardCameraPage,
  selectManageDashboardTotalCamera,
  setManageCameraPage
} from '../../../../stores/Reducers/dashboardReducer'
import NoDataComponent from '../../../../components/NoDataComponent'
import PaginationComponent from '../../../../components/Pagination'
import ManageDashboardTableItem from './manage-dashboard-table-item'

type Props = { dashboardCameraType: string }

const ManageDashboardCameraTable = ({ dashboardCameraType }: Props) => {
  const totalCameras = useAppSelector(selectManageDashboardTotalCamera)
  const liveViewData = useAppSelector(selectLiveViewData)
  const listCamera = useAppSelector(selectListDashboardCamera)
  const cameraPage = useAppSelector(selectManageDashboardCameraPage)
  const dispatch = useAppDispatch()

  const listCameraInDashboard = useMemo(
    () => liveViewData.map((item) => item && item.camera),
    [liveViewData]
  )

  const listCameraByType = useMemo(() => {
    switch (dashboardCameraType) {
      case 'selected':
        return listCameraInDashboard
      case 'all':
        return listCamera

      default:
        return listCamera
    }
  }, [dashboardCameraType, listCamera, listCameraInDashboard])

  return (
    <>
      <div
        id="manage-dashboard-table"
        className="h-[550px] w-full overflow-auto"
      >
        <div className="-my-2 overflow-x-auto w-full h-full">
          <div className="min-w-full py-2 align-middle">
            <table className="min-w-full relative table w-full divide-gray-300">
              <thead className="sticky top-2 w-full bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="py-3.5 px-3 text-center text-sm font-semibold text-mainBlack"
                  >
                    Select
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    Camera
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    Project
                  </th>
                  <th
                    scope="col"
                    className="px-3 pl-6 py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    RTSP
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white overflow-auto">
                {listCameraByType.length > 0 ? (
                  listCameraByType.map(
                    (dashboardCamera) =>
                      dashboardCamera && (
                        <ManageDashboardTableItem
                          dashboardCamera={dashboardCamera}
                        />
                      )
                  )
                ) : (
                  <tr className="h-[300px]">
                    <td colSpan={7}>
                      <NoDataComponent />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {dashboardCameraType === 'all' && (
        <PaginationComponent
          totalItems={totalCameras}
          currentPage={cameraPage}
          onPageChange={(page) => dispatch(setManageCameraPage(page))}
          pageSize={DEFAULT_PAGE_SIZE}
        />
      )}
    </>
  )
}

export default ManageDashboardCameraTable
