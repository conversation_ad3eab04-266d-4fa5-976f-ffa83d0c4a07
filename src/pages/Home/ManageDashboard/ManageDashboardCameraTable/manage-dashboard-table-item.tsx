import React, { useCallback, useState } from 'react'
import { ICameras } from '../../../../models/camera'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  addCameraToDashboardThunk,
  addToLiveView,
  removeDashboardCameraThunk,
  selectListDashboardCamera,
  selectLiveViewData
} from '../../../../stores/Reducers/dashboardReducer'
import { AddCameraDashboard } from '../../../../models/dashboard'
import DraggableArea from '../../DragAndDropView/DraggableArea'
import DragIcon from '../../../../assets/svgs/DragIcon'
import { Tooltip } from 'antd'
import { LoaderIcon } from 'react-hot-toast'

type Props = {
  dashboardCamera?: ICameras
}

const ManageDashboardTableItem = ({ dashboardCamera }: Props) => {
  const listCamera = useAppSelector(selectListDashboardCamera)
  const liveViewData = useAppSelector(selectLiveViewData)
  const dispatch = useAppDispatch()
  const [isProcessing, setIsProcessing] = useState(false)

  const onRemoveCameraDashboard = async (camera: ICameras) => {
    setIsProcessing(true)
    const grid_id =
      liveViewData.find((item) => item?.camera?.id === camera?.id)?.id ?? ''
    await dispatch(removeDashboardCameraThunk(grid_id))
    setIsProcessing(false)
  }

  const onAddCameraDashboard = async (camera: ICameras) => {
    setIsProcessing(true)
    const addedIndex = liveViewData.findIndex(
      (item) => item?.camera === undefined
    )
    const firstGridItemEmpty =
      addedIndex >= 0 ? addedIndex : liveViewData.length
    const data: AddCameraDashboard = {
      camera_id: camera.id,
      position: firstGridItemEmpty + 1,
      data: camera
    }
    dispatch(addToLiveView(data))

    await dispatch(addCameraToDashboardThunk(data))
    setIsProcessing(false)
  }
  const onCheckedCamera = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, camera: ICameras) => {
      if (e.target.checked) {
        onAddCameraDashboard(camera)
      } else {
        onRemoveCameraDashboard(camera)
      }
    },
    [liveViewData]
  )
  const isIncludedInLiveView = useCallback(
    (camera: ICameras) =>
      liveViewData.some((item) => item?.camera?.id === camera.id),
    [liveViewData, listCamera]
  )

  if (!dashboardCamera) return <></>
  return (
    <tr key={dashboardCamera?.id ?? ''}>
      <td className="whitespace-nowrap gap-2 flex items-center justify-center py-4 pt-5 px-3 text-sm font-medium text-mainBlack">
        {!isIncludedInLiveView(dashboardCamera) && (
          <DraggableArea
            data={dashboardCamera}
            id={`${dashboardCamera.id}`}
            isTable
            isDraggable={!isIncludedInLiveView(dashboardCamera)}
            key={`${dashboardCamera.id}`}
          >
            <DragIcon className="cursor-grab" />
          </DraggableArea>
        )}
        {isProcessing ? (
          <LoaderIcon />
        ) : (
          <input
            checked={isIncludedInLiveView(dashboardCamera)}
            type="checkbox"
            onChange={(e) => onCheckedCamera(e, dashboardCamera)}
            className="max-w-4 h-4 border border-solid rounded-sm border-gray-200"
          />
        )}
      </td>
      <td className="whitespace-nowrap min-w-fit w-fit max-w-[150px] px-3 py-4 text-sm truncate text-mainBlack">
        {dashboardCamera.name}
      </td>
      <td className="whitespace-nowrap truncate min-w-fit w-fit max-w-[150px] px-3 py-4 text-sm text-mainBlack">
        {dashboardCamera?.project?.name}
      </td>
      <td className="whitespace-nowrap truncate max-w-[150px] px-3 py-4">
        <Tooltip
          className="text-sm text-mainBlack"
          title={dashboardCamera.rtsp}
          mouseEnterDelay={0.5}
        >
          {dashboardCamera.rtsp}
        </Tooltip>
      </td>
    </tr>
  )
}

export default ManageDashboardTableItem
