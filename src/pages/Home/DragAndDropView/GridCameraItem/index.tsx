import React from 'react'
import { IDashboardCamera } from '../../../../models/dashboard'
import DroppableArea from '../DroppableArea'
import DraggableArea from '../DraggableArea'
import DefaultDropArea from '../DefaultDropZone'
import LiveViewItem from '../../../LiveView/LiveViewItem'
import { classNames } from '../../../../utils'
import { ICameras } from '../../../../models/camera'

type Props = {
  gridItem: IDashboardCamera
  draggedItem: ICameras | undefined
  overItem: string
  setOpenManageDashboard: React.Dispatch<React.SetStateAction<boolean>>
}

const GridCameraItemComponent = ({
  gridItem,
  draggedItem,
  overItem,
  setOpenManageDashboard
}: Props) => {
  if (!gridItem?.camera) {
    return (
      <DroppableArea key={gridItem?.id ?? ''} id={gridItem?.id ?? ''}>
        <div onClick={() => setOpenManageDashboard(true)}>
          <DefaultDropArea
            overItem={overItem}
            originalItem={gridItem?.id ?? ''}
            text={
              <>
                <p className="text-sm text-[#272727]">
                  This camera feed is empty.
                </p>
                <p className="text-sm text-[#272727]">
                  Click here or "Manage dashboard" to add a camera.
                </p>
              </>
            }
          />
        </div>
      </DroppableArea>
    )
  }
  return (
    <>
      <DroppableArea key={gridItem?.id ?? ''} id={gridItem?.id ?? ''}>
        <DraggableArea data={gridItem.camera} id={gridItem?.id ?? ''}>
          {draggedItem?.id !== gridItem?.id && draggedItem ? (
            <DefaultDropArea
              overItem={overItem}
              originalItem={gridItem?.id ?? ''}
            />
          ) : (
            <div
              title="Hold and drag to arrange"
              className={classNames(
                draggedItem?.id === gridItem?.id && 'hidden'
              )}
            >
              <LiveViewItem
                project={gridItem?.camera?.project?.name}
                camera={gridItem.camera}
              />
            </div>
          )}
        </DraggableArea>
      </DroppableArea>
    </>
  )
}

export default GridCameraItemComponent
