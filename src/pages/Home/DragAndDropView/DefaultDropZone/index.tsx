import { ReactNode } from 'react'
import DefaultThumbnail from '../../../../assets/svgs/DefaultThumbnail'
import { classNames } from '../../../../utils'

type Props = {
  overItem: string
  originalItem: string
  text?: string | ReactNode
}

const DefaultDropArea = ({ overItem, originalItem, text }: Props) => {
  return (
    <div
      className={classNames(
        'min-w-[300px] w-full h-[365px] flex flex-col justify-center items-center gap-2 cursor-pointer rounded-[4px]',
        overItem === originalItem
          ? 'border border-dashed border-[#853bac] bg-purple-100'
          : 'border border-dashed border-gray-400 bg-gray-50 '
      )}
    >
      <DefaultThumbnail
        className={classNames(overItem === originalItem && 'fill-purple-700')}
      />
      {text ? (
        text
      ) : (
        <p
          className={classNames(
            'font-semibold text-lg',
            overItem === originalItem ? 'text-purple-600' : 'text-gray-500 '
          )}
        >
          {'Drop here'}
        </p>
      )}
    </div>
  )
}

export default DefaultDropArea
