import { useDroppable } from '@dnd-kit/core'
import { PropsWithChildren } from 'react'

interface Props extends PropsWithChildren {
  id: string
}

const DroppableArea = ({ children, id }: Props) => {
  const { isOver, setNodeRef } = useDroppable({
    id: id
  })
  const style = {
    color: isOver ? 'green' : undefined
  }

  return (
    <div
      className="cursor-pointer z-0 h-fit overscroll-x-hidden"
      ref={setNodeRef}
      style={style}
    >
      {children}
    </div>
  )
}

export default DroppableArea
