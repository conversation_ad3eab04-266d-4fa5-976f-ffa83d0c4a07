import { useDraggable } from '@dnd-kit/core'
import { PropsWithChildren } from 'react'
import { ICameras } from '../../../../models/camera'

interface Props extends PropsWithChildren {
  id: string
  data?: ICameras
  isTable?: boolean
  className?: string
  isDraggable?: boolean
}

const DraggableArea = ({
  children,
  id,
  data,
  isTable,
  className,
  isDraggable = true
}: Props) => {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: id,
    data: data
  })
  return isTable ? (
    <tr
      title="Hold and drag to arrange"
      className={`${className} cursor-pointer overflow-x-hidden even:bg-gray-50`}
      ref={setNodeRef}
      {...(isDraggable ? listeners : {})}
      {...attributes}
    >
      {children}
    </tr>
  ) : (
    <div
      title="Hold and drag to arrange"
      className="cursor-pointer overflow-x-hidden"
      ref={setNodeRef}
      {...(isDraggable ? listeners : {})}
      {...attributes}
    >
      {children}
    </div>
  )
}

export default DraggableArea
