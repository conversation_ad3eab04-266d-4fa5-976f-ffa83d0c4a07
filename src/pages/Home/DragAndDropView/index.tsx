import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  useSensor,
  useSensors,
  DragOverEvent,
  CollisionDetection,
  rectIntersection,
  TouchSensor
} from '@dnd-kit/core'
import { snapCenterToCursor } from '@dnd-kit/modifiers'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import { useCallback, useEffect, useState } from 'react'
import LiveViewItem from '../../LiveView/LiveViewItem'
import { ICameras } from '../../../models/camera'
import { createPortal } from 'react-dom'
import {
  addCameraToDashboardThunk,
  getDashboardCamerasThunk,
  selectLiveViewData,
  swapDashboardCamerasThunk
} from '../../../stores/Reducers/dashboardReducer'
import ManageDashboardComponenent from '../ManageDashboard'
import {
  AddCameraDashboard,
  IDashboardCamera,
  SwapCameraDashboard
} from '../../../models/dashboard'
import GridCameraItemComponent from './GridCameraItem'
import DroppableArea from './DroppableArea'
import DefaultDropArea from './DefaultDropZone'
type Props = {
  manageDashboardState: [boolean, React.Dispatch<React.SetStateAction<boolean>>]
}
const DragAndDropCameraView = ({ manageDashboardState }: Props) => {
  const [openManageDashboard, setOpenManageDashboard] = manageDashboardState
  const mouseSensor = useSensor(MouseSensor, {
    // Require the mouse to move by 10 pixels before activating
    activationConstraint: {
      delay: 100, // Delay in milliseconds
      tolerance: 5 // Tolerance in pixels to differentiate tap and drag
    }
  })
  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 100, // Delay in milliseconds
      tolerance: 5 // Tolerance in pixels to differentiate tap and drag
    }
  })
  const sensors = useSensors(mouseSensor, touchSensor)
  const dispatch = useAppDispatch()
  const [draggedItem, setDraggedItem] = useState<ICameras>()
  const [overItem, setOverItem] = useState<string>('')
  const liveViewData = useAppSelector(selectLiveViewData)

  const fixCursorSnapOffset: CollisionDetection = (args) => {
    // Bail out if keyboard activated
    if (!args.pointerCoordinates) {
      return rectIntersection(args)
    }
    const { x, y } = args.pointerCoordinates
    const { width, height } = args.collisionRect
    const updated = {
      ...args,
      // The collision rectangle is broken when using snapCenterToCursor. Reset
      // the collision rectangle based on pointer location and overlay size.
      collisionRect: {
        width,
        height,
        bottom: y + height / 2,
        left: x - width / 2,
        right: x + width / 2,
        top: y - height / 2
      }
    }
    return rectIntersection(updated)
  }

  useEffect(() => {
    dispatch(getDashboardCamerasThunk())
  }, [])

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setDraggedItem(undefined)
    setOverItem('')
    if (!over) {
      return
    }
    const isItemInTheLiveView = liveViewData.some(
      (item) => item.camera?.id === (active.data.current as IDashboardCamera).id
    )
    if (!isItemInTheLiveView) {
      const dropIndex =
        liveViewData.findIndex((live) => live?.id === over.id?.toString()) + 1

      handleAddItemInLiveView(
        event,
        dropIndex === 0 ? Number(over.id.toString()) : dropIndex
      )
      return
    }

    handleSwapDashboardItem(event)
  }

  const handleAddItemInLiveView = useCallback(
    async (event: DragEndEvent, position: number) => {
      const { active } = event
      const data: AddCameraDashboard = {
        camera_id: (active.data.current as ICameras)?.id ?? '',
        position: position,
        data: active.data.current as ICameras
      }
      await dispatch(addCameraToDashboardThunk(data))
    },
    []
  )

  const handleSwapDashboardItem = async (event: DragEndEvent) => {
    const { active, over } = event

    if (over?.id.toString() === (liveViewData.length + 1).toString()) {
      return
    }
    const draggedItemPosition =
      liveViewData.findIndex(
        (gridItem) =>
          gridItem?.camera?.id === (active.data.current as ICameras).id
      ) ?? 0

    const droppedItemPosition =
      liveViewData.findIndex((gridItem) => gridItem?.id === over?.id) ?? 0

    if (droppedItemPosition !== draggedItemPosition) {
      const data: SwapCameraDashboard = {
        source_position: draggedItemPosition + 1,
        target_position: droppedItemPosition + 1
      }
      await dispatch(swapDashboardCamerasThunk(data))
    }
  }

  const handleDragCancel = () => {
    setDraggedItem(undefined)
  }

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event
    setOverItem(over?.id?.toString() ?? '')
  }

  const handleDragStart = (event: DragStartEvent) => {
    setDraggedItem(event?.active?.data?.current as ICameras)
  }

  return (
    <div className="grid relative grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 grid-flow-row gap-4 h-full w-full sm:pr-2">
      <DndContext
        onDragStart={handleDragStart}
        sensors={sensors}
        autoScroll={{ layoutShiftCompensation: false }}
        onDragEnd={handleDragEnd}
        collisionDetection={fixCursorSnapOffset}
        onDragOver={handleDragOver}
        onDragCancel={handleDragCancel}
      >
        {liveViewData.map((gridItem) => (
          <GridCameraItemComponent
            key={gridItem?.id ?? ''}
            gridItem={gridItem}
            setOpenManageDashboard={setOpenManageDashboard}
            overItem={overItem}
            draggedItem={draggedItem}
          />
        ))}
        <DroppableArea id={`${liveViewData.length + 1}`}>
          <div onClick={() => setOpenManageDashboard(true)}>
            <DefaultDropArea
              overItem={overItem}
              originalItem={`${liveViewData.length + 1}`}
              text={
                <>
                  <p className="text-sm text-[#272727]">
                    This camera feed is empty.
                  </p>
                  <p className="text-sm text-[#272727]">
                    Click here or "Manage dashboard" to add a camera.
                  </p>
                </>
              }
            />
          </div>
        </DroppableArea>
        {openManageDashboard && (
          <ManageDashboardComponenent
            setOpenManageDashboard={setOpenManageDashboard}
            draggedItemId={draggedItem?.id ?? ''}
          />
        )}
        {createPortal(
          <DragOverlay
            dropAnimation={null}
            modifiers={[snapCenterToCursor]}
            adjustScale={false}
          >
            <div className="w-[200px] h-[200px]">
              <LiveViewItem camera={draggedItem} />
            </div>
          </DragOverlay>,
          document.body
        )}
      </DndContext>
    </div>
  )
}

export default DragAndDropCameraView
