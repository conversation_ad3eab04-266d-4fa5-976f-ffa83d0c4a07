import { EllipsisVerticalIcon } from '@heroicons/react/20/solid'
import DropdownMenus from '../../../components/DropdownMenu'
import { AssignedVA, IRecordings } from '../../../models/recodings'
import dayjs from 'dayjs'
import {
  formattedRecordingName,
  getErrorMessage,
  secondsToHMS
} from '../../../utils'
import { useEffect, useMemo, useRef, useState } from 'react'
import CustomModal from '../../../components/Modal'
import AssignVAModal from './AssignVAModal'
import { Tooltip } from 'antd'
import TagComponent from '../../../components/Tags'
import TextInputComponent from '../../../components/TextInputComponent'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  assignVAPluginsThunk,
  fetchListRecordingThunk,
  selectDeletedRecordings,
  setSingulaDeletedRecording,
  unSelectSingularDeletedRecording
} from '../../../stores/Reducers/recordingReducer'
import {
  deleteRecording,
  getSASToken,
  getThumbNail
} from '../../../api/Recordings'
import { toast } from 'react-hot-toast'
import DefaultThumbnail from '../../../assets/svgs/DefaultThumbnail'
import { AxiosError } from 'axios'
import { AssignVARecording } from '../../../models/analytics'
import ConfirmDeleteRecordingModal from './DeleteRecordingModal'
import DeleteModalHeader from '../../../components/DeleteModalHeader'
import ConfirmModalFooter from '../../../components/ConfirmModalFooter'

type Props = {
  onDelete: () => void
  onPlayVideo: () => void
  recording: IRecordings
}

const RecordingItemComponent = ({ onPlayVideo, recording }: Props) => {
  const [openRunVAModal, setOpenRunVAModal] = useState<boolean>(false)
  const deletedRecordings = useAppSelector(selectDeletedRecordings)
  const [thumbnailErr, setThumbnailErr] = useState<string>('')
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false)
  const [thumbnailSrc, setThumbnailSrc] = useState<string>('')
  const deletedSingularRecording = useRef<IRecordings>()
  const retryThumbnailLimit = useRef<number>(1)
  const abortController = useRef<AbortController>()
  const dispatch = useAppDispatch()
  const menuOptions = [
    {
      id: '1',
      text: 'Run VA Plugin',
      onClick: () => setOpenRunVAModal(true)
    },
    {
      id: '2',
      text: 'Delete',
      onClick: () => {
        deletedSingularRecording.current = recording
        setOpenConfirmDelete(true)
      }
    }
  ]

  const renderFormattedRecordingName = useMemo((): string => {
    return formattedRecordingName(recording)
  }, [recording])

  const onConfirmDeleteSingularRecording = async () => {
    try {
      await deleteRecording([deletedSingularRecording.current?.id ?? ''])
      dispatch(fetchListRecordingThunk())
      toast.success(
        `Recording ${deletedSingularRecording.current?.record_name} is deleted!`
      )
    } catch (error) {
      toast.error(getErrorMessage(error))
    }
  }

  const onSelectRecording = (checked: boolean) => {
    if (checked) {
      dispatch(setSingulaDeletedRecording(recording))
      return
    }
    dispatch(unSelectSingularDeletedRecording(recording.id))
  }

  useEffect(() => {
    handleGetThumbnail()
    return () => {
      abortController.current?.abort()
    }
  }, [])

  const handleGetThumbnail = async () => {
    abortController.current = new AbortController()
    setThumbnailErr('')
    try {
      const res = await getThumbNail(
        recording.record_type === 'uploaded'
          ? recording.file_name
              .split('.mp4')[0]
              .split('.mov')[0]
              .split('.avi')[0]
          : recording.id,
        abortController.current.signal
      )
      setThumbnailSrc(res)
      retryThumbnailLimit.current = 1
    } catch (error) {
      const axiosError = error as AxiosError

      if (axiosError.code === AxiosError.ERR_CANCELED) {
        return
      }
      if (axiosError.status === 403) {
        // if token expired/invalid
        retryThumbnailLimit.current++
        await getSASToken()
        if (retryThumbnailLimit.current < 5) {
          await handleGetThumbnail()
          return
        }
        retryThumbnailLimit.current = 1
        setThumbnailErr(recording.id)
      }
      retryThumbnailLimit.current = 1
      setThumbnailErr(recording.id)
    }
  }
  const onConfirmAssignVA = async (selectedVA: AssignedVA[]) => {
    const data: AssignVARecording = {
      recording_id: recording.id,
      va_plugin_ids: selectedVA
    }
    const action = await dispatch(assignVAPluginsThunk(data)) //fire assign VA request
    if (assignVAPluginsThunk.fulfilled.match(action)) {
      // if the request success, close the modal
      setOpenRunVAModal(false)
    }
  }
  return (
    <>
      <div className="h-fit flex-1 lg:max-w-[320px] md:min-w-[180px] cursor-pointer rounded-[4px] border border-solid border-gray-200">
        <div className="relative w-full h-fit">
          <div
            onClick={onPlayVideo}
            className="w-full min-h-[200px] bg-[#80869e] flex justify-center items-center"
          >
            {thumbnailErr === recording.id ? (
              <DefaultThumbnail />
            ) : (
              <img
                className="w-full h-[200px] object-cover"
                src={thumbnailSrc}
                loading="lazy"
              />
            )}
          </div>
          <div className="absolute right-2 bottom-4 bg-[#424242] py-[2px] px-1">
            <p className="text-white text-xs">
              {typeof recording?.duration === 'number'
                ? secondsToHMS(recording.duration)
                : 'Processing...'}
            </p>
          </div>
          <div className="absolute right-2 top-2">
            <TagComponent
              text={
                recording.record_type === 'uploaded' ? 'Uploaded' : 'Recorded'
              }
              className="font-bold !py-1 !px-3"
              fontSize="10px"
              textColor="gray"
            />
          </div>
        </div>
        <div className="w-full bg-white flex relative items-center justify-between py-1 px-3 pr-1 gap-[10px]">
          <TextInputComponent
            type="checkbox"
            checked={deletedRecordings.some((item) => item.id === recording.id)}
            onChange={(e) => onSelectRecording(e.target.checked)}
            className="size-[18px] border-solid border-[1.5px] border-gray-400 rounded-sm"
          />
          <div className="flex flex-col truncate">
            <Tooltip
              title={renderFormattedRecordingName}
              mouseEnterDelay={0.5}
              className="text-sm font-medium truncate w-full"
            >
              {renderFormattedRecordingName}
            </Tooltip>
            <p className="text-xs">
              Created on {dayjs(recording.created_at).format('MMMM D, YYYY')}
            </p>
          </div>
          <DropdownMenus
            className="ring-white shadow-none shadow-white"
            label={<EllipsisVerticalIcon height={20} width={20} />}
            menus={menuOptions}
            popupDirection="left"
          />
        </div>
      </div>
      <CustomModal
        openState={[openRunVAModal, setOpenRunVAModal]}
        title={'Run VA Plugins'}
      >
        <AssignVAModal
          onCancel={() => setOpenRunVAModal(false)}
          vaPlugins={recording.va_plugins ?? []}
          onConfirmAssignVA={onConfirmAssignVA}
        />
      </CustomModal>
      <CustomModal
        openState={[openConfirmDelete, setOpenConfirmDelete]}
        className="md:!min-w-[28rem] md:!max-w-md max-h-[90vh]"
        title={
          <DeleteModalHeader
            title="Delete Recording"
            description={formattedRecordingName(recording)}
          />
        }
      >
        <ConfirmDeleteRecordingModal
          recording={deletedSingularRecording.current}
          assignedPlugins={recording.va_plugins ?? []}
        />
        <ConfirmModalFooter
          onCancel={() => setOpenConfirmDelete(false)}
          onConfirm={onConfirmDeleteSingularRecording}
          isLoading={false}
        />
      </CustomModal>
    </>
  )
}

export default RecordingItemComponent
