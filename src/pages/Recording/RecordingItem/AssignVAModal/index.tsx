import { useEffect, useRef, useState } from 'react'
import { IAnalytics } from '../../../../models/analytics'
import { IPagination } from '../../../../interfaces'
import { getListAnalytics } from '../../../../api/Analytics'
import PrimaryButton from '../../../../components/PrimaryButtons'
import SecondaryButton from '../../../../components/SecondaryButtons'
import { AssignedVA } from '../../../../models/recodings'
import NoDataComponent from '../../../../components/NoDataComponent'
import { ENV } from '../../../../utils'
import ConfirmModal from '../../../../components/ConfirmModal'
import { NavLink } from 'react-router-dom'
import { ROUTE_PATH } from '../../../../enum/RoutePath'

type Props = {
  onCancel: () => void
  vaPlugins: AssignedVA[]
  onConfirmAssignVA: (selectedVA: AssignedVA[]) => Promise<void>
}

const AssignVAModal = ({ vaPlugins, onCancel, onConfirmAssignVA }: Props) => {
  const [listVA, setListVA] = useState<IAnalytics[]>([])
  const [selectedVA, setSelectedVA] = useState<AssignedVA[]>(vaPlugins ?? [])
  const [isProcessing, setIsProcessing] = useState<boolean>(false)
  const [vaAssignWarning, setVAAssignWarning] = useState<boolean>(false)
  const defaultRunningVA = useRef<AssignedVA[]>([])
  const fetchListVA = async () => {
    const data: IPagination = {
      'page-no': 1,
      'page-size': 50
    }
    try {
      const res = await getListAnalytics(data)
      setListVA(res.data.filter((va) => !va.api_keys?.[0].revoked))
    } catch (e) {
      if (ENV === 'DEV') {
        console.log(e)
      }
      setListVA([])
    }
  }

  const getDefaultRunningVA = () => {
    const runningVA: AssignedVA[] = []
    selectedVA.forEach((va) => {
      if (vaPlugins?.some((recordingVA) => recordingVA.id === va.id)) {
        runningVA.push(va)
      }
    })
    defaultRunningVA.current = runningVA
    return runningVA
  }

  const handleAssignVA = async () => {
    const runningVA = getDefaultRunningVA()
    if (runningVA.length > 0) {
      setVAAssignWarning(true)
      return
    }
    setIsProcessing(true)
    await onConfirmAssignVA(selectedVA)
    setVAAssignWarning(false)
    setIsProcessing(false)
  }

  const onSelectVA = (analytics: IAnalytics) =>
    selectedVA.some((va) => va.id === analytics.id) // if the VA is already selected then remove it from the array
      ? setSelectedVA((prev) => prev.filter((va) => va.id !== analytics.id))
      : setSelectedVA((prev) => [...prev, analytics])

  useEffect(() => {
    fetchListVA()
  }, [])

  if (listVA.length === 0) {
    return (
      <div className="min-h-[300px] flex items-center pb-12">
        <NoDataComponent />
      </div>
    )
  }

  return (
    <>
      <div className="w-full px-2 flex flex-col h-full">
        <div className="mt-4 flex-1 grid grid-cols-1 md:grid-cols-2 divide-y max-h-[300px] w-full overflow-auto divide-gray-200 border-b border-t border-gray-200">
          {listVA.map((analytics) => (
            <div
              key={analytics.id}
              className="relative gap-4 flex flex-row-reverse items-start py-1"
            >
              <div className="min-w-0 flex-1 text-sm leading-6">
                <label
                  htmlFor={`analytics-${analytics.id}`}
                  className="select-none leading-[22px] text-mainBlack"
                >
                  {analytics.name}
                </label>
              </div>
              <div className="ml-3 flex h-6 items-center">
                <input
                  id={`analytics-${analytics.id}`}
                  name={`analytics-${analytics.id}`}
                  defaultChecked={selectedVA.some(
                    (va) => va.id === analytics.id
                  )}
                  onChange={() => onSelectVA(analytics)}
                  type="checkbox"
                  className="h-4 w-4 rounded border-solid border-gray-300 text-primary focus:ring-primary"
                />
              </div>
            </div>
          ))}
        </div>
        <div className="flex items-center gap-4 py-4 sticky bottom-0 w-full justify-end">
          <SecondaryButton className="sm:max-w-[250px]" onClick={onCancel}>
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="sm:max-w-[250px]"
            isLoading={isProcessing}
            onClick={handleAssignVA}
          >
            Assign
          </PrimaryButton>
        </div>
      </div>
      <ConfirmModal
        onConfirm={() => onConfirmAssignVA(selectedVA)}
        text={undefined}
        openState={[vaAssignWarning, setVAAssignWarning]}
        title={'Assign VA Confirmation'}
      >
        <div className="flex flex-col h-full max-w-lg gap-4">
          <p className="text-sm">
            The following VA plugins are already assigned to this recording and
            will not be reassigned. To run the plugins again, please unassign
            them first.
          </p>
          <ul role="list" className="list-disc list-item pl-4">
            {defaultRunningVA.current.map((va) => (
              <li key={va.id} className="gap-2 items-center list-item">
                <p className="text-sm font-medium flex gap-2 items-center">
                  {va.name}{' '}
                  <NavLink
                    className={'text-xs text-blue-400 underline'}
                    to={`${ROUTE_PATH.VideoAnalytics}/${va.id}`}
                  >
                    View analytic stream
                  </NavLink>
                </p>
              </li>
            ))}
          </ul>
        </div>
      </ConfirmModal>
    </>
  )
}

export default AssignVAModal
