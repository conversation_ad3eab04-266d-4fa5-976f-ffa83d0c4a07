import { DatePicker } from 'antd'
import PrimaryButton from '../../components/PrimaryButtons'
import { useCallback, useState } from 'react'
import dayjs, { Dayjs } from 'dayjs'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import ListRecordingLoading from './ListLoading'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import utc from 'dayjs/plugin/utc'
import {
  selectIsUploading,
  selectRecordingLoading,
  selectRecordingMonth,
  selectRecordingYear,
  setSelectRecordingMonth,
  setSelectRecordingYear
} from '../../stores/Reducers/recordingReducer'
import RecordingQueriesComponent from './RecordingQueriesComponent'
import CustomModal from '../../components/Modal'
import UploadExternalRecordingModal from './UploadExternalFileModal'
import ListRecordingComponent from './ListRecordingComponent'
import PageHeaderText from '../../components/PageHeaderText'

const RecordingPage = () => {
  dayjs.extend(utc)
  const dispatch = useAppDispatch()
  const selectedMonth = useAppSelector(selectRecordingMonth)
  const selectedYear = useAppSelector(selectRecordingYear)
  const loading = useAppSelector(selectRecordingLoading)
  const isUploadingRecording = useAppSelector(selectIsUploading)
  const [openUploadModal, setOpenUploadModal] = useState<boolean>(false)
  const [date, setDate] = useState<Dayjs | null>(
    dayjs(`${selectedYear}-${selectedMonth + 1}-01`) ?? dayjs()
  )
  const onMonthChange = useCallback((date: Dayjs | null) => {
    setDate(date)
    dispatch(setSelectRecordingMonth(date?.month() ?? 0))
    dispatch(setSelectRecordingYear(date?.year() ?? 0))
  }, [])

  return (
    <div className="w-full flex flex-col flex-1 overflow-auto">
      <div className="flex w-full flex-wrap items-center justify-between gap-2">
        <div className="flex items-center gap-2 flex-1 justify-between">
          <PageHeaderText>Recordings</PageHeaderText>
          <DatePicker
            picker="month"
            onChange={onMonthChange}
            allowClear={false}
            value={date}
            id="recording-month-pickers"
            className="recording-month-pickers !z-0 sm:hidden block border-none ring-0 w-fit sm:w-[180px] font-bold focus:ring-0"
            suffixIcon={<ChevronDownIcon height={20} width={20} />}
            format="MMMM YYYY"
          />
        </div>
        <PrimaryButton
          isDisabled={isUploadingRecording}
          isLoading={isUploadingRecording}
          onClick={() => setOpenUploadModal(true)}
          className="sm:max-w-[200px] py-[10px]"
        >
          {isUploadingRecording ? 'Uploading...' : 'Upload Files'}
        </PrimaryButton>
      </div>
      <RecordingQueriesComponent onMonthChange={onMonthChange} date={date} />
      {loading ? <ListRecordingLoading /> : <ListRecordingComponent />}

      <CustomModal
        openState={[openUploadModal, setOpenUploadModal]}
        title={'Upload Recording File'}
      >
        <UploadExternalRecordingModal
          closeModal={() => setOpenUploadModal(false)}
        />
      </CustomModal>
    </div>
  )
}

export default RecordingPage
