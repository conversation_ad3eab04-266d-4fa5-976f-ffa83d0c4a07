import { useCallback } from 'react'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  RECORDING_PAGESIZE,
  playRecording,
  selectCurrentRecordingPage,
  selectListRecording,
  selectTotalRecording,
  setCurrentRecordingPage
} from '../../../stores/Reducers/recordingReducer'
import NoDataComponent from '../../../components/NoDataComponent'
import RecordingItemComponent from '../RecordingItem'
import { GetPlaybackInput, IRecordings } from '../../../models/recodings'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import { ROUTE_PATH } from '../../../enum/RoutePath'
import PaginationComponent from '../../../components/Pagination'
import { toast } from 'react-hot-toast'
import { formattedRecordingName } from '../../../utils'

const ListRecordingComponent = () => {
  const listRecordings = useAppSelector(selectListRecording)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const totalRecording = useAppSelector(selectTotalRecording)
  const recordingPage = useAppSelector(selectCurrentRecordingPage)

  const handleGetCameraPlayback = (recording: IRecordings) => {
    const data: GetPlaybackInput = {
      camera_id: recording.camera_id,
      selected_date: dayjs.utc(recording.created_at).format('YYYY-MM-DD'),
      recording_id:
        recording.record_type === 'uploaded' //if the type is uploaded, then pass in the uuid in the file_name field
          ? recording.file_name.split('.')[0] //exclude the file extension in the field
          : recording.id, // else, get the uuid from the id field
      type: recording?.record_type === '' ? 'recorded' : recording.record_type
    }
    dispatch(playRecording(recording))
    navigate(
      `${ROUTE_PATH.Recording}/${recording.camera_id}/${data.recording_id}/${
        data.selected_date
      }/${data.type}?${new URLSearchParams({
        record_name: formattedRecordingName(recording),
        duration: recording.duration.toString(),
        plugins: JSON.stringify(recording.va_plugins),
        size: recording.size.toString()
      }).toString()}`
    )
  }

  const onChangeRecordingPage = useCallback((page: number) => {
    dispatch(setCurrentRecordingPage(page))
  }, [])
  if (listRecordings.length === 0) {
    return <NoDataComponent />
  }
  return (
    <>
      <div className="h-fit flex-1 overflow-y-auto pb-4 mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-4 gap-12 list-none w-full">
        {listRecordings.map((recording) => (
          <RecordingItemComponent
            recording={recording}
            onPlayVideo={() => handleGetCameraPlayback(recording)}
            onDelete={() => toast.error('Not implemented')}
            key={recording.id}
          />
        ))}
      </div>

      <PaginationComponent
        totalItems={totalRecording}
        currentPage={recordingPage}
        onPageChange={onChangeRecordingPage}
        pageSize={RECORDING_PAGESIZE}
      />
    </>
  )
}

export default ListRecordingComponent
