import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import RecordingIcon from '../../../assets/svgs/RecordingIcon'
import VideoIcon from '../../../assets/svgs/VideoIcon'
import {
  selectDeletedRecordings,
  unSelectSingularDeletedRecording
} from '../../../stores/Reducers/recordingReducer'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import { formattedRecordingName } from '../../../utils'
import NoDataComponent from '../../../components/NoDataComponent'

const ConfirmDeleteMultipleRecordingsModal = () => {
  const deletedRecordings = useAppSelector(selectDeletedRecordings)
  const dispatch = useAppDispatch()
  if (!deletedRecordings) return null
  return (
    <div className="pr-2 flex-1 h-full overflow-auto flex flex-col gap-4">
      <div className="border-red-200 gap-3 bg-red-50 p-4 flex border border-solid rounded-lg">
        <ExclamationTriangleIcon className="min-h-5 min-w-5 max-h-5 max-w-5 stroke-red-600 mt-1" />
        <p className="text-red-800 text-sm font-medium">
          <strong className="text-red-800 font-bold">Warning:</strong> Deleting
          these recordings is an irreversible action and will result in the
          following:
        </p>
      </div>
      <div className="flex relative items-start gap-3 flex-1 overflow-auto h-full">
        <RecordingIcon className="h-5 w-5 sticky top-0.5 text-gray-500 mt-0.5" />
        <div className="flex-1 h-full flex flex-col">
          <div className="sticky top-0 bg-white">
            <p className="font-semibold text-sm">Recordings:</p>
            <p className="text-sm text-gray-600 font-medium mb-2">
              These recordings will be permanently removed from the system:
            </p>
          </div>
          <div className="space-y-1 h-full overflow-auto gap-2 flex-1">
            {deletedRecordings.length > 0 ? (
              deletedRecordings.map((recording) => (
                <div
                  key={recording.id}
                  className="flex items-center gap-4 justify-between bg-gray-50 rounded p-2 px-4"
                >
                  <span className="text-sm font-medium">
                    {formattedRecordingName(recording)}
                  </span>
                  <span
                    onClick={() =>
                      dispatch(unSelectSingularDeletedRecording(recording.id))
                    }
                    className={
                      'text-xs w-fit underline text-blue-400 cursor-pointer'
                    }
                  >
                    Remove
                  </span>
                </div>
              ))
            ) : (
              <NoDataComponent />
            )}
          </div>
        </div>
      </div>
      <div className="flex items-start gap-3">
        <VideoIcon className="h-5 w-5 text-gray-500 mt-0.5" />
        <div>
          <p className="font-semibold text-sm">Video Analytics Plugins</p>
          <p className="text-sm text-gray-600 font-medium">
            All of the VA plugins assigned to these recordings will be removed.
          </p>
        </div>
      </div>

      <div className="h-[1px] border-solid border-0 border-b border-b-gray-200"></div>
      <div className="border-amber-200 items-center gap-3 bg-amber-50 p-4 flex border border-solid rounded-lg">
        <ExclamationTriangleIcon className="min-h-5 min-w-5 max-h-5 max-w-5 stroke-amber-600 mt-1" />
        <p className="text-amber-800 text-sm font-medium">
          This action{' '}
          <strong className="text-amber-800 font-bold">cannot be undone</strong>
          .
        </p>
      </div>
    </div>
  )
}

export default ConfirmDeleteMultipleRecordingsModal
