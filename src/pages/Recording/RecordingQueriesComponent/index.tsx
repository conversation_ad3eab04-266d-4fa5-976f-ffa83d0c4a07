import TextInputComponent from '../../../components/TextInputComponent'
import SearchIcon from '../../../assets/svgs/SearchIcon'
import DropdownOptions from '../../../components/DropdownOptions'
import { useAppDispatch } from '../../../stores/hooks'
import {
  addRecordingFilters,
  clearAllFilter,
  fetchListRecordingThunk,
  removeRecordingFilters,
  RECORD_SORT_OPTIONS,
  RECORD_TYPE_OPTIONS,
  unSelectDeletedRecordingsAllPages
} from '../../../stores/Reducers/recordingReducer'
import SecondaryButton from '../../../components/SecondaryButtons'
import FilterIcon from '../../../assets/svgs/FilterIcon'
import ActiveFiltersComponent from '../../../components/ActiveFiltersComponent'
import { UpdateFilters } from '../../../interfaces'
import {
  ChevronDownIcon,
  ChevronUpIcon,
  TrashIcon
} from '@heroicons/react/20/solid'
import { DatePicker } from 'antd'
import { Dayjs } from 'dayjs'
import CustomModal from '../../../components/Modal'
import FilterModalComponent from '../../../components/FIilterModalComponent'
import { classNames } from '../../../utils'
import { useRecordingQuery } from './use-recording-query'
import ConfirmDeleteMultipleRecordingsModal from './delete-multiple-recording-modal'
import DeleteModalHeader from '../../../components/DeleteModalHeader'
import ConfirmModalFooter from '../../../components/ConfirmModalFooter'
import { XCircleIcon } from '@heroicons/react/24/outline'

type Props = {
  onMonthChange: (date: Dayjs | null) => void
  date: Dayjs | null
}

const RecordingQueriesComponent = ({ onMonthChange, date }: Props) => {
  const {
    openFilters,
    setOpenFilters,
    openConfirmDelete,
    setOpenConfirmDelete,
    isDeleting,
    onRemoveRecordingFilters,
    onClearAllFilters,
    onChangeRecordType,
    recordTypeValue,
    onSelectAllRecordings,
    onConfirmDeleteRecordings,
    handleSorting,
    listRecordings,
    orderBy,
    filteredAgent,
    filteredProject,
    filteredTag,
    filteredVA,
    deletedRecordings,
    params,
    setParams
  } = useRecordingQuery()
  const deletedRecordingsIds = new Set(deletedRecordings.map((item) => item.id))
  const currentListRecordingsIds = listRecordings.map((item) => item.id)
  const isDeletingCurrentPage = currentListRecordingsIds.every((id) =>
    deletedRecordingsIds.has(id)
  )
  const dispatch = useAppDispatch()
  const recordingSearchState = params.get('search') ?? ''
  return (
    <>
      <div className="w-full flex-wrap lg:flex-nowrap justify-start lg:justify-between flex my-2 items-center gap-6">
        <TextInputComponent
          prefixIcon={<SearchIcon />}
          onChange={(e) => setParams({ search: e.target.value })}
          placeholder="Search..."
          value={params.get('search') ?? ''}
        />
        <div className="sm:flex hidden items-center gap-6 justify-end">
          <DropdownOptions
            onSelect={handleSorting}
            label="Sort by"
            options={RECORD_SORT_OPTIONS}
            selected={orderBy}
          />
          <SecondaryButton
            className={
              'justify-between max-w-fit sm:min-w-[200px] sm:max-w-[200px]'
            }
            onClick={() => setOpenFilters(true)}
            backgroundColor="#f4f4f4"
          >
            <div className="flex items-center gap-2">
              <FilterIcon />
              <p className="hidden sm:block font-normal">Filters</p>
            </div>
          </SecondaryButton>
        </div>
      </div>
      <div className="sm:flex w-full hidden gap-2 flex-wrap mb-4">
        {[filteredAgent, filteredProject, filteredTag, filteredVA].flat()
          .length > 0 && (
          <ActiveFiltersComponent
            listActiveFilters={[
              filteredAgent,
              filteredProject,
              filteredTag,
              filteredVA
            ].flat()}
            onRemoveFilter={onRemoveRecordingFilters}
            clearAllFilter={onClearAllFilters}
          />
        )}
      </div>
      <div className="flex sm:flex-row flex-col items-center gap-3">
        <div className="flex w-full justify-between">
          {recordingSearchState ? (
            <h3 className="font-semibold sm:max-w-[70%]">
              Search results for "{recordingSearchState}"
              <span
                className="text-sm text-blue-400 underline font-medium ml-2 cursor-pointer"
                onClick={() => setParams({ search: '' })}
              >
                Clear
              </span>
            </h3>
          ) : (
            <div className="flex items-center">
              <ChevronUpIcon
                height={20}
                width={20}
                cursor={'pointer'}
                className="min-w-[20px] hidden sm:block min-h-[20px]"
                onClick={() =>
                  onMonthChange(date?.subtract(1, 'month') ?? date)
                }
              />
              <ChevronDownIcon
                height={20}
                width={20}
                cursor={'pointer'}
                className="min-w-[20px] hidden sm:block ml-3 min-h-[20px]"
                onClick={() => onMonthChange(date?.add(1, 'month') ?? date)}
              />

              <DatePicker
                picker="month"
                onChange={onMonthChange}
                allowClear={false}
                value={date}
                id="recording-month-pickers"
                className="recording-month-pickers hidden sm:block border-none ring-0 w-[180px] font-bold focus:ring-0"
                suffixIcon={<ChevronDownIcon height={20} width={20} />}
                format="MMMM YYYY"
              />
            </div>
          )}

          <div className="w-full justify-between sm:w-fit flex gap-2 items-center">
            <DropdownOptions
              label={''}
              labelIcon={
                <div className="flex gap-3 cursor-pointer items-center">
                  <p className="font-bold capitalize text-sm">
                    {recordTypeValue}
                  </p>
                  <ChevronDownIcon height={20} width={20} />
                </div>
              }
              onlyButtonIcon
              selected={recordTypeValue}
              options={RECORD_TYPE_OPTIONS}
              onSelect={onChangeRecordType}
            />
            <div className="flex w-full justify-between flex-row-reverse sm:hidden items-center gap-2 flex-1">
              <DropdownOptions
                onSelect={handleSorting}
                label="Sort by"
                options={RECORD_SORT_OPTIONS}
                selected={orderBy}
              />
              <SecondaryButton
                className={'justify-between max-w-[200px]'}
                onClick={() => setOpenFilters(true)}
                backgroundColor="#f4f4f4"
              >
                <div className="flex items-center gap-2">
                  <FilterIcon />
                  <p className="font-normal hidden sm:block">Filters</p>
                </div>
              </SecondaryButton>
            </div>
          </div>
        </div>
      </div>
      <div className="flex mt-2 gap-2 w-full justify-end items-center">
        {listRecordings.length > 0 && (
          <>
            <label
              htmlFor="delete_page"
              className="text-xs sm:text-sm font-medium min-w-fit"
            >
              Select All on Page
            </label>
            <div className="flex items-center">
              <TextInputComponent
                type="checkbox"
                checked={isDeletingCurrentPage}
                id="delete_page"
                onChange={(e) => onSelectAllRecordings(e.target.checked)}
                className="size-[18px] mx-1 sm:ml-3 border-solid border-[1.5px] border-gray-400 rounded-sm"
              />
            </div>
          </>
        )}
        {deletedRecordings.length > 0 && (
          <>
            <SecondaryButton
              isDisabled={deletedRecordings?.length === 0}
              onClick={() => dispatch(unSelectDeletedRecordingsAllPages())}
              className={classNames(
                'border !gap-1 sm:!gap-3 !px-2 max-w-fit sm:max-w-[150px] !font-medium border-solid',
                deletedRecordings?.length === 0 && 'opacity-40'
              )}
            >
              <XCircleIcon
                height={18}
                width={18}
                strokeWidth={2}
                stroke="#374151"
              />
              <div className="flex gap-0 sm:gap-1">
                <span className="hidden sm:block text-xs sm:text-sm font-medium text-gray-700">
                  Clear{' '}
                </span>
                <span className="">
                  {deletedRecordings?.length &&
                    `(${deletedRecordings?.length})`}
                </span>
              </div>
            </SecondaryButton>
            <SecondaryButton
              isDisabled={deletedRecordings?.length === 0}
              onClick={() => setOpenConfirmDelete(true)}
              className={classNames(
                'border !gap-1 sm:!gap-3 !px-2 max-w-fit sm:max-w-[150px] !text-[#D81414] !font-medium !bg-[#FFF2F2] border-solid border-red-400',
                deletedRecordings?.length === 0 && 'opacity-40'
              )}
            >
              <TrashIcon height={16} width={16} fill="#D81414" />
              <div className="flex gap-0 sm:gap-1">
                <span className="text-[#D81414] font-medium hidden sm:block">
                  Delete{' '}
                </span>
                <span className="text-[#D81414] font-medium">
                  {deletedRecordings?.length &&
                    `(${deletedRecordings?.length})`}
                </span>
              </div>
            </SecondaryButton>
          </>
        )}
      </div>
      <CustomModal
        title=""
        className="max-w-[50vw]"
        openState={[openFilters, setOpenFilters]}
      >
        <FilterModalComponent
          onAddFilters={(params: UpdateFilters) =>
            dispatch(addRecordingFilters(params))
          }
          onRemoveFilters={(params: UpdateFilters) =>
            dispatch(removeRecordingFilters(params))
          }
          closeModal={() => setOpenFilters(false)}
          onApply={() => dispatch(fetchListRecordingThunk())}
          clearAllFilters={() => dispatch(clearAllFilter())}
          filteredAgent={filteredAgent}
          filteredProject={filteredProject}
          filteredTag={filteredTag}
          filteredVA={filteredVA}
        />
      </CustomModal>
      <CustomModal
        title={
          <DeleteModalHeader
            title={`Delete ${deletedRecordings?.length} recordings`}
            description={`You are deleting ${deletedRecordings?.length} recordings.`}
          />
        }
        className="md:!min-w-[28rem] md:!max-w-md max-h-[90vh]"
        openState={[openConfirmDelete, setOpenConfirmDelete]}
      >
        <ConfirmDeleteMultipleRecordingsModal />
        <ConfirmModalFooter
          onCancel={() => setOpenConfirmDelete(false)}
          onConfirm={onConfirmDeleteRecordings}
          isLoading={isDeleting}
        />
      </CustomModal>
    </>
  )
}

export default RecordingQueriesComponent
