import { useCallback, useEffect, useMemo, useState } from 'react'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  clearAllFilter,
  fetchListRecordingThunk,
  removeRecordingFilters,
  selectCurrentRecordingPage,
  selectFilteredAgents,
  selectFilteredProjects,
  selectFilteredTags,
  selectFilteredVA,
  selectUploadRecordType,
  selectRecordingMonth,
  selectRecordingOrder,
  selectRecordingYear,
  setCurrentRecordingPage,
  setOrderBy,
  showAllRecordTypes,
  toggleRecordedRecordType,
  toggleUploadedRecordType,
  selectRecordedType,
  selectDeletedRecordings,
  deleteRecordingsThunk,
  selectAllRecordingInPage,
  selectListRecording,
  unSelectAllRecordingInPage
} from '../../../stores/Reducers/recordingReducer'
import { IOption } from '../../../interfaces'
import { FILTER_TYPES } from '../../../enum/FilterTypes'
import { useDebounce } from '../../../utils/hooks/useDebounce'
import { useSearchParams } from 'react-router-dom'

export const useRecordingQuery = () => {
  const [openFilters, setOpenFilters] = useState<boolean>(false)
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false)
  const [isDeleting, setIsDeleting] = useState<boolean>(false)
  const dispatch = useAppDispatch()
  const filteredAgent = useAppSelector(selectFilteredAgents)
  const filteredProject = useAppSelector(selectFilteredProjects)
  const filteredTag = useAppSelector(selectFilteredTags)
  const filteredVA = useAppSelector(selectFilteredVA)
  const orderBy = useAppSelector(selectRecordingOrder)
  const selectedMonth = useAppSelector(selectRecordingMonth)
  const selectedYear = useAppSelector(selectRecordingYear)
  const isOnlyUploaded = useAppSelector(selectUploadRecordType)
  const isOnlyRecorded = useAppSelector(selectRecordedType)
  const recordingPage = useAppSelector(selectCurrentRecordingPage)
  const deletedRecordings = useAppSelector(selectDeletedRecordings)
  const listRecordings = useAppSelector(selectListRecording)
  const [params, setParams] = useSearchParams()
  const handleSorting = (value: string) => {
    dispatch(setOrderBy(value))
  }

  const onRemoveRecordingFilters = useCallback(
    (item: IOption) =>
      dispatch(
        removeRecordingFilters({
          type: item.additonalValue as FILTER_TYPES,
          data: item
        })
      ),
    []
  )

  const onClearAllFilters = useCallback(() => dispatch(clearAllFilter()), [])

  const initialFetchData = async () => {
    await dispatch(fetchListRecordingThunk())
    isMounted.current = true
  }

  const onChangeRecordType = useCallback((type: string) => {
    switch (type) {
      case 'all':
        dispatch(showAllRecordTypes())
        break
      case 'uploaded':
        dispatch(toggleUploadedRecordType())
        break
      case 'recorded':
        dispatch(toggleRecordedRecordType())
        break
      default:
        dispatch(showAllRecordTypes())
        break
    }
  }, [])

  const recordTypeValue = useMemo(() => {
    return isOnlyUploaded ? 'uploaded' : isOnlyRecorded ? 'recorded' : 'all'
  }, [isOnlyRecorded, isOnlyUploaded])

  //function run when fetching data with queries
  const fetchRecordingDataWithQueries = () =>
    recordingPage > 1
      ? dispatch(setCurrentRecordingPage(1)) // changing the page back to 1
      : dispatch(fetchListRecordingThunk()) // refetch the data

  const onSelectAllRecordings = (checked: boolean) => {
    if (checked) {
      dispatch(selectAllRecordingInPage())
      return
    }
    dispatch(unSelectAllRecordingInPage())
  }

  const onConfirmDeleteRecordings = async () => {
    setIsDeleting(true)
    const deleteAction = await dispatch(deleteRecordingsThunk())
    setIsDeleting(false)
    if (deleteRecordingsThunk.rejected.match(deleteAction)) {
      return
    }
    setOpenConfirmDelete(false)
  }

  useEffect(() => {
    initialFetchData()
  }, [recordingPage])

  // fetching data when applying filters and sortings
  useEffect(() => {
    // specified isMounted condition to prevent duplicated request with the use effect above
    if (isMounted.current && !openFilters) {
      // only fetch by filters when filter modal not opened
      fetchRecordingDataWithQueries()
    }
  }, [
    filteredAgent,
    filteredProject,
    filteredTag,
    filteredVA,
    isOnlyUploaded,
    isOnlyRecorded,
    selectedMonth,
    selectedYear,
    orderBy
  ])

  const { isMounted } = useDebounce({
    func: fetchRecordingDataWithQueries,
    searchText: params.get('search') ?? ''
  })

  return {
    openFilters,
    setOpenFilters,
    openConfirmDelete,
    setOpenConfirmDelete,
    isDeleting,
    setIsDeleting,
    onRemoveRecordingFilters,
    onClearAllFilters,
    onChangeRecordType,
    recordTypeValue,
    onSelectAllRecordings,
    onConfirmDeleteRecordings,
    handleSorting,
    listRecordings,
    recordingPage,
    orderBy,
    filteredAgent,
    filteredProject,
    filteredTag,
    filteredVA,
    deletedRecordings,
    params,
    setParams
  }
}
