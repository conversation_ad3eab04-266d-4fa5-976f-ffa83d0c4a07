import { Progress, Tooltip } from 'antd'
import { LoaderIcon, toast } from 'react-hot-toast'
import { abortAllControllers, classNames } from '../../../utils'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  UPLOAD_TOAST_ID,
  commitAzureBlockThunk,
  controllers,
  refreshChunkProgress,
  selectChunkProgress
} from '../../../stores/Reducers/recordingReducer'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { MinusIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/20/solid'
import ConfirmModal from '../../../components/ConfirmModal'
import { IOption } from '../../../interfaces'
import { FileMetadata, ICommitRecordBlocks } from '../../../models/recodings'

type Props = {
  total: number
  project_id: string
  analytic_ids: IOption[]
  onReupload: (url?: string) => void
  file_name: string
  fileMetadata: FileMetadata | undefined
  record_name: string
}

const LoadingWithProgress = ({
  total = 0,
  file_name,
  onReupload,
  project_id = '',
  analytic_ids = [],
  fileMetadata,
  record_name
}: Props) => {
  const listProgressChunks = useAppSelector(selectChunkProgress)
  const [isMinimized, setIsMinimized] = useState<boolean>(false)
  const [confirmCancelUpload, setConfirmCancelUpload] = useState<boolean>(false)
  const dispatch = useAppDispatch()

  const onConfirmCancelUploading = useCallback(() => {
    abortAllControllers(controllers)
    toast.error(`Your file has been canceled!`, {
      duration: 2000,
      id: UPLOAD_TOAST_ID
    })
    setConfirmCancelUpload(false)
    dispatch(refreshChunkProgress()) // refresh all old data
    setTimeout(() => {
      toast.remove(UPLOAD_TOAST_ID)
    }, 3000)
  }, [])

  const onReUploadFailedChunks = async () => {
    const failedChunks = listProgressChunks.filter((chunk) => chunk.isError)
    failedChunks.forEach((chunk) => {
      onReupload(chunk.url)
    })
  }

  const renderHeaderText = useMemo(() => {
    const isError = listProgressChunks.some((chunk) => chunk.isError)

    if (isError) {
      return (
        <p className="mb-0 inline-flex items-center gap-1 text-sm text-gray-500">
          {`Failed to upload `}
          <span className="font-semibold">{file_name}.</span>{' '}
          <span
            onClick={onReUploadFailedChunks}
            className="text-sm underline cursor-pointer text-blue-600"
          >
            Retry?
          </span>
        </p>
      )
    }

    return (
      <p className="mb-0 inline-flex items-center gap-2 text-sm text-gray-500">
        {'Please wait while we upload your file...'}{' '}
        <span>
          <LoaderIcon />
        </span>
      </p>
    )
  }, [listProgressChunks])

  const renderProgressFormat = useCallback(
    (percent: number = 0) =>
      listProgressChunks.some((chunk) => chunk.isError) ? (
        <Tooltip
          title={listProgressChunks.find((chunk) => chunk.isError)?.errorMsg}
          mouseEnterDelay={0.5}
          getPopupContainer={(trigger) =>
            trigger.parentElement || document.body
          }
        >
          {' '}
          <XCircleIcon
            height={isMinimized ? 30 : 20}
            fill="red"
            width={isMinimized ? 30 : 20}
          />
        </Tooltip>
      ) : totalUploadedBytes === total ? (
        <CheckCircleIcon
          height={isMinimized ? 30 : 20}
          fill="green"
          width={isMinimized ? 30 : 20}
        />
      ) : (
        `${percent}%`
      ),
    [listProgressChunks, isMinimized]
  )

  const totalUploadedBytes = useMemo(
    () =>
      Math.floor(
        (listProgressChunks
          .map((chunk) => chunk.progress)
          .reduce((acc, currentValue) => acc + currentValue, 0) *
          100) /
          total
      ),
    [listProgressChunks]
  )

  const handleCommitUploadedChunks = async () => {
    const eTagList = listProgressChunks.map((chunk) => chunk.etag)
    const commitData: ICommitRecordBlocks = {
      record_name: record_name,
      block_list: eTagList,
      va_plugin_ids: analytic_ids?.map((analytic) => analytic.value) ?? [],
      project_id: project_id,
      duration: Math.ceil(fileMetadata?.duration ?? 0),
      size: fileMetadata?.size ?? 0,
      resolution: fileMetadata?.resolution ?? '',
      file_name: file_name
    }
    dispatch(commitAzureBlockThunk(commitData))
  }

  useEffect(() => {
    if (listProgressChunks.every((chunk) => chunk.etag !== '')) {
      handleCommitUploadedChunks()
    }
  }, [listProgressChunks])

  if (isMinimized) {
    return (
      <Tooltip
        title="Expand"
        mouseEnterDelay={0.5}
        getPopupContainer={(trigger) => trigger.parentElement || document.body}
      >
        <div
          className="cursor-pointer rounded-full shadow-lg bg-white"
          onClick={() => setIsMinimized(false)}
        >
          <Progress
            type="circle"
            size="small"
            format={renderProgressFormat}
            status={
              listProgressChunks.some((chunk) => chunk.isError)
                ? 'exception'
                : 'active'
            }
            className="shadow-2xl rounded-full"
            strokeWidth={10}
            percent={totalUploadedBytes}
          />
        </div>
      </Tooltip>
    )
  }
  return (
    <div
      className={classNames(
        'flex flex-col shadow-lg min-w-[350px] rounded-lg gap-4 p-4 bg-white max-w-[450px]'
      )}
    >
      <div className="flex w-full justify-between">
        <span className="font-semibold text-sm">{record_name}</span>
        <div className="text-sm text-end text-blue-400 gap-4 hover:underline cursor-pointer">
          <Tooltip
            title="Minimize"
            mouseEnterDelay={0.5}
            getPopupContainer={(trigger) =>
              trigger.parentElement || document.body
            }
          >
            <MinusIcon
              onClick={() => setIsMinimized(true)}
              height={20}
              width={20}
            />
          </Tooltip>
          <Tooltip
            title="Cancel"
            mouseEnterDelay={0.5}
            getPopupContainer={(trigger) =>
              trigger.parentElement || document.body
            }
          >
            <XMarkIcon
              onClick={() => setConfirmCancelUpload(true)}
              height={20}
              width={20}
            />
          </Tooltip>
        </div>
      </div>
      {renderHeaderText}
      <Progress
        status={
          listProgressChunks.some((chunk) => chunk.isError)
            ? 'exception'
            : 'active'
        }
        format={renderProgressFormat}
        className="flex justify-between w-full"
        percent={totalUploadedBytes}
      />
      <ConfirmModal
        onConfirm={onConfirmCancelUploading}
        text={'The uploaded data will be lost.'}
        openState={[confirmCancelUpload, setConfirmCancelUpload]}
        title={`Cancel uploading ${file_name}`}
      />
    </div>
  )
}

export default LoadingWithProgress
