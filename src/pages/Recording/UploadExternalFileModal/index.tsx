import { useCallback, useMemo, useRef } from 'react'
import UploadComponent from '../../../components/UploadComponent'
import { IOption } from '../../../interfaces'
import { ControllerRenderProps, useForm } from 'react-hook-form'
import FormInputContainer from '../../../components/FormInputComponent'
import VASelectComponent from '../../../components/CameraDetails/VASelectComponent'
import SecondaryButton from '../../../components/SecondaryButtons'
import PrimaryButton from '../../../components/PrimaryButtons'
import UploadedFileItem from '../../../components/UploadedFileItem'
import {
  FileMetadata,
  GetAzureUrlParams,
  UploadChunkParams,
  UploadRecordingParams
} from '../../../models/recodings'
import toast from 'react-hot-toast'
import { useAppDispatch } from '../../../stores/hooks'
import {
  PART_SIZE,
  SUPPORTED_RECORDINGS_FORMAT,
  UPLOAD_TOAST_ID,
  refreshChunkProgress,
  startUploading,
  updateChunkUrl,
  uploadRecordByChunkThunk
} from '../../../stores/Reducers/recordingReducer'
import { v4 as uuidv4 } from 'uuid'
import LoadingWithProgress from '../LoadingWithProgress'
import LazyProjectSelectComponent from '../../../components/LazyProjectSelectComponent'
import { getAzureURLS } from '../../../api/Recordings'
import { getErrorMessage } from '../../../utils'

type Props = {
  closeModal: () => void
}

const UploadExternalRecordingModal = ({ closeModal }: Props) => {
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    handleSubmit,
    watch,
    setValue
  } = useForm<UploadRecordingParams>({
    mode: 'onChange'
  })
  const selectedFiles: File[] | undefined = watch('files')
  const dispatch = useAppDispatch()
  const fileMetadata = useRef<FileMetadata>()

  const getSupportedFormatValue = useMemo(
    () => SUPPORTED_RECORDINGS_FORMAT.map((format) => format.value),
    []
  )

  const getSupportedFormatKey = useMemo(
    () => SUPPORTED_RECORDINGS_FORMAT.map((format) => format.label),
    []
  )

  const onHandleFileChange = useCallback(
    (files: File[], field: ControllerRenderProps<UploadRecordingParams>) => {
      field.onChange(files)
      getFileMetadata(files[0])
    },
    []
  )

  const onRemoveFile = useCallback(
    (file: File) =>
      setValue(
        'files',
        selectedFiles?.filter((selectedFile) => selectedFile.name !== file.name)
      ),
    []
  )

  const onReuploadFailedChunk = useCallback(
    (url: string | undefined, defaultUploadChunkParams: UploadChunkParams) =>
      dispatch(
        uploadRecordByChunkThunk({
          ...defaultUploadChunkParams,
          url: url
        })
      ),
    []
  )

  const startUploadByChunk = (
    listUrls: string[],
    data: UploadRecordingParams,
    file_name: string
  ) => {
    const defaultUploadChunkParams: UploadChunkParams = {
      index: 0,
      data: data
    }
    toast.custom(
      <LoadingWithProgress
        file_name={file_name}
        project_id={data.project_id}
        analytic_ids={data.analytic_ids}
        total={data.files?.[0].size ?? 0}
        record_name={data.files?.[0].name}
        fileMetadata={fileMetadata.current}
        onReupload={(url) =>
          onReuploadFailedChunk(url, defaultUploadChunkParams)
        } // index =0 because the index will be based on the url passed in
      />,
      {
        position: 'bottom-right',
        id: UPLOAD_TOAST_ID,
        duration: Infinity
      }
    )
    listUrls.forEach((_, index) => {
      dispatch(
        uploadRecordByChunkThunk({
          ...defaultUploadChunkParams,
          index: index
        })
      )
    })
  }

  const getFileMetadata = (file: File) => {
    const videoUrl = URL.createObjectURL(file)

    // Create a video element to load metadata
    const video = document.createElement('video')
    video.src = videoUrl

    // Listen for the loadedmetadata event to get resolution
    video.addEventListener('loadedmetadata', () => {
      fileMetadata.current = {
        duration: video.duration,
        resolution: `${video.videoWidth}x${video.videoWidth}`,
        size: file.size
      }
      URL.revokeObjectURL(videoUrl) // Clean up the URL object
    })
    video.remove()
  }

  const onSubmitUpload = async (data: UploadRecordingParams) => {
    toast.loading('Prepairing your uploading...', {
      position: 'top-center',
      id: UPLOAD_TOAST_ID,
      duration: Infinity
    })
    dispatch(startUploading())
    const uploadedFileFormat = SUPPORTED_RECORDINGS_FORMAT.find(
      (item) => item.value === data.files[0].type
    )
    const params: GetAzureUrlParams = {
      filename: `${uuidv4()}.${uploadedFileFormat?.label.toLocaleLowerCase()}`,
      total_chunks: Math.ceil(data.files[0].size / PART_SIZE)
    }
    try {
      const res = await getAzureURLS(params)
      closeModal()
      dispatch(updateChunkUrl(res))
      startUploadByChunk(res, data, params.filename)
    } catch (error) {
      toast.error(getErrorMessage(error), {
        id: UPLOAD_TOAST_ID,
        position: 'top-right',
        duration: 5000
      })
      dispatch(refreshChunkProgress())
    }
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmitUpload)}
      className="w-full flex flex-col h-full"
    >
      <FormInputContainer<UploadRecordingParams>
        label={''}
        name={'files'}
        vertialAlign
        required
        control={control}
        rules={{ required: 'Required' }}
        errors={errors}
        render={({ field }) => (
          <UploadComponent
            supportedFormats={getSupportedFormatValue}
            setSelectedFiles={(files: File[]) =>
              onHandleFileChange(files, field)
            }
            selectedFiles={field.value as File[]}
          />
        )}
      />
      <p className="text-[#7A7A7A] text-sm mb-4">
        Supported formats: {getSupportedFormatKey.join(', ')}
      </p>
      <FormInputContainer<UploadRecordingParams>
        control={control}
        label={'Project'}
        vertialAlign
        rules={{ required: 'Required' }}
        errors={errors}
        name={'project_id'}
        required
        render={({ field }) => <LazyProjectSelectComponent field={field} />}
      />
      <FormInputContainer<UploadRecordingParams>
        errors={errors}
        label="Select plugins"
        name="analytic_ids"
        vertialAlign
        control={control}
        render={({ field }) => (
          <VASelectComponent
            selectedValue={(field.value as IOption[]) ?? []}
            onChange={field.onChange}
          />
        )}
      />
      {selectedFiles?.map((file) => (
        <div className="flex flex-col" key={file.name}>
          <p className="text-sm font-semibold mt-2">Selected file:</p>
          <UploadedFileItem file={file} onRemoveFile={onRemoveFile} />
        </div>
      ))}
      <div className="flex w-full mt-4 justify-end bg-white pb-4 sticky gap-3 bottom-0">
        <SecondaryButton onClick={closeModal} className="sm:max-w-[200px]">
          Cancel
        </SecondaryButton>
        <PrimaryButton
          isDisabled={!isValid || isSubmitting}
          isLoading={isSubmitting}
          type="submit"
          className="sm:max-w-[200px]"
        >
          Upload
        </PrimaryButton>
      </div>
    </form>
  )
}

export default UploadExternalRecordingModal
