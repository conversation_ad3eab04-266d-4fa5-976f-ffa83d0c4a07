import { Skeleton } from 'antd'

const ListRecordingLoading = () => {
  return (
    <div className="h-fit flex-1 overflow-y-auto pb-4 mt-2 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-4 gap-12 list-none w-full">
      <div className="w-[300px] border border-solid cursor-pointer border-[#E3E3E3] h-fit rounded-[4px]">
        <div className="w-full flex items-center justify-between py-1 px-3 gap-[10px]">
          <div className="flex w-full gap-2 flex-col">
            <Skeleton.Image className="!w-full !h-[200px] mb-2" active />
            <Skeleton.Input active size="small" className="w-full" block />
            <Skeleton.Input active size="small" block />
          </div>
        </div>
      </div>
      <div className="w-[300px] border border-solid cursor-pointer border-[#E3E3E3] h-fit rounded-[4px]">
        <div className="w-full flex items-center justify-between py-1 px-3 gap-[10px]">
          <div className="flex w-full gap-2 flex-col">
            <Skeleton.Image className="!w-full !h-[200px] mb-2" active />
            <Skeleton.Input active size="small" className="w-full" block />
            <Skeleton.Input active size="small" block />
          </div>
        </div>
      </div>
      <div className="w-[300px] border border-solid cursor-pointer border-[#E3E3E3] h-fit rounded-[4px]">
        <div className="w-full flex items-center justify-between py-1 px-3 gap-[10px]">
          <div className="flex w-full gap-2 flex-col">
            <Skeleton.Image className="!w-full !h-[200px] mb-2" active />
            <Skeleton.Input active size="small" className="w-full" block />
            <Skeleton.Input active size="small" block />
          </div>
        </div>
      </div>
      <div className="w-[300px] border border-solid cursor-pointer border-[#E3E3E3] h-fit rounded-[4px]">
        <div className="w-full flex items-center justify-between py-1 px-3 gap-[10px]">
          <div className="flex w-full gap-2 flex-col">
            <Skeleton.Image className="!w-full !h-[200px] mb-2" active />
            <Skeleton.Input active size="small" className="w-full" block />
            <Skeleton.Input active size="small" block />
          </div>
        </div>
      </div>
      <div className="w-[300px] border border-solid cursor-pointer border-[#E3E3E3] h-fit rounded-[4px]">
        <div className="w-full flex items-center justify-between py-1 px-3 gap-[10px]">
          <div className="flex w-full gap-2 flex-col">
            <Skeleton.Image className="!w-full !h-[200px] mb-2" active />
            <Skeleton.Input active size="small" className="w-full" block />
            <Skeleton.Input active size="small" block />
          </div>
        </div>
      </div>
      <div className="w-[300px] border border-solid cursor-pointer border-[#E3E3E3] h-fit rounded-[4px]">
        <div className="w-full flex items-center justify-between py-1 px-3 gap-[10px]">
          <div className="flex w-full gap-2 flex-col">
            <Skeleton.Image className="!w-full !h-[200px] mb-2" active />
            <Skeleton.Input active size="small" className="w-full" block />
            <Skeleton.Input active size="small" block />
          </div>
        </div>
      </div>
    </div>
  )
}

export default ListRecordingLoading
