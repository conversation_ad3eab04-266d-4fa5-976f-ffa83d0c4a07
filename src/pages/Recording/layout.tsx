import { useEffect } from 'react'
import { Outlet } from 'react-router-dom'
import { useAppDispatch } from '../../stores/hooks'
import {
  setCurrentRecordingPage,
  unSelectDeletedRecordingsAllPages
} from '../../stores/Reducers/recordingReducer'

const RecordingPageLayout = () => {
  const dispatch = useAppDispatch()
  useEffect(() => {
    return () => {
      dispatch(setCurrentRecordingPage(1))
      dispatch(unSelectDeletedRecordingsAllPages())
    }
  }, [])

  return <Outlet />
}

export default RecordingPageLayout
