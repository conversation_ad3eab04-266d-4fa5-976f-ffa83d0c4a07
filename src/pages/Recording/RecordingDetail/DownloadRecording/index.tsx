import { useEffect, useMemo, useState } from 'react'
import parseHls from '../../../../api/Recordings/parse-hls'
import {
  SEGMENT,
  downloadFromAPI,
  formatBytes,
  getErrorMessage,
  secondsToHMS
} from '../../../../utils'
import PrimaryButton from '../../../../components/PrimaryButtons'
import { Skeleton, Slider } from 'antd'
import SecondaryButton from '../../../../components/SecondaryButtons'
import TextInputComponent from '../../../../components/TextInputComponent'
import {
  DownloadRecordingQuery,
  IRecordingSegment,
  RecordingType
} from '../../../../models/recodings'
import { LoaderIcon, toast } from 'react-hot-toast'
import { ArrowDownOnSquareIcon } from '@heroicons/react/24/outline'
import { useParams } from 'react-router-dom'
import { downloadRecording } from '../../../../api/Recordings'
import { CanceledError } from 'axios'
import { v4 as uuidv4 } from 'uuid'
import { useAppDispatch } from '../../../../stores/hooks'
import {
  addDownloadedFile,
  removeDownloadedFile
} from '../../../../stores/Reducers/recordingReducer'
type Props = {
  src: string
  recordingName: string
  onCancel: VoidFunction
}

const DownloadRecordingModal = ({ src, recordingName, onCancel }: Props) => {
  const {
    camera_id = '',
    recording_id = '',
    selected_date = '',
    type = ''
  } = useParams()
  const [tsFiles, setTsFiles] = useState<{ duration: number; data: string }[]>(
    []
  )
  const [minSegment, setMinSegment] = useState<number>(0)
  const [maxSegment, setMaxSegment] = useState<number>(0)
  const [fileName, setFileName] = useState<string>(recordingName)
  const [isLoadingSegments, setIsLoadingSegments] = useState<boolean>(true)
  const dispatch = useAppDispatch()

  async function getTsSegments() {
    if (!src) return
    setIsLoadingSegments(true)

    try {
      const getSegments: IRecordingSegment | undefined = await parseHls({
        hlsUrl: src
      })
      if (getSegments?.type !== SEGMENT) {
        toast.error(`Invalid segment url, Please refresh the page`)
        return
      }
      setTsFiles(
        getSegments.data.map((data) => {
          return {
            duration: data.duration,

            data: data.uri
          }
        })
      )
      setMaxSegment(getSegments.data.length)
    } catch (error) {
      toast.error(getErrorMessage(error))
    } finally {
      setIsLoadingSegments(false)
    }
  }

  const startDownload = async () => {
    const fileId = uuidv4()
    const abortController = new AbortController()
    dispatch(addDownloadedFile(fileId))
    const downloadToasterID = 'recording'
    toast.loading(<p className="w-[350px]">Your file is being downloaded</p>, {
      toasterId: downloadToasterID,
      id: fileId
    })

    const data: DownloadRecordingQuery = {
      start: minSegment,
      end: maxSegment === tsFiles.length ? maxSegment : maxSegment - 1,
      camera_id: camera_id,
      date: selected_date,
      record_id: recording_id,
      type: type as RecordingType
    }
    onCancel()
    try {
      const res = await downloadRecording(
        (progress) =>
          toast.loading(
            <div className="flex-1 pr-2">
              <p className="text-sm line-clamp-2">
                {fileName}_({secondsToHMS(calculateMinSegmentTimeStamp)} _
                {secondsToHMS(calculateMaxSegmentTimeStamp)}).zip
              </p>
              <div className="w-full gap-2 flex justify-between">
                <span className="text-xs text-gray-400">
                  {formatBytes(progress)} loaded
                </span>
                <span
                  onClick={() => {
                    abortController.abort()

                    setTimeout(() => {
                      toast.remove(fileId)
                      toast.error('Canceled', { position: 'top-center' })
                      dispatch(removeDownloadedFile(fileId))
                    }, 500)
                  }}
                  className="text-xs hover:cursor-pointer text-blue-400"
                >
                  Cancel
                </span>
              </div>
            </div>,
            {
              toasterId: downloadToasterID,
              id: fileId,
              icon: <LoaderIcon className="min-w-5 min-h-5" />
            }
          ),
        data,
        abortController
      )

      downloadFromAPI(
        res as BlobPart,
        `${fileName}_(${secondsToHMS(
          calculateMinSegmentTimeStamp
        )} _ ${secondsToHMS(calculateMaxSegmentTimeStamp)}).zip`
      )
      toast.dismiss(fileId)
      toast.success(`File ${fileName}.zip is successfully downloaded`, {
        position: 'top-center'
      })

      dispatch(removeDownloadedFile(fileId))
    } catch (error) {
      if (error instanceof CanceledError) return

      dispatch(removeDownloadedFile(fileId))
      toast.dismiss(fileId)

      toast.error(getErrorMessage(error), {
        position: 'top-center'
      })
      dispatch(removeDownloadedFile(fileId))
    }
  }

  const calculateMinSegmentTimeStamp = useMemo(
    () =>
      tsFiles
        .filter((_, index: number) => index < minSegment)
        .reduce((accumutator, current) => accumutator + current.duration, 0),
    [minSegment]
  )

  const calculateMaxSegmentTimeStamp = useMemo(
    () =>
      tsFiles
        .filter((_, index: number) => index < maxSegment)
        .reduce((accumutator, current) => accumutator + current.duration, 0),
    [maxSegment]
  )

  useEffect(() => {
    getTsSegments()
  }, [])

  if (isLoadingSegments) {
    return <Skeleton.Image className="min-w-full min-h-[300px]" active />
  }
  return (
    <div className="flex-1 block gap-4 bg-gray-100 p-4 rounded-lg">
      <p className="font-bold">Downloaded recording file:</p>
      <div className="w-full flex flex-col mt-2 gap-2">
        <label htmlFor="file_name" className="font-semibold text-sm">
          Title
        </label>
        <TextInputComponent
          id="file_name"
          defaultValue={fileName}
          onChange={(e) => setFileName(e.target.value)}
        />
      </div>
      <p className="mt-4 font-semibold text-sm">Downloaded parts:</p>
      <div className="flex gap-2 mb-4 items-center justify-center">
        <TextInputComponent
          disabled
          value={secondsToHMS(calculateMinSegmentTimeStamp)}
        />
        <span> - </span>
        <TextInputComponent
          disabled
          value={secondsToHMS(calculateMaxSegmentTimeStamp)}
        />
      </div>
      {tsFiles.length > 1 && (
        <Slider
          range
          tooltip={{
            formatter: (value) =>
              value === minSegment
                ? secondsToHMS(calculateMinSegmentTimeStamp)
                : secondsToHMS(calculateMaxSegmentTimeStamp)
          }}
          defaultValue={[minSegment, maxSegment]}
          max={tsFiles.length}
          onChange={(value) => {
            setMaxSegment(value[1])
            setMinSegment(value[0])
          }}
        />
      )}
      <div className="flex gap-2">
        <SecondaryButton onClick={onCancel}>Cancel</SecondaryButton>
        <PrimaryButton
          isDisabled={!fileName}
          className="mt-auto items-center flex"
          onClick={startDownload}
        >
          Download
          <ArrowDownOnSquareIcon stroke="white" width={20} height={20} />
        </PrimaryButton>
      </div>
    </div>
  )
}

export default DownloadRecordingModal
