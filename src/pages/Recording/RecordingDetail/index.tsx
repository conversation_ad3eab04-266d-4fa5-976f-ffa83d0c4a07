import {
  NavLink,
  useNavigate,
  useParams,
  useSearchParams
} from 'react-router-dom'
import HlsPlayer from '../../../components/HLSPlayer'
import TagComponent from '../../../components/Tags'
import { useEffect, useState } from 'react'
import { ICameras } from '../../../models/camera'
import { getCameraById } from '../../../api/Camera'
import { ENV, classNames, getErrorMessage, secondsToHMS } from '../../../utils'
import NavigateBackComponent from '../../../components/NavigateBackComponent'
import dayjs from 'dayjs'
import {
  getCameraRecording,
  getUploadedRecordings
} from '../../../api/Recordings'
import { GetPlaybackInput, RecordingType } from '../../../models/recodings'
import { Skeleton, Tooltip } from 'antd'
import { XCircleIcon } from '@heroicons/react/20/solid'
import PrimaryButton from '../../../components/PrimaryButtons'
import NoDataIcon from '../../../assets/svgs/NoDataIcon'
import DownloadRecordingModal from './DownloadRecording'
import { ROUTE_PATH } from '../../../enum/RoutePath'
import NoDataComponent from '../../../components/NoDataComponent'
import { IAnalytics } from '../../../models/analytics'

const RecordingDetailComponent = () => {
  const [params] = useSearchParams()
  const record_name = params.get('record_name') ?? ''
  const duration = Number(params.get('duration') ?? '')
  const size = Number(params.get('size') ?? '')
  const plugins: IAnalytics[] = JSON.parse(params.get('plugins') ?? '') ?? []
  const navigate = useNavigate()
  const { camera_id, recording_id, selected_date, type } = useParams()
  const [cameraDetail, setCameraDetail] = useState<ICameras>()
  const [playbackUrl, setPlaybackUrl] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [errorMsg, setErrorMsg] = useState<string>('')
  const [openDownload, setOpenDownload] = useState<boolean>(false)

  const getCameraDetail = async () => {
    try {
      const res = await getCameraById(camera_id ?? '')
      setCameraDetail(res.data)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    }
  }

  const handleGetCameraPlayback = async () => {
    setIsLoading(true)
    setErrorMsg('')
    const data: GetPlaybackInput = {
      camera_id: camera_id ?? '',
      selected_date: dayjs(selected_date).format('YYYY-MM-DD'),
      recording_id: recording_id ?? '',
      type: type as RecordingType
    }
    try {
      const res =
        type === 'uploaded'
          ? await getUploadedRecordings(data.recording_id)
          : await getCameraRecording(data)
      setPlaybackUrl(res)
    } catch (error) {
      setErrorMsg(getErrorMessage(error))
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    getCameraDetail()
    handleGetCameraPlayback()
  }, [])

  if (!record_name) {
    return <NoDataComponent />
  }
  return (
    <div className={classNames('flex-1 w-full flex flex-col gap-6 pr-2 pb-4')}>
      <NavigateBackComponent />

      {!isLoading && errorMsg === '' && (
        <>
          <div className="w-full gap-4 flex-1 flex flex-col md:flex-row">
            <div className="flex-2">
              <HlsPlayer src={playbackUrl} className="flex-1 h-full" />
            </div>
            {openDownload && (
              <DownloadRecordingModal
                onCancel={() => setOpenDownload(false)}
                recordingName={record_name}
                src={playbackUrl}
              />
            )}
          </div>
          {!openDownload && (
            <PrimaryButton onClick={() => setOpenDownload(true)}>
              Download
            </PrimaryButton>
          )}
        </>
      )}

      {isLoading && (
        <div className="w-full h-[500px] border border-solid cursor-pointer border-[#E3E3E3] rounded-[4px]">
          <div className="w-full h-full flex items-center justify-between py-1 px-3 gap-[10px]">
            <div className="flex w-full h-full gap-2 flex-col">
              <Skeleton.Image className="!w-full !h-full mb-2" active />
            </div>
          </div>
        </div>
      )}
      {errorMsg !== '' ? (
        <div className="text-center flex-1 justify-center flex flex-col items-center">
          <XCircleIcon
            aria-hidden="true"
            className="-ml-0.5 mr-1.5 fill-gray-500 max-h-20 max-w-20 min-h-20 min-w-20"
          />
          <h3 className="mt-2 text-sm font-semibold text-mainBlack">
            An error has occurred
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Your recording is not ready, please try again later
          </p>
          <div className="mt-6">
            <PrimaryButton
              onClick={() => navigate(0)}
              type="button"
              className="inline-flex items-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
            >
              Refresh
            </PrimaryButton>
          </div>
        </div>
      ) : (
        <div className="w-full max-h-[200px] flex-1 flex gap-6 flex-wrap">
          {cameraDetail ? (
            <div className="flex h-full flex-1 flex-col border border-solid border-[#E3E3E3] rounded-xl p-3 gap-[10px]">
              <p className="text-sm font-medium">Camera Information</p>
              <div className="w-full h-full overflow-auto flex-1 bg-[#F7F3FF] py-3 px-[18px] rounded-2xl flex flex-col gap-[10px]">
                <div className="flex w-full justify-between items-center gap-2">
                  <p className="text-xs font-medium">Camera</p>
                  <p className="text-xs truncate max-w-[70px] sm:max-w-[50%]">
                    {cameraDetail?.name}
                  </p>
                </div>
                <div className="flex w-full justify-between items-center gap-2">
                  <p className="text-xs font-medium">Agent</p>
                  <p className="text-xs truncate max-w-[70px] sm:max-w-[50%]">
                    {cameraDetail?.agent?.name}
                  </p>
                </div>
                <div className="flex w-full justify-between items-center gap-2">
                  <p className="text-xs font-medium">Project</p>
                  <p className="text-xs truncate max-w-[70px] sm:max-w-[50%]">
                    {cameraDetail?.project?.name}
                  </p>
                </div>
                <div className="flex w-full justify-between gap-14">
                  <p className="text-xs font-medium">Tags</p>
                  {cameraDetail?.tags?.length === 0 ? (
                    <p className="text-xs line-clamp-5">No Information</p>
                  ) : (
                    <div className="flex flex-wrap justify-end gap-1">
                      {cameraDetail?.tags?.map((tag) => (
                        <TagComponent
                          key={tag.id}
                          text={tag.name}
                          fontSize="12px"
                          backgroundColor="white"
                          textColor="black"
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-1 flex-col border border-solid border-[#E3E3E3] rounded-xl p-3 gap-[10px]">
              <div className="w-full flex flex-col justify-center items-center gap-2 h-full">
                <NoDataIcon />
                <p className="font-semibold text-gray-400">No Information</p>
              </div>
            </div>
          )}
          <div className="flex flex-1 flex-col border border-solid border-[#E3E3E3] rounded-xl p-3 gap-[10px]">
            <p className="text-sm font-medium">File Information</p>
            <div className="w-full flex-1 bg-[#F7F3FF] py-3 px-[18px] rounded-2xl flex flex-col gap-[10px]">
              <div className="flex w-full flex-wrap justify-between items-center gap-2">
                <p className="text-xs font-medium">File name</p>
                <p
                  title={record_name}
                  className="text-xs truncate max-w-[70px] sm:max-w-[50%]"
                >
                  {record_name}
                </p>
              </div>
              <div className="flex w-full justify-between items-center gap-2">
                <p className="text-xs font-medium">Size</p>
                <p className="text-xs truncate max-w-[70px] sm:max-w-[50%]">
                  {Math.floor(size / (1024 * 1024))} MB
                </p>
              </div>
              <div className="flex w-full justify-between items-center gap-2">
                <p className="text-xs font-medium">Duration</p>
                <p className="text-xs truncate max-w-[70px] sm:max-w-[50%]">
                  {secondsToHMS(duration)}
                </p>
              </div>
              <div className="flex w-full justify-between items-center gap-2">
                <p className="text-xs font-medium">Va Plugins</p>
                <Tooltip
                  title={(plugins ?? [])
                    .map((analytic) => analytic.name)
                    .join(', ')}
                  className="text-xs flex gap-2 truncate max-w-[70px] sm:max-w-[50%]"
                  mouseEnterDelay={0.5}
                >
                  {(plugins ?? []).map((analytic) => (
                    <NavLink
                      key={analytic.id}
                      className={'underline text-primary'}
                      to={`${ROUTE_PATH.VideoAnalytics}/${
                        analytic.id
                      }?${new URLSearchParams({
                        fileName: record_name
                      })}`}
                    >
                      {analytic.name},
                    </NavLink>
                  ))}
                </Tooltip>
              </div>
            </div>
          </div>
          <div className="flex flex-1 flex-col border border-solid border-[#E3E3E3] rounded-xl p-3 gap-[10px]">
            <p className="text-sm font-medium">Notes</p>
            <div className="w-full flex-1 bg-[#F7F3FF] py-3 px-[18px] rounded-2xl flex flex-col gap-[10px]">
              <p className="text-sm line-clamp-5">
                {cameraDetail?.notes !== ''
                  ? cameraDetail?.notes
                  : 'No Information'}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default RecordingDetailComponent
