import PrimaryButton from '../../components/PrimaryButtons'
import SearchIcon from '../../assets/svgs/SearchIcon'
import { useParamsPagination } from '../../utils/hooks/useParamsPagination'
import { ROUTE_PATH } from '../../enum/RoutePath'
import { IOption, UpdateFilters } from '../../interfaces'
import { useEffect, useState } from 'react'
import DropdownOptions from '../../components/DropdownOptions'
import FilterIcon from '../../assets/svgs/FilterIcon'
import CustomModal from '../../components/Modal'
import SecondaryButton from '../../components/SecondaryButtons'
import {
  addCameraFilters,
  clearAllFilter,
  fetchListCameraThunk,
  removeCameraFilters,
  selectCameraOrderBy,
  selectFilterAgents,
  selectFilterProjects,
  selectFilterTags,
  selectFilterVA,
  selectListCamera,
  selectListCameraLoading,
  selectTotalCamera,
  toggleOrder
} from '../../stores/Reducers/cameraReducers'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import CameraDetailComponent from '../../components/CameraDetails'
import ListCameraTable from '../../components/ListCameraTable'
import { ListCameraFilterParams } from '../../models/camera'
import TagComponent from '../../components/Tags'
import { DEFAULT_PAGE_SIZE } from '../../utils'
import { useDebounce } from '../../utils/hooks/useDebounce'
import TextInputComponent from '../../components/TextInputComponent'
import { FILTER_TYPES } from '../../enum/FilterTypes'
import FilterModalComponent from '../../components/FIilterModalComponent'
import PaginationComponent from '../../components/Pagination'
import PageHeaderText from '../../components/PageHeaderText'
const options: IOption[] = [
  { value: 'created_at-desc', label: 'Newest to oldest' },
  { value: 'created_at-asc', label: 'Oldest to latest' },
  { value: 'name-asc', label: 'Camera name (A-Z)' },
  { value: 'name-desc', label: 'Camera name (Z-A)' },
  { value: 'status-asc', label: 'Status (Online first)' },
  { value: 'status-desc', label: 'Status (Offline first)' }
]

const CameraManagementPage = () => {
  const { page, pageSize, navigatePage } = useParamsPagination({
    navigatePath: ROUTE_PATH.Camera
  })
  const listCamera = useAppSelector(selectListCamera)
  const listCameraLoading = useAppSelector(selectListCameraLoading)
  const totalCamera = useAppSelector(selectTotalCamera)
  const orderBy = useAppSelector(selectCameraOrderBy)
  const dispatch = useAppDispatch()
  const [openFilters, setOpenFilters] = useState<boolean>(false)
  const [addModalOpen, setAddModalOpen] = useState<boolean>(false)
  const [searchText, setSearchText] = useState<string>('')
  const [isCreateFormDirty, setIsCreateFormDirty] = useState<boolean>(false)
  const filteredAgent = useAppSelector(selectFilterAgents)
  const filteredProject = useAppSelector(selectFilterProjects)
  const filteredTag = useAppSelector(selectFilterTags)
  const filteredVA = useAppSelector(selectFilterVA)
  const handleFetchListCamera = async () => {
    const data: ListCameraFilterParams = {
      'page-no': Number(page),
      'page-size': Number(pageSize),
      'order-by': orderBy.split('-')[0],
      'order-direction': orderBy.split('-')[1],
      search: searchText.trim()
    }
    if (filteredProject.length > 0) {
      data['project-ids'] = filteredProject.map((project) => project.value)
    }
    if (filteredTag.length > 0) {
      data['tag-ids'] = filteredTag.map((tag) => tag.value)
    }
    if (filteredAgent.length > 0) {
      data['agent-ids'] = filteredAgent.map((agent) => agent.value)
    }
    if (filteredVA.length > 0) {
      data['va-plugin-ids'] = filteredVA.map((va) => va.value)
    }
    await dispatch(fetchListCameraThunk(data))
    isMounted.current = true
  }

  const fetchListCameraWithFilterSorting = () =>
    page === '1' ? handleFetchListCamera() : navigatePage(1, DEFAULT_PAGE_SIZE)

  const handleRemoveFilter = (type: FILTER_TYPES, item: IOption) => {
    dispatch(
      removeCameraFilters({
        type: type,
        data: item
      })
    )
  }

  const handleSorting = (value: string) => {
    dispatch(toggleOrder(value))
  }

  const handleRefetchCamerasAfterDelete = () =>
    listCamera.length === 1 && Number(page) > 1 // if the last camera on the page, navigate to the previous page
      ? navigatePage(Number(page) - 1, DEFAULT_PAGE_SIZE)
      : fetchListCameraWithFilterSorting()

  useEffect(() => {
    handleFetchListCamera()
  }, [page, pageSize])

  useEffect(() => {
    if (isMounted.current) {
      fetchListCameraWithFilterSorting()
    }
  }, [orderBy])

  const { isMounted } = useDebounce({
    func: fetchListCameraWithFilterSorting, //debounce search with text
    searchText: searchText
  })

  useEffect(() => {
    if (isMounted.current && !openFilters) {
      // only fetch by filters when filter modal not opened
      fetchListCameraWithFilterSorting()
    }
  }, [filteredAgent, filteredProject, filteredTag, filteredVA])

  return (
    <div className="h-full w-full flex flex-col">
      <div className="flex w-full flex-wrap items-center justify-between gap-2">
        <PageHeaderText>Camera Management</PageHeaderText>
        <PrimaryButton
          onClick={() => setAddModalOpen(true)}
          className="sm:max-w-[200px] py-[10px]"
        >
          Add Camera
        </PrimaryButton>
      </div>

      <div className="w-full flex-wrap lg:flex-nowrap justify-start lg:justify-between flex my-2 items-center gap-6">
        <TextInputComponent
          prefixIcon={<SearchIcon />}
          onChange={(e) => setSearchText(e.target.value)}
          placeholder="Search..."
        />
        <DropdownOptions
          onSelect={handleSorting}
          label="Sort by"
          options={options}
          selected={orderBy}
        />
        <SecondaryButton
          className={'justify-between sm:max-w-[200px]'}
          backgroundColor="#f4f4f4"
          onClick={() => setOpenFilters(true)}
        >
          <div className="flex items-center gap-2">
            <FilterIcon />
            <p className="font-normal">Filters</p>
          </div>
        </SecondaryButton>
      </div>
      <div className="flex gap-2 mb-4 w-full flex-wrap">
        {filteredAgent.map((item) => (
          <TagComponent
            key={item.value}
            text={item.label}
            removable
            textColor="#843BAC"
            border="1px solid #843BAC"
            onRemove={() => handleRemoveFilter(FILTER_TYPES.AGENTS, item)}
          />
        ))}
        {filteredProject.map((item) => (
          <TagComponent
            key={item.value}
            text={item.label}
            removable
            textColor="#843BAC"
            border="1px solid #843BAC"
            onRemove={() => handleRemoveFilter(FILTER_TYPES.PROJECTS, item)}
          />
        ))}
        {filteredTag.map((item) => (
          <TagComponent
            key={item.value}
            text={item.label}
            removable
            textColor="#843BAC"
            border="1px solid #843BAC"
            onRemove={() => handleRemoveFilter(FILTER_TYPES.TAGS, item)}
          />
        ))}
        {filteredVA.map((item) => (
          <TagComponent
            key={item.value}
            text={item.label}
            onRemove={() => handleRemoveFilter(FILTER_TYPES.VA_PLUGINS, item)}
            removable
            textColor="#843BAC"
            border="1px solid #843BAC"
          />
        ))}
      </div>
      <ListCameraTable
        page={Number(page)}
        listCamera={listCamera}
        refetchCameraList={handleFetchListCamera}
        loading={listCameraLoading}
        refetchCameraListAfterDelete={handleRefetchCamerasAfterDelete}
      />

      {listCamera.length > 0 && (
        <PaginationComponent
          totalItems={totalCamera}
          currentPage={Number(page)}
          onPageChange={(page) => navigatePage(page, DEFAULT_PAGE_SIZE)}
          pageSize={DEFAULT_PAGE_SIZE}
        />
      )}
      <CustomModal
        title=""
        className="max-w-[50vw]"
        openState={[openFilters, setOpenFilters]}
      >
        <FilterModalComponent
          onAddFilters={(params: UpdateFilters) =>
            dispatch(addCameraFilters(params))
          }
          onRemoveFilters={(params: UpdateFilters) =>
            dispatch(removeCameraFilters(params))
          }
          closeModal={() => setOpenFilters(false)}
          onApply={fetchListCameraWithFilterSorting}
          clearAllFilters={() => dispatch(clearAllFilter())}
          filteredAgent={filteredAgent}
          filteredProject={filteredProject}
          filteredTag={filteredTag}
          filteredVA={filteredVA}
        />
      </CustomModal>
      <CustomModal
        title="Add Camera"
        isModalDirty={isCreateFormDirty}
        openState={[addModalOpen, setAddModalOpen]}
      >
        <CameraDetailComponent
          isFormDirty={isCreateFormDirty}
          setIsFormDirty={setIsCreateFormDirty}
          refetchListCamera={fetchListCameraWithFilterSorting}
          setModalOpen={setAddModalOpen}
        />
      </CustomModal>
    </div>
  )
}

export default CameraManagementPage
