import { useEffect, useRef, useState } from 'react'
import { accountRecover, verifyOTP } from '../../api/Auth'
import OTPInput from '../../components/OTPInput'
import { classNames, getErrorMessage } from '../../utils'
import PrimaryButton from '../../components/PrimaryButtons'
import { LoaderIcon } from 'react-hot-toast'
import { AuthVerifyInput } from '../../models/auth'
import ResetPasswordForm from '../Auth/ResetPassword/ResetPasswordForm'
import { useAppSelector } from '../../stores/hooks'
import { selectCurrentUser } from '../../stores/Reducers/usersReducer'
import { useLocation } from 'react-router-dom'

const ChangePasswordComponent = () => {
  const location = useLocation()
  const accessToken = useRef<string>('')
  const refreshToken = useRef<string>('')
  const currentUser = useAppSelector(selectCurrentUser)
  const [otp, setOtp] = useState<string[]>(Array(6).fill(''))
  const [resendConfirmMsg, setResendConfirmMsg] = useState<string>('')
  const [isResendingOTP, setIsResendingOTP] = useState<boolean>(false)
  const [isVerifyOTP, setIsVerifyOTP] = useState<boolean>(false)
  const [changePasswordPhases, setChangePasswordPhases] = useState<
    'otp' | 'password'
  >(location.state?.accessToken ? 'password' : 'otp')
  const isMounted = useRef<boolean>(false)
  // send an OTP through email
  const handleRequestChangePassword = async (resend_confirm?: string) => {
    isMounted.current = true
    setIsResendingOTP(true)
    const data = {
      email: currentUser?.email ?? ''
    }
    try {
      await accountRecover(data)
      setResendConfirmMsg(resend_confirm ?? '')
    } catch (error) {
      setResendConfirmMsg(getErrorMessage(error))
    } finally {
      setIsResendingOTP(false)
    }
  }

  //verify the OTP
  const handleVerifyOTP = async () => {
    setIsVerifyOTP(true)
    const data: AuthVerifyInput = {
      type: 'magiclink',
      token: otp.join(''),
      email: currentUser?.email ?? ''
    }
    try {
      const res = await verifyOTP(data)
      accessToken.current = res.access_token
      refreshToken.current = res.refresh_token
      setChangePasswordPhases('password')
    } catch (error) {
      setResendConfirmMsg(getErrorMessage(error))
    } finally {
      setIsVerifyOTP(false)
    }
  }
  useEffect(() => {
    if (isMounted.current === false) {
      handleRequestChangePassword()
    }
  }, [currentUser])

  return (
    <div className="w-full flex flex-col h-full mt-12 items-center">
      {changePasswordPhases === 'password' ? (
        <div className="w-1/2 h-1/2 flex flex-col gap-8">
          <p className="text-center font-bold text-2xl">Change your password</p>
          <ResetPasswordForm
            email={currentUser?.email ?? ''}
            token={location.state?.accessToken ?? accessToken.current}
            refreshToken={location.state?.refreshToken ?? refreshToken.current}
          />
        </div>
      ) : (
        <>
          <div className="sm:mx-auto sm:w-full sm:max-w-md">
            <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-mainBlack">
              Enter Verification Code
            </h2>
            <p className="my-3 text-sm text-center">
              We have sent a code to {currentUser?.email ?? ''}
            </p>
          </div>

          <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
            <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
              <OTPInput
                otpState={[otp, setOtp]}
                onOTPFilled={() => handleVerifyOTP()}
              />
              <PrimaryButton
                isLoading={isVerifyOTP || isResendingOTP}
                isDisabled={isVerifyOTP}
                className="my-8"
              >
                Enter
              </PrimaryButton>
              <p className="text-sm text-center">
                Didn't receive a code?{' '}
                <span
                  onClick={() =>
                    handleRequestChangePassword(
                      'We have sent another code to ' + currentUser?.email
                    )
                  }
                  className="underline text-primary cursor-pointer"
                >
                  Resend code
                  {isResendingOTP && (
                    <LoaderIcon className="h-4 w-4 inline-block ml-2 no-underline" />
                  )}
                </span>
              </p>
              {!isResendingOTP && (
                <p
                  className={classNames(
                    'text-sm text-center font-semibold mt-2',
                    resendConfirmMsg.includes('We have sent another code to')
                      ? 'text-green-500'
                      : 'text-red-400'
                  )}
                >
                  {resendConfirmMsg}
                </p>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default ChangePasswordComponent
