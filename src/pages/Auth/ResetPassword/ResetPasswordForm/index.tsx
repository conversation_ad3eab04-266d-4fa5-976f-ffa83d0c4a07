import { useForm } from 'react-hook-form'
import { EmailPasswordAuthenication } from '../../../../interfaces'
import { REGEX_PASSWORD } from '../../../../enum/Regex'
import { COOKIE_EXPIRE_TIME, getErrorMessage } from '../../../../utils'
import PrimaryButton from '../../../../components/PrimaryButtons'
import { ResetPasswordInput } from '../../../../models/auth'
import { editUserCredential } from '../../../../api/Auth'
import { toast } from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'
import { ROUTE_PATH } from '../../../../enum/RoutePath'
import TextInputComponent from '../../../../components/TextInputComponent'
import FormInputContainer from '../../../../components/FormInputComponent'
import { useState } from 'react'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { KEY_STORAGE } from '../../../../enum/KeyStorage'
import cookie from 'js-cookie'

type Props = {
  email: string
  token: string
  refreshToken?: string
}

const ResetPasswordForm = ({ email, token, refreshToken }: Props) => {
  const navigate = useNavigate()
  const {
    control,
    formState: { errors, isSubmitting },
    handleSubmit,
    watch,
    setError
  } = useForm<ResetPasswordInput>({
    mode: 'onChange'
  })
  const [showNewPassword, setShowNewPassword] = useState<boolean>(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState<boolean>(false)
  const newPassword = watch('newPassword')
  const handleResetPassword = async (value: ResetPasswordInput) => {
    const data: EmailPasswordAuthenication = {
      email: email,
      password: value.newPassword
    }
    try {
      await editUserCredential(data, token)
      toast.success('Your password is successfully reset!')
      if (!refreshToken) {
        navigate(ROUTE_PATH.Login)
        return
      }

      cookie.set(KEY_STORAGE.REFRESH_TOKEN, refreshToken, {
        expires: new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
      })

      navigate(ROUTE_PATH.Login)
    } catch (error) {
      setError('newPassword', {
        type: 'custom',
        message: getErrorMessage(error)
      })
      setError('confirmNewPassword', {
        type: 'custom',
        message: getErrorMessage(error)
      })
    }
  }
  return (
    <form onSubmit={handleSubmit(handleResetPassword)} className="w-full">
      <div className="flex items-center gap-2">
        <FormInputContainer<ResetPasswordInput>
          control={control}
          endfixIcon={
            !showNewPassword ? (
              <EyeIcon
                onClick={() => setShowNewPassword((prev) => !prev)}
                cursor={'pointer'}
                className="mt-1"
                height={20}
                width={20}
              />
            ) : (
              <EyeSlashIcon
                onClick={() => setShowNewPassword((prev) => !prev)}
                cursor={'pointer'}
                height={20}
                className="mt-1"
                width={20}
              />
            )
          }
          vertialAlign
          required
          label={'New password'}
          name={'newPassword'}
          errors={errors}
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_PASSWORD,
              message:
                ' Must be at least 8 characters, maximum of 16 characters, contain atleast 1 lowercase, 1 uppercase, 1 number and 1 special character'
            }
          }}
          render={({ field }) => (
            <TextInputComponent
              placeholder="Enter your new password"
              type={showNewPassword ? 'text' : 'password'}
              {...field}
            />
          )}
        />
      </div>
      <FormInputContainer<ResetPasswordInput>
        vertialAlign
        required
        endfixIcon={
          !showConfirmPassword ? (
            <EyeSlashIcon
              onClick={() => setShowConfirmPassword((prev) => !prev)}
              cursor={'pointer'}
              className="mt-1"
              height={20}
              width={20}
            />
          ) : (
            <EyeIcon
              onClick={() => setShowConfirmPassword((prev) => !prev)}
              cursor={'pointer'}
              height={20}
              className="mt-1"
              width={20}
            />
          )
        }
        label={'Confirm new password'}
        name={'confirmNewPassword'}
        errors={errors}
        control={control}
        rules={{
          required: 'Required',
          validate: (value) => value === newPassword || 'Passwords do not match'
        }}
        render={({ field }) => (
          <TextInputComponent
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Confirm your new password"
            {...field}
          />
        )}
      />

      <PrimaryButton className="mt-4" isDisabled={isSubmitting} type="submit">
        Enter
      </PrimaryButton>
    </form>
  )
}

export default ResetPasswordForm
