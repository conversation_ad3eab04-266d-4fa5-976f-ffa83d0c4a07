import { useForm } from 'react-hook-form'
import { classNames, getErrorMessage } from '../../../utils'
import PrimaryButton from '../../../components/PrimaryButtons'
import { REGEX_EMAIL } from '../../../enum/Regex'
import { accountRecover, verifyOTP } from '../../../api/Auth'
import { LoaderIcon } from 'react-hot-toast'
import { useRef, useState } from 'react'
import OTPInput from '../../../components/OTPInput'
import { AuthVerifyInput } from '../../../models/auth'
import ResetPasswordForm from './ResetPasswordForm'
import TextInputComponent from '../../../components/TextInputComponent'
import FormInputContainer from '../../../components/FormInputComponent'
import { useLocation } from 'react-router-dom'

const ResetPasswordComponent = () => {
  const location = useLocation()

  const [resetPhase, setResetPhase] = useState<'email' | 'otp' | 'password'>( // each phase of the reset process
    location.state?.accessToken ? 'password' : 'email'
  )
  const [otp, setOtp] = useState<string[]>(Array(6).fill(''))
  const [resendConfirmMsg, setResendConfirmMsg] = useState<string>('')
  const [isResendingOTP, setIsResendingOTP] = useState<boolean>(false)
  const [isVerifyOTP, setIsVerifyOTP] = useState<boolean>(false)
  const {
    control,
    formState: { errors, isSubmitting },
    handleSubmit,
    getValues,
    setError
  } = useForm<{ email: string }>({
    mode: 'onChange'
  })
  const verifiedToken = useRef<string>('')

  const onSubmitEmail = async (value: { email: string }) => {
    try {
      await accountRecover(value)
      setResetPhase('otp')
    } catch (error) {
      setError('email', { type: 'custom', message: getErrorMessage(error) })
    }
  }

  const handleResendOTP = async () => {
    if (!isResendingOTP) {
      setIsResendingOTP(true)
      const data: { email: string } = {
        email: getValues().email
      }
      try {
        await accountRecover(data)
        setResendConfirmMsg('We have sent an email to ' + getValues().email)
      } catch (error) {
        setResendConfirmMsg(getErrorMessage(error))
      } finally {
        setIsResendingOTP(false)
      }
    }
  }

  const handleVerifyOTP = async () => {
    setIsVerifyOTP(true)
    const data: AuthVerifyInput = {
      type: 'recovery',
      token: otp.join(''),
      email: getValues().email
    }
    try {
      const res = await verifyOTP(data)
      verifiedToken.current = res.access_token
      setResetPhase('password')
    } catch (error) {
      setResendConfirmMsg(getErrorMessage(error))
    } finally {
      setIsVerifyOTP(false)
    }
  }

  const renderResetPasswordBody = () => {
    switch (resetPhase) {
      case 'email':
        return (
          <>
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
              <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-mainBlack">
                Forgot Password
              </h2>
            </div>
            <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
              <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
                <form onSubmit={handleSubmit(onSubmitEmail)} className="w-full">
                  <FormInputContainer<{ email: string }>
                    control={control}
                    vertialAlign
                    required
                    label={'Email'}
                    name={'email'}
                    errors={errors}
                    rules={{
                      required: 'Required',
                      pattern: {
                        value: REGEX_EMAIL,
                        message: 'Invalid email address'
                      }
                    }}
                    render={({ field }) => (
                      <TextInputComponent
                        placeholder="Enter your email"
                        type="email"
                        {...field}
                      />
                    )}
                  />
                  <PrimaryButton
                    isLoading={isSubmitting}
                    isDisabled={isSubmitting}
                    type="submit"
                  >
                    Enter
                  </PrimaryButton>
                </form>
              </div>
            </div>
          </>
        )
      case 'otp':
        return (
          <>
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
              <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-mainBlack">
                Enter Verification Code
              </h2>
              <p className="my-3 text-sm text-center">
                If this user exists, we will send a code to{' '}
                <span className="font-semibold">{getValues().email}</span>
              </p>
            </div>

            <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
              <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
                <OTPInput
                  otpState={[otp, setOtp]}
                  onOTPFilled={() => handleVerifyOTP()}
                />
                <PrimaryButton
                  isLoading={isVerifyOTP}
                  isDisabled={isVerifyOTP}
                  className="my-8"
                >
                  Enter
                </PrimaryButton>
                <p className="text-sm text-center">
                  Didn't receive a code?{' '}
                  <span
                    onClick={handleResendOTP}
                    className="underline text-primary cursor-pointer"
                  >
                    Resend code
                    {isResendingOTP && (
                      <LoaderIcon className="h-4 w-4 inline-block ml-2 no-underline" />
                    )}
                  </span>
                </p>
                {!isResendingOTP && (
                  <p
                    className={classNames(
                      'text-sm text-center font-semibold mt-2',
                      resendConfirmMsg.includes('We have sent an email to')
                        ? 'text-green-500'
                        : 'text-red-400'
                    )}
                  >
                    {resendConfirmMsg}
                  </p>
                )}
              </div>
            </div>
          </>
        )
      case 'password':
        return (
          <>
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
              <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-mainBlack">
                Reset Password
              </h2>
            </div>
            <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
              <div className="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
                <ResetPasswordForm
                  token={location.state?.accessToken ?? verifiedToken.current}
                  email={getValues().email}
                />
              </div>
            </div>
          </>
        )
      default:
        break
    }
  }

  return <>{renderResetPasswordBody()}</>
}

export default ResetPasswordComponent
