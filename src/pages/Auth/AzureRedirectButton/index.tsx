import { AUTH_ENDPOINTS } from '../../../api/Endpoints'
import AzureIcon from '../../../assets/svgs/AzureIcon'

const AzureRedirectButton = () => {
  return (
    <a
      href={`${import.meta.env.VITE_SUPABASE_ENDPOINT}${
        AUTH_ENDPOINTS.LOGIN_AZURE
      }`}
      className="flex mt-4 sm:mt-6 w-full items-center justify-center gap-3 rounded-3xl bg-white px-3 py-2 text-sm font-semibold text-mainBlack shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent"
    >
      <AzureIcon />
      <span className="text-sm font-semibold leading-6">Azure</span>
    </a>
  )
}

export default AzureRedirectButton
