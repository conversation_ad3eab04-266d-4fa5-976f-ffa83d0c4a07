import { useForm } from 'react-hook-form'
import {
  InvitationSignup,
  RegisterParams,
  RequestAccessParams,
  UserCredential
} from '../../../models/auth'
import {
  REGEX_EMAIL,
  REGEX_PASSWORD,
  REGEX_PREVENT_ONLY_WHITESPACES
} from '../../../enum/Regex'
import {
  DEFAULT_PAGE_SIZE,
  ENV,
  capitalizeFirstLetter,
  getErrorMessage,
  trimText,
  COOKIE_EXPIRE_TIME
} from '../../../utils'
import PrimaryButton from '../../../components/PrimaryButtons'
import { requestAccess, supabaseLogin, supabaseSignup } from '../../../api/Auth'
import { useEffect, useRef, useState } from 'react'
import { ROUTE_PATH } from '../../../enum/RoutePath'
import TextInputComponent from '../../../components/TextInputComponent'
import FormInputContainer from '../../../components/FormInputComponent'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { LoaderIcon, toast } from 'react-hot-toast'
import { getUnauthorizedGroupList } from '../../../api/Groups'
import { IGroupQuery, UnauthorizedListGroup } from '../../../models/groups'
import { Select } from 'antd'
import SearchIcon from '../../../assets/svgs/SearchIcon'
import NoDataComponent from '../../../components/NoDataComponent'
import { KEY_STORAGE } from '../../../enum/KeyStorage'
import AzureRedirectButton from '../AzureRedirectButton'
import cookie from 'js-cookie'

const RegisterComponent = () => {
  const navigate = useNavigate()
  const [params] = useSearchParams() // if there's a params, it means the user access this page from an invitation email
  const {
    control,
    formState: { errors, isSubmitting },
    watch,
    handleSubmit,
    setError
  } = useForm<RegisterParams>({
    mode: 'onChange',
    defaultValues: {
      email: params.get('email') ?? '',
      name: params.get('name') ?? '',
      group: params.get('group') ?? undefined
    }
  })
  const password = watch('password')
  const [showPassword, setShowPassword] = useState<boolean>(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState<boolean>(false)
  const [groupPage, setGroupPage] = useState<number>(1)
  const [listGroup, setListGroup] = useState<UnauthorizedListGroup[]>([])
  const [isFetchingMoreGroup, setIsFetchingMoreGroup] = useState<boolean>(false)
  const [searchGroup, setSearchGroup] = useState<string>('')
  const haveMoreGroup = useRef<boolean>(true)

  const fetchMoreGroupList = (e: unknown) => {
    if (!e || typeof e !== 'object' || !('target' in e)) return
    //detect when scroll to the end of the div
    const target = e.target as HTMLElement
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setGroupPage((prev) => prev + 1)
    }
  }

  const handleLogin = async (
    email: string | undefined = '',
    password: string = ''
  ) => {
    try {
      const res: UserCredential = await supabaseLogin({
        email: email,
        password: password
      })
      cookie.set(KEY_STORAGE.ACCESS_TOKEN, res.access_token, {
        expires: new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
      })
      cookie.set(KEY_STORAGE.REFRESH_TOKEN, res.refresh_token, {
        expires: new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
      })
      localStorage.setItem(KEY_STORAGE.USER_ID, res.user.id)
      navigate(ROUTE_PATH.Home)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    }
  }

  //this function to signup when clicking on invitation link
  const onSignUpSubmit = async (value: RegisterParams) => {
    const data: InvitationSignup = {
      id: params.get('id') ?? '',
      password: value.password,
      name: value.name.trim()
    }
    try {
      await supabaseSignup(data)
      await handleLogin(params.get('email') ?? '', data.password)
      toast.success('Sign up successful!')
    } catch (error) {
      Object.keys(value).forEach((fieldValue) => {
        setError(fieldValue as keyof RegisterParams, {
          type: 'error',
          message: getErrorMessage(error)
        })
      })
    }
  }

  // this function is to request access
  const onRequestAccessSubmit = async (value: RegisterParams) => {
    const data: RequestAccessParams = {
      email: value.email.trim(),
      group_id: value.group?.trim() ?? '',
      name: value.name.trim(),
      password: value.password
    }
    try {
      await requestAccess(data)
      toast.success(
        'Your request has been sent! Please wait for the admin to approve!'
      )
      navigate(ROUTE_PATH.Login)
    } catch (error) {
      navigate(ROUTE_PATH.Login, {
        replace: true,
        state: {
          email: value.email
        }
      })
      const errorMsg = getErrorMessage(error)
      toast.error(
        errorMsg.includes('user already exists')
          ? `An account with this email address already exists. Please log in or reset your password.`
          : capitalizeFirstLetter(errorMsg)
      )
    }
  }

  const onSubmit = async (value: RegisterParams) =>
    params.get('id')
      ? await onSignUpSubmit(value)
      : await onRequestAccessSubmit(value)
  // if there's a params, it means the user access this page from an invitation email

  const fetchListGroup = async () => {
    setIsFetchingMoreGroup(true)
    const data: IGroupQuery = {
      'page-no': groupPage,
      'page-size': DEFAULT_PAGE_SIZE,
      name: ''
    }
    try {
      const res = await getUnauthorizedGroupList(data)
      const totalPages = Math.floor(res.total / DEFAULT_PAGE_SIZE)
      setListGroup((prev) =>
        groupPage === 1 ? res.data : prev.concat(res.data)
      )
      if (groupPage > Math.floor(totalPages)) {
        // if the current page is the last page
        haveMoreGroup.current = false
      }
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    } finally {
      setIsFetchingMoreGroup(false)
    }
  }

  useEffect(() => {
    if (!params.get('id') && haveMoreGroup.current) {
      fetchListGroup()
    } // initial fetching function
  }, [groupPage])

  return (
    <>
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="text-center text-lg sm:text-2xl font-bold leading-9 tracking-tight text-mainBlack">
          {params.get('name') ? 'Sign up' : 'Request Access'}
        </h2>
      </div>

      <div className="mt-4 sm:mx-auto sm:w-full sm:max-w-[700px]">
        <div className="bg-white max-h-[70vh] overflow-auto xl:overflow-hidden xl:max-h-full p-6 shadow sm:rounded-lg sm:px-12">
          <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-3">
            <div className="flex flex-col sm:flex-row gap-4">
              <FormInputContainer<RegisterParams>
                control={control}
                label={'Name'}
                name={'name'}
                required
                vertialAlign
                errors={errors}
                rules={{
                  required: 'Required',
                  pattern: {
                    value: REGEX_PREVENT_ONLY_WHITESPACES,
                    message: 'Username cannot begin with a space'
                  }
                }}
                render={({ field }) => (
                  <TextInputComponent
                    value={trimText(field.value)}
                    placeholder="Enter your name"
                    disabled={Boolean(params.get('name'))}
                    onChange={field.onChange}
                  />
                )}
              />
              <FormInputContainer<RegisterParams>
                control={control}
                label={'Email'}
                name={'email'}
                required
                vertialAlign
                errors={errors}
                rules={{
                  required: 'Required',
                  pattern: {
                    value: REGEX_EMAIL,
                    message: 'Invalid email address'
                  }
                }}
                render={({ field }) => (
                  <TextInputComponent
                    value={trimText(field.value)}
                    placeholder="Enter your email"
                    disabled={Boolean(params.get('id'))}
                    onChange={field.onChange}
                  />
                )}
              />
            </div>

            <FormInputContainer<RegisterParams>
              endfixIcon={
                !showPassword ? (
                  <EyeSlashIcon
                    onClick={() => setShowPassword((prev) => !prev)}
                    cursor={'pointer'}
                    height={20}
                    width={20}
                  />
                ) : (
                  <EyeIcon
                    onClick={() => setShowPassword((prev) => !prev)}
                    cursor={'pointer'}
                    height={20}
                    width={20}
                  />
                )
              }
              control={control}
              label={'Password'}
              name={'password'}
              required
              vertialAlign
              rules={{
                required: 'Required',
                pattern: {
                  value: REGEX_PASSWORD,
                  message:
                    ' Must be at least 8 characters, maximum of 16 characters, contain atleast 1 lowercase, 1 uppercase, 1 number and 1 special character'
                }
              }}
              render={({ field }) => (
                <TextInputComponent
                  autoFocus={Boolean(params.get('id'))}
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                  {...field}
                />
              )}
              errors={errors}
            />

            <FormInputContainer<RegisterParams>
              endfixIcon={
                !showConfirmPassword ? (
                  <EyeIcon
                    onClick={() => setShowConfirmPassword((prev) => !prev)}
                    cursor={'pointer'}
                    height={20}
                    width={20}
                  />
                ) : (
                  <EyeSlashIcon
                    onClick={() => setShowConfirmPassword((prev) => !prev)}
                    cursor={'pointer'}
                    height={20}
                    width={20}
                  />
                )
              }
              control={control}
              vertialAlign
              required
              label={'Confirm password'}
              name={'confirmPassword'}
              errors={errors}
              rules={{
                required: 'Required',
                validate: (value) =>
                  value === password || 'Passwords do not match'
              }}
              render={({ field }) => (
                <TextInputComponent
                  placeholder="Confirm your password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  {...field}
                />
              )}
            />
            <FormInputContainer<RegisterParams>
              control={control}
              label={'Group'}
              name={'group'}
              required
              vertialAlign
              errors={errors}
              rules={{
                required: 'Required'
              }}
              render={({ field }) =>
                params.get('id') ? (
                  <TextInputComponent
                    disabled
                    placeholder="Enter your name"
                    {...field}
                  />
                ) : (
                  <Select
                    {...field}
                    className="w-full min-h-[36px]"
                    onPopupScroll={(e) => fetchMoreGroupList(e)}
                    options={listGroup
                      .filter((group) =>
                        group.name
                          .toLocaleLowerCase()
                          .includes(searchGroup.toLocaleLowerCase())
                      )
                      .map((group) => {
                        return {
                          label: group.name,
                          value: group.id
                        }
                      })}
                    notFoundContent={
                      isFetchingMoreGroup ? <LoaderIcon /> : <NoDataComponent />
                    }
                    placeholder="Select a group"
                    onChange={field.onChange}
                    dropdownRender={(menu) => (
                      <div className="w-full flex flex-col gap-2 px-3 py-4">
                        <TextInputComponent
                          prefixIcon={<SearchIcon />}
                          onChange={(e) => setSearchGroup(e.target.value)}
                          placeholder="Search groups"
                        />
                        {menu}
                        {isFetchingMoreGroup && (
                          <LoaderIcon className="mx-auto min-h-5 min-w-5" />
                        )}
                      </div>
                    )}
                  />
                )
              }
            />

            <PrimaryButton
              isLoading={isSubmitting}
              isDisabled={isSubmitting}
              type="submit"
            >
              {params.get('id') ? 'Sign up' : 'Request'}
            </PrimaryButton>
          </form>

          <div>
            <div className="relative mt-4 sm:mt-6">
              <div
                aria-hidden="true"
                className="absolute inset-0 flex items-center"
              >
                <div className="w-full border-t border-b-0 border-x-0 border-solid border-gray-200" />
              </div>
              <div className="relative flex justify-center text-sm font-medium leading-6">
                <span className="bg-white px-6 text-mainBlack">
                  Or continue with
                </span>
              </div>
            </div>

            <AzureRedirectButton />
          </div>
        </div>

        <p className="mt-4 sm:mt-6 text-center text-sm text-gray-500">
          Already had an account?{' '}
          <a
            href={ROUTE_PATH.Login}
            className="font-semibold leading-6 text-primary hover:text-primary"
          >
            Login
          </a>
        </p>
      </div>
    </>
  )
}

export default RegisterComponent
