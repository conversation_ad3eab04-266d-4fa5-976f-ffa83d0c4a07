import { Submit<PERSON>and<PERSON>, useForm } from 'react-hook-form'
import { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { supabaseLogin } from '../../../api/Auth'
import PrimaryButton from '../../../components/PrimaryButtons'
import { KEY_STORAGE } from '../../../enum/KeyStorage'
import { MESSAGE_CONTENT } from '../../../enum/Notification'
import { REGEX_EMAIL } from '../../../enum/Regex'
import { ROUTE_PATH } from '../../../enum/RoutePath'
import { EmailPasswordAuthenication } from '../../../interfaces'
import { UserCredential } from '../../../models/auth'
import TextInputComponent from '../../../components/TextInputComponent'
import FormInputContainer from '../../../components/FormInputComponent'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import AzureRedirectButton from '../AzureRedirectButton'
import { AxiosError } from 'axios'
import cookie from 'js-cookie'
import { COOKIE_EXPIRE_TIME } from '../../../utils'
const LoginPage = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const [saveSession, setSaveSession] = useState<boolean>(false)
  const [showPassword, setShowPassword] = useState<boolean>(false)
  const {
    formState: { errors, isSubmitting },
    handleSubmit,
    setError,
    control
  } = useForm<EmailPasswordAuthenication>({
    mode: 'onChange',
    defaultValues: {
      email: location.state?.email
    }
  })

  const handleLogin: SubmitHandler<EmailPasswordAuthenication> = async (
    data
  ) => {
    try {
      const res: UserCredential = await supabaseLogin(data)
      cookie.set(KEY_STORAGE.ACCESS_TOKEN, res.access_token, {
        expires: saveSession
          ? new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
          : undefined
      })
      cookie.set(KEY_STORAGE.REFRESH_TOKEN, res.refresh_token, {
        expires: saveSession
          ? new Date(new Date().getTime() + COOKIE_EXPIRE_TIME)
          : undefined
      })
      localStorage.setItem(KEY_STORAGE.USER_ID, res.user.id)
      navigate(ROUTE_PATH.Home)
    } catch (error: unknown) {
      if (!(error instanceof AxiosError)) {
        setError('password', {
          type: 'custom',
          message: MESSAGE_CONTENT.DEFAULT_ERROR
        })
        return
      }

      setError('email', {
        type: 'custom',
        message:
          error?.response?.data?.error_description ??
          MESSAGE_CONTENT.DEFAULT_ERROR
      })
      setError('password', {
        type: 'custom',
        message:
          error?.response?.data?.error_description ??
          MESSAGE_CONTENT.DEFAULT_ERROR
      })
    }
  }

  return (
    <>
      <div className="sm:mx-auto h-full sm:w-full sm:max-w-md">
        <h2 className="mt-0 sm:mt-2 text-center text-lg sm:text-2xl font-bold leading-9 tracking-tight text-mainBlack">
          Sign in to your account
        </h2>
      </div>

      <div className="mt-4 sm:mx-auto sm:w-full sm:max-w-[480px]">
        <div className="bg-white overflow-auto max-h-[70vh] p-6 sm:py-12 shadow sm:rounded-lg sm:px-12">
          <form
            className="w-full space-y-3 sm:space-y-6"
            onSubmit={handleSubmit(handleLogin)}
          >
            <FormInputContainer<EmailPasswordAuthenication>
              control={control}
              label={'Email'}
              name={'email'}
              required
              vertialAlign
              errors={errors}
              rules={{
                required: 'Required',
                pattern: {
                  value: REGEX_EMAIL,
                  message: 'Invalid email address'
                }
              }}
              render={({ field }) => (
                <TextInputComponent
                  type="email"
                  placeholder="Enter your email"
                  {...field}
                />
              )}
            />

            <FormInputContainer<EmailPasswordAuthenication>
              control={control}
              label={'Password'}
              name={'password'}
              required
              vertialAlign
              endfixIcon={
                !showPassword ? (
                  <EyeSlashIcon
                    onClick={() => setShowPassword((prev) => !prev)}
                    cursor={'pointer'}
                    height={20}
                    width={20}
                  />
                ) : (
                  <EyeIcon
                    onClick={() => setShowPassword((prev) => !prev)}
                    cursor={'pointer'}
                    height={20}
                    width={20}
                  />
                )
              }
              errors={errors}
              rules={{
                required: 'Required'
              }}
              render={({ field }) => (
                <TextInputComponent
                  placeholder="Enter your password"
                  type={showPassword ? 'text' : 'password'}
                  {...field}
                />
              )}
            />

            <div className="flex items-center flex-wrap justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  onChange={(e) => setSaveSession(e.target.checked)}
                  type="checkbox"
                  className="h-4 w-4 rounded border border-solid border-gray-300 text-primary focus:ring-primary"
                />
                <label
                  htmlFor="remember-me"
                  className="ml-2 block text-xs sm:text-sm leading-6 text-mainBlack"
                >
                  Remember me
                </label>
              </div>

              <div className="text-sm leading-6">
                <a
                  href={ROUTE_PATH.Forget_Password}
                  className="font-semibold text-xs sm:text-sm text-primary hover:text-primary hover:underline"
                >
                  Forgot password?
                </a>
              </div>
            </div>
            <PrimaryButton isDisabled={isSubmitting} type="submit">
              Sign in
            </PrimaryButton>
          </form>

          <div className="relative mt-4 sm:mt-10">
            <div
              aria-hidden="true"
              className="absolute inset-0 flex items-center"
            >
              <div className="w-full border-t border-b-0 border-x-0 border-solid border-gray-200" />
            </div>
            <div className="relative flex justify-center text-sm font-medium leading-6">
              <span className="bg-white px-6 text-mainBlack">
                Or continue with
              </span>
            </div>
          </div>

          <AzureRedirectButton />
        </div>

        <p className="mt-2 sm:mt-6 md:mt-10 text-center text-sm text-gray-500">
          Not a member?{' '}
          <a
            href={ROUTE_PATH.Register}
            className="font-semibold leading-6 text-primary hover:text-primary"
          >
            Request Access
          </a>
        </p>
      </div>
    </>
  )
}

export default LoginPage
