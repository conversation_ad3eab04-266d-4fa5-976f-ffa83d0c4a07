import { Tooltip } from 'antd'
import { Analytics_Streams } from '../../../models/analytics'
import { classNames, getErrorMessage } from '../../../utils'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import TagComponent from '../../../components/Tags'
import { EyeIcon } from '@heroicons/react/24/outline'
import { ROUTE_PATH } from '../../../enum/RoutePath'
import VideoIcon from '../../../assets/svgs/VideoIcon'
import ViewFocusIcon from '../../../assets/svgs/ViewFocus'
import { getProcessedStream } from '../../../api/Analytics'
import { useRef, useState } from 'react'
import toast, { LoaderIcon } from 'react-hot-toast'
import CustomModal from '../../../components/Modal'
import HlsPlayer from '../../../components/HLSPlayer'
import { ANALYTIC_STREAM_STATUS } from '../../../enum/AnalyticStreamStatus'
import RecordingRedirectLink from './recording-redirect-link'

type Props = {
  va_item: Analytics_Streams
}

const StreamItemComponent = ({ va_item }: Props) => {
  const { analytic_id } = useParams()
  const [params] = useSearchParams()
  const fileName = params.get('fileName')
  const navigate = useNavigate()
  const streamType = useRef<'raw' | 'processed'>('raw')
  const selectedStreamURL = useRef<Analytics_Streams | null>(null)
  const [openVAStreamModal, setOpenVAStreamModal] = useState(false)
  const [gettingProcessedStream, setGettingProcessedStream] = useState(false)
  const onViewAnalyticResults = (analytic_item: Analytics_Streams) => {
    navigate(
      `${ROUTE_PATH.VideoAnalytics}/${analytic_id}/${
        analytic_item.id
      }/${analytic_item.name.replace('/', '_')}`
    )
  }

  const onViewVARawStream = (analytic_item: Analytics_Streams) => {
    streamType.current = 'raw'
    selectedStreamURL.current = analytic_item
    setOpenVAStreamModal(true)
  }

  const onViewProcessedStream = async (analytic_item: Analytics_Streams) => {
    if (analytic_item.status !== ANALYTIC_STREAM_STATUS.PROCESSING) {
      toast.error(
        <RecordingRedirectLink
          recordingName={analytic_item.name}
          navigate={navigate}
        />
      )
      return
    }
    try {
      setGettingProcessedStream(true)
      const res = await getProcessedStream(analytic_item.id)
      streamType.current = 'processed'
      selectedStreamURL.current = {
        ...analytic_item,
        source: res.data.sink_url
      }
      setOpenVAStreamModal(true)
    } catch (error) {
      toast.error(getErrorMessage(error))
    } finally {
      setGettingProcessedStream(false)
    }
  }
  const renderStatusColor = (status: ANALYTIC_STREAM_STATUS) => {
    switch (status) {
      case ANALYTIC_STREAM_STATUS.READY:
        return '#dbe9fd'
      case ANALYTIC_STREAM_STATUS.PENDING:
        return '#f2f4f6'
      case ANALYTIC_STREAM_STATUS.PROCESSED:
        return '#dbfbe7'
      case ANALYTIC_STREAM_STATUS.PROCESSING:
        return '#ffead3'
      default:
        return '#f2f4f6'
    }
  }
  const renderStatusTextColor = (status: ANALYTIC_STREAM_STATUS) => {
    switch (status) {
      case ANALYTIC_STREAM_STATUS.READY:
        return '#4865c1'
      case ANALYTIC_STREAM_STATUS.PENDING:
        return '#2a3140'
      case ANALYTIC_STREAM_STATUS.PROCESSED:
        return '#056047'
      case ANALYTIC_STREAM_STATUS.PROCESSING:
        return '#ff8f00'
      default:
        return '#2a3140'
    }
  }

  return (
    <>
      <tr
        key={va_item.name}
        className={classNames(
          'even:bg-gray-50',
          fileName === va_item.name && '!bg-purple-100'
        )}
      >
        <td className="whitespace-nowrap px-4 text-left py-4 min-w-[150px] max-w-[20vw] truncate text-sm font-medium text-mainBlack">
          <Tooltip
            title={va_item.name}
            mouseEnterDelay={0.5}
            className="text-mainBlack truncate"
          >
            {va_item.name}
          </Tooltip>
        </td>
        <td className="px-4 py-4 min-w-[150px] max-w-[20vw] text-sm font-medium text-left text-mainBlack">
          <Tooltip
            title={va_item.source}
            mouseEnterDelay={0.5}
            className="text-mainBlack truncate"
          >
            <span className="truncate max-w-full block overflow-hidden text-ellipsis whitespace-nowrap">
              {va_item.source}
            </span>
          </Tooltip>
        </td>
        <td className="whitespace-nowrap capitalize px-3 py-4 text-center text-sm text-mainBlack">
          {va_item.type.toLocaleLowerCase()}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-center text-sm flex justify-center">
          <TagComponent
            backgroundColor={renderStatusColor(va_item.status)}
            className="capitalize font-medium"
            fontSize="13px"
            textColor={renderStatusTextColor(va_item.status)}
            text={va_item.status.toLocaleLowerCase()}
          />
        </td>
        <td className="whitespace-nowrap items-center px-3 py-4 text-center text-sm ">
          <div className="flex w-full gap-2 items-center">
            <Tooltip
              title={'View events'}
              mouseEnterDelay={0.5}
              className="flex w-full capitalize justify-center"
            >
              <EyeIcon
                onClick={() => {
                  onViewAnalyticResults(va_item)
                }}
                className="min-w-6 min-h-6 max-w-6 max-h-6"
                cursor={'pointer'}
                height={20}
                width={20}
              />
            </Tooltip>
            <Tooltip title={'View stream'} mouseEnterDelay={0.5}>
              <VideoIcon
                cursor={'pointer'}
                height={20}
                width={20}
                className="min-w-6 min-h-6 max-w-6 max-h-6"
                onClick={() => onViewVARawStream(va_item)}
              />
            </Tooltip>
            {gettingProcessedStream ? (
              <LoaderIcon className="min-w-2 min-h-2 max-w-2 max-h-2 p-2" />
            ) : (
              <Tooltip
                title={
                  va_item.status === ANALYTIC_STREAM_STATUS.PROCESSING ? (
                    'View detection overlay'
                  ) : (
                    <RecordingRedirectLink
                      recordingName={va_item.name}
                      navigate={navigate}
                    />
                  )
                }
                overlayInnerStyle={{
                  color: 'black',
                  backgroundColor: 'white'
                }}
                mouseEnterDelay={0.5}
                className={classNames(
                  'flex w-full capitalize justify-center p-2 rounded-md !group',
                  va_item.status === ANALYTIC_STREAM_STATUS.PROCESSING
                    ? 'bg-red-100 hover:bg-red-200'
                    : 'bg-gray-100'
                )}
                color="white"
              >
                <ViewFocusIcon
                  cursor={'pointer'}
                  className={classNames(
                    'min-w-9 min-h-9 max-w-9 max-h-9',
                    va_item.status === ANALYTIC_STREAM_STATUS.PROCESSING
                      ? ' stroke-red-700'
                      : 'stroke-gray-400'
                  )}
                  onClick={() => onViewProcessedStream(va_item)}
                />
              </Tooltip>
            )}
          </div>
        </td>
      </tr>
      <CustomModal
        openState={[openVAStreamModal, setOpenVAStreamModal]}
        title={`${
          streamType.current === 'raw'
            ? `Raw stream: `
            : 'View detection overlay: '
        } ${selectedStreamURL.current?.name}`}
      >
        <HlsPlayer
          className="mb-4"
          src={selectedStreamURL.current?.source ?? ''}
        />
      </CustomModal>
    </>
  )
}

export default StreamItemComponent
