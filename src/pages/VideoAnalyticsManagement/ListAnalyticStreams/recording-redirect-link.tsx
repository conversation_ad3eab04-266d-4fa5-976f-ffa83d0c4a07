import { NavigateFunction } from 'react-router-dom'
import { ROUTE_PATH } from '../../../enum/RoutePath'

type Props = {
  navigate: NavigateFunction
  recordingName: string
}

const RecordingRedirectLink = ({ navigate, recordingName }: Props) => {
  const redirectToRecording = async () => {
    navigate(
      `${ROUTE_PATH.Recording}?${new URLSearchParams({
        search: recordingName
      })}`
    )
  }

  return (
    <span className="whitespace-pre-line ">
      Stream is no longer live. To access past streams,{' '}
      <p
        className="text-blue-600 underline hover:cursor-pointer"
        onClick={() => redirectToRecording()}
      >
        go to the recording page.
      </p>
    </span>
  )
}

export default RecordingRedirectLink
