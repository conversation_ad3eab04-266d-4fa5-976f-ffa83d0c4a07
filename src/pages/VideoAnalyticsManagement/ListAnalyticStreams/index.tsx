import { Analytics_Streams } from '../../../models/analytics'
import { Skeleton } from 'antd'
import NoDataComponent from '../../../components/NoDataComponent'
import StreamItemComponent from './stream-item-component'

type Props = {
  listStreams: Analytics_Streams[] | null
  loading: boolean
}

const ListAnalyticStreamsComponent = ({ listStreams, loading }: Props) => {
  return (
    <div className="min-h-[400px] w-full h-full flex-1 overflow-auto">
      <div className="-my-2 overflow-x-auto w-full h-full">
        <div className="min-w-full py-2 align-middle">
          <table className="min-w-full relative table w-full divide-gray-300">
            <thead className="sticky top-2 w-full bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="py-3.5 px-4 text-left text-sm font-semibold text-mainBlack"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="py-3.5 px-4 text-left text-sm font-semibold text-mainBlack"
                >
                  Source
                </th>
                <th
                  scope="col"
                  className="px-4 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="px-3 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-3 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white w-full overflow-auto">
              {loading ? (
                <>
                  <tr>
                    <td colSpan={5}>
                      <Skeleton active />
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={5}>
                      <Skeleton active />
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={5}>
                      <Skeleton active />
                    </td>
                  </tr>
                </>
              ) : listStreams && listStreams?.length > 0 ? (
                listStreams?.map((va_item) => (
                  <StreamItemComponent key={va_item.id} va_item={va_item} />
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="pt-24">
                    <NoDataComponent />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default ListAnalyticStreamsComponent
