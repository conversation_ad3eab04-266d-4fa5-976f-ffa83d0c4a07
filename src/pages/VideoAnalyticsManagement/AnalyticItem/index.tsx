import moment from 'moment'
import { IAnalytics } from '../../../models/analytics'
import DropdownMenus from '../../../components/DropdownMenu'
import { EllipsisHorizontalIcon } from '@heroicons/react/20/solid'
import { useNavigate } from 'react-router-dom'
import { ROUTE_PATH } from '../../../enum/RoutePath'
import { Tooltip } from 'antd'
import { classNames } from '../../../utils'
import { Menu } from '../../../interfaces'

type Props = {
  analytic: IAnalytics
  triggerDeleteVA: (analytic: IAnalytics) => void
  triggerRevokeVA: (analytic: IAnalytics) => void
}

const AnalyticItemComponent = ({
  analytic,
  triggerDeleteVA,
  triggerRevokeVA
}: Props) => {
  const navigate = useNavigate()
  const optionMenus: Menu[] = [
    {
      text: 'View Details',
      id: '1',
      onClick: () => navigate(`${ROUTE_PATH.VideoAnalytics}/${analytic.id}`)
    },
    {
      text: 'Revoke plugin',
      id: '2',
      onClick: () => triggerRevokeVA(analytic),
      disabled: analytic.api_keys[0].revoked
    },
    {
      text: 'Delete',
      id: '3',
      onClick: () => triggerDeleteVA(analytic)
    }
  ]
  return (
    <div className="h-fit flex-1 lg:max-w-[320px] min-w-[400px] md:min-w-[250px] cursor-pointer rounded-xl border border-solid border-gray-200">
      <div className="flex justify-between items-center rounded-xl rounded-b-none gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
        <div className="flex truncate items-center gap-4">
          <span className="flex h-8 w-8 shrink-0 items-center justify-center rounded-lg border border-gray-700 bg-gray-800 text-sm font-medium text-white group-hover:text-white">
            {analytic.name.charAt(0).toUpperCase()}
          </span>
          <Tooltip
            title={analytic.name}
            mouseEnterDelay={0.5}
            className="text-sm truncate font-medium leading-6 text-mainBlack"
          >
            {analytic.name}
          </Tooltip>
        </div>
        <div>
          <DropdownMenus
            label={
              <EllipsisHorizontalIcon aria-hidden="true" className="h-5 w-5" />
            }
            popupDirection="left"
            className="ring-white shadow-white"
            menus={optionMenus}
          />
        </div>
      </div>
      <dl className="-my-4 divide-y divide-solid divide-x-0 divide-gray-100 truncate px-6 py-4 text-sm leading-6">
        <div className="flex flex-wrap sm:flex-nowrap justify-between gap-x-4 py-3">
          <dt className="text-gray-500">Created at: </dt>
          <dd className="text-gray-700">
            <time dateTime={analytic.created_at} className="text-gray-500">
              {moment(analytic.created_at).format('MMMM D, YYYY')}
            </time>
          </dd>
        </div>

        <div className="flex flex-wrap sm:flex-nowrap justify-between gap-x-4 py-3">
          <dt className="text-gray-500">Provider: </dt>
          <dd className="text-gray-700 truncate">
            <Tooltip
              title={analytic.provider}
              mouseEnterDelay={0.5}
              className="text-gray-500 text-sm truncate max-w-full block"
            >
              {analytic.provider}
            </Tooltip>
          </dd>
        </div>
        <div className="flex flex-wrap sm:flex-nowrap items-center justify-between gap-x-4 truncate py-3">
          <dt className="text-gray-500">Status: </dt>
          <dd className="flex items-center gap-x-2 truncate">
            <span
              className={classNames(
                'inline-flex sm:block capitalize items-center gap-x-1.5 rounded-full px-3 py-1 text-xs font-medium',
                analytic.api_keys[0]?.revoked === true
                  ? 'text-red-700 bg-red-100'
                  : 'text-green-700 bg-green-100'
              )}
            >
              {analytic.api_keys[0]?.revoked === true ? 'Disabled' : 'Enabled'}
            </span>
          </dd>
        </div>
      </dl>
    </div>
  )
}

export default AnalyticItemComponent
