import NavigateBackComponent from '../../../../components/NavigateBackComponent'
import DropdownOptions from '../../../../components/DropdownOptions'
import SecondaryButton from '../../../../components/SecondaryButtons'
import FilterIcon from '../../../../assets/svgs/FilterIcon'
import PrimaryButton from '../../../../components/PrimaryButtons'
import { IOption } from '../../../../interfaces'
import { useCallback, useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import { exportAnalyticResults } from '../../../../api/Analytics'
import { downloadFromAPI, getErrorMessage } from '../../../../utils'
import { useParams } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  fetchListVAThunk,
  selectAnalyticResultQuery,
  selectListVA,
  setQueryParams
} from '../../../../stores/Reducers/analyticsReducer'

type Props = {
  openFilterModal: () => void
}
const QUERY_SORT_OPTIONS: IOption[] = [
  { value: 'created_at-desc', label: 'Newest to oldest' },
  { value: 'created_at-asc', label: 'Oldest to latest' }
]

const AnalyticResultHeader = ({ openFilterModal }: Props) => {
  const { assignment_id, analytic_id, camera_name } = useParams()
  const [isExporting, setIsExporting] = useState<boolean>(false)
  const listVA = useAppSelector(selectListVA)
  const selectedVa = listVA.find((va) => va.id === analytic_id)
  const queryParams = useAppSelector(selectAnalyticResultQuery)
  const dispatch = useAppDispatch()

  const onSelectSortOption = (value: string) =>
    dispatch(
      setQueryParams({
        ...queryParams,
        'page-no': queryParams['page-no'] > 1 ? 1 : queryParams['page-no'],
        'order-by': value.split('-')[0],
        'order-direction': value.split('-')[1]
      })
    )

  const handleExportResults = useCallback(async () => {
    setIsExporting(true)
    const toastId = toast.loading('Preparing your file...')
    try {
      const res = await exportAnalyticResults(assignment_id, queryParams)
      downloadFromAPI(res, `${selectedVa?.name}-${camera_name}.zip`)
      toast.success('The download process has started!', { id: toastId })
    } catch (error) {
      toast.error(getErrorMessage(error), { id: toastId })
    } finally {
      setIsExporting(false)
    }
  }, [queryParams])

  useEffect(() => {
    if (listVA.length === 0) {
      dispatch(fetchListVAThunk())
    }
  }, [])
  return (
    <>
      <NavigateBackComponent />
      <div className="flex flex-wrap sm:flex-nowrap w-full gap-2 justify-between">
        <div className="flex flex-wrap gap-4 flex-1 items-center">
          <div className="flex-1 flex w-full sm:max-w-48">
            <DropdownOptions
              label={'Sort By'}
              options={QUERY_SORT_OPTIONS}
              onSelect={onSelectSortOption}
              selected={`${queryParams['order-by']}-${queryParams['order-direction']}`}
            />
          </div>
          <SecondaryButton
            className={
              'justify-between !rounded-3xl border-none flex-1 sm:max-w-[200px]'
            }
            backgroundColor="#f4f4f4"
            onClick={openFilterModal}
          >
            <div className="flex items-center gap-2">
              <FilterIcon />
              <p className="font-normal">Filters</p>
            </div>
          </SecondaryButton>
        </div>
        <PrimaryButton
          isDisabled={isExporting}
          isLoading={isExporting}
          onClick={handleExportResults}
          className="sm:max-w-52"
        >
          Export
        </PrimaryButton>
      </div>
    </>
  )
}

export default AnalyticResultHeader
