import { useParams } from 'react-router-dom'
import { getAnalyticsResult } from '../../../api/Analytics'
import { DEFAULT_PAGE_SIZE } from '../../../utils'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { IOption } from '../../../interfaces'
import CustomModal from '../../../components/Modal'
import AnalyticResultFilter from './AnalyticResultFilter'
import { IAnalyticsResults } from '../../../models/analytics'
import { Skeleton } from 'antd'
import PaginationComponent from '../../../components/Pagination'
import ListEventsComponent from './ListEventsComponent'
import AnalyticResultHeader from './AnalyticResultHeader'
import ActiveFiltersComponent from '../../../components/ActiveFiltersComponent'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  DEFAULT_QUERY_PARAMS,
  selectAnalyticResultQuery,
  setAnalyticEndDateQuery,
  setAnalyticQueryPage,
  setAnalyticStartDateQuery,
  setQueryParams
} from '../../../stores/Reducers/analyticsReducer'
import { AxiosError } from 'axios'
import moment from 'moment'
import dayjs from 'dayjs'

const AnalyticsResultsPage = () => {
  const { assignment_id } = useParams()
  const [openFilterModal, setOpenFilterModal] = useState<boolean>(false)
  const [listResults, setListResults] = useState<IAnalyticsResults[]>([])
  const [isFetchingResults, setIsFetchingResults] = useState<boolean>(true)
  const [totalResults, setTotalResults] = useState<number>(0)
  const queryParams = useAppSelector(selectAnalyticResultQuery)
  const dispatch = useAppDispatch()
  const IS_QUERY_MODIFIED = useMemo(
    () =>
      queryParams.label?.join(',').length > 0 ||
      queryParams.event_code.join(',').length > 0 ||
      queryParams.end_time !==
        dayjs(Date.now()).second(59).millisecond(0).toISOString() ||
      queryParams.start_time !==
        dayjs(Date.now())
          .subtract(30, 'day')
          .second(0)
          .millisecond(0)
          .toISOString(),
    [queryParams]
  )

  const listActiveFilters = useMemo(() => {
    return [
      queryParams.end_time !==
        dayjs(Date.now()).second(59).millisecond(0).toISOString() ||
      queryParams.start_time !==
        dayjs(Date.now())
          .subtract(30, 'day')
          .second(0)
          .millisecond(0)
          .toISOString()
        ? {
            label: `from: ${moment(queryParams?.start_time).format(
              'YYYY-MM-DD HH:mm'
            )} to:  ${moment(queryParams?.end_time).format(
              'YYYY-MM-DD HH:mm'
            )}`,
            value: 'time'
          }
        : {
            label: '',
            value: ''
          }
    ]
  }, [queryParams.end_time, queryParams.start_time])

  const abortController = useRef<AbortController>()
  const handleOpenFilterModal = () => setOpenFilterModal(true)

  const closeModal = () => setOpenFilterModal(false)

  const handleApplyFilters = (
    label: string[],
    event_code: string[],
    start_time?: string,
    end_time?: string
  ) => {
    dispatch(
      setQueryParams({
        ...queryParams,
        label: label,
        event_code: event_code,
        start_time: start_time,
        end_time: end_time
      })
    )
    closeModal()
  }

  const fetchListResults = async () => {
    setIsFetchingResults(true)
    abortController.current = new AbortController()
    try {
      const res = await getAnalyticsResult(
        assignment_id,
        queryParams,
        abortController.current
      )
      setListResults(res.data)
      setIsFetchingResults(false)
      setTotalResults(res.total)
    } catch (error) {
      if ((error as AxiosError).code === AxiosError.ERR_CANCELED) {
        return
      }
      setIsFetchingResults(false)
    }
  }

  const handleChangePage = (page: number) =>
    dispatch(setAnalyticQueryPage(page))

  const handleRemoveQueryParam = (item: IOption) => {
    if (item.value === 'time') {
      dispatch(
        setAnalyticStartDateQuery(
          dayjs(Date.now())
            .subtract(30, 'day')
            .second(0)
            .millisecond(0)
            .toISOString() ?? ''
        )
      )
      dispatch(
        setAnalyticEndDateQuery(
          dayjs(Date.now()).second(59).millisecond(0).toISOString() ?? ''
        )
      )
      return
    }
  }

  const handleClearAllQuery = () => {
    dispatch(
      setQueryParams({
        ...DEFAULT_QUERY_PARAMS,
        'page-no': queryParams['page-no'],
        start_time: dayjs(Date.now())
          .subtract(30, 'day')
          .second(0)
          .millisecond(0)
          .toISOString(),
        end_time: dayjs(Date.now()).second(59).millisecond(0).toISOString()
      })
    )
  }

  useEffect(() => {
    fetchListResults()
    return () => {
      abortController.current?.abort()
    }
  }, [queryParams])

  useEffect(() => {
    return () => {
      dispatch(
        setQueryParams({
          ...DEFAULT_QUERY_PARAMS,
          start_time: dayjs(Date.now())
            .subtract(30, 'day')
            .second(0)
            .millisecond(0)
            .toISOString(),
          end_time: dayjs(Date.now()).second(59).millisecond(0).toISOString()
        })
      )
      dispatch(setAnalyticQueryPage(1))
    }
  }, [])

  return (
    <div className="w-full flex-1 flex flex-col h-full gap-4">
      <AnalyticResultHeader openFilterModal={handleOpenFilterModal} />
      {IS_QUERY_MODIFIED && (
        <ActiveFiltersComponent
          listActiveFilters={listActiveFilters}
          clearAllFilter={handleClearAllQuery}
          onRemoveFilter={handleRemoveQueryParam}
        />
      )}
      {isFetchingResults ? (
        <>
          <Skeleton />
          <Skeleton />
          <Skeleton />
          <Skeleton />
        </>
      ) : (
        <ListEventsComponent
          listResults={listResults}
          totalResults={totalResults}
        />
      )}
      <PaginationComponent
        totalItems={totalResults}
        currentPage={queryParams['page-no']}
        onPageChange={handleChangePage}
        pageSize={DEFAULT_PAGE_SIZE}
      />
      <CustomModal
        className="sm:!min-w-[80vw] md:!min-w-[70vw]"
        openState={[openFilterModal, setOpenFilterModal]}
        title={'Filter Events'}
      >
        <AnalyticResultFilter
          defaultLabel={queryParams.label}
          defaultEventCode={queryParams.event_code}
          onApply={handleApplyFilters}
          closeModal={closeModal}
          defaultFromDate={queryParams.start_time}
          defaultToDate={queryParams.end_time}
        />
      </CustomModal>
    </div>
  )
}

export default memo(AnalyticsResultsPage)
