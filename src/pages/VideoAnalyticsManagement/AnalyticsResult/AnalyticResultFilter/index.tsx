import { useState } from 'react'
import PrimaryButton from '../../../../components/PrimaryButtons'
import SecondaryButton from '../../../../components/SecondaryButtons'
import { classNames } from '../../../../utils'
import { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import FilterContentByTypeComponent from '../FilterContent'
import { QueryEvents } from '../../../../models/analytics'

type Props = {
  closeModal: () => void
  onApply: (
    label: string[],
    event_code: string[],
    start_time?: string,
    end_time?: string
  ) => void
  defaultLabel: string[]
  defaultEventCode: string[]
  defaultFromDate?: string
  defaultToDate?: string
}
const FILTER_CATEGORIES = ['Query', 'Date']

const AnalyticResultFilter = ({
  closeModal,
  onApply,
  defaultEventCode = [''],
  defaultLabel = [''],
  defaultFromDate,
  defaultToDate
}: Props) => {
  const [selectedFilter, setSelectedFilter] = useState<string>('Query')
  const [textObjectValue, setTextObjectValue] = useState<QueryEvents[]>(
    defaultLabel.map((label, index) => ({
      index: index,
      value: label
    }))
  )
  const [textEventCodeValue, setTextEventCodeValue] = useState<QueryEvents[]>(
    defaultEventCode.map((label, index) => ({
      index: index,
      value: label
    }))
  )
  const [fromDate, setFromDate] = useState<Dayjs>(dayjs(defaultFromDate))
  const [toDate, setToDate] = useState<Dayjs>(dayjs(defaultToDate))
  const handleApplyFilters = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    onApply(
      textObjectValue.map((object) => object.value).flat(),
      textEventCodeValue.map((object) => object.value).flat(),
      fromDate.toISOString(),
      toDate.toISOString()
    )
  }

  const handleClearFilters = () => {
    setTextObjectValue([])
    setTextEventCodeValue([])
    setFromDate(dayjs(Date.now()).subtract(30, 'day'))
    setToDate(dayjs(Date.now()))
  }

  return (
    <form
      onSubmit={(e) => handleApplyFilters(e)}
      className="w-full h-full flex flex-col gap-2 divide-y divide-x-0 divide-solid divide-gray-200 pb-4"
    >
      <div className="flex flex-col sm:flex-row gap-4 sm:gap-8 lg:gap-24 w-full flex-1 h-full">
        <nav
          aria-label="Sidebar"
          className="flex sm:w-1/3 sm:max-w-[150px] w-[150px] flex-col"
        >
          <ul role="list" className="-mx-2 flex sm:block list-none">
            {FILTER_CATEGORIES.map((item) => (
              <li
                key={item}
                className="cursor-pointer"
                onClick={() => setSelectedFilter(item)}
              >
                <a
                  className={classNames(
                    item === selectedFilter
                      ? 'bg-[#F7F3FF] text-primary'
                      : 'text-gray-700 hover:bg-[#F7F3FF] hover:text-primary',
                    'group flex gap-x-3 rounded-md p-2 pl-3 text-sm/6 font-semibold'
                  )}
                >
                  {item}
                </a>
              </li>
            ))}
          </ul>
        </nav>
        <FilterContentByTypeComponent
          selectedFilter={selectedFilter}
          toDateState={[toDate, setToDate]}
          fromDateState={[fromDate, setFromDate]}
          onTextQueryChange={(e, option) =>
            option.value === 'label'
              ? setTextObjectValue(e)
              : setTextEventCodeValue(e)
          }
          textQueryValue={(option) =>
            option.value === 'label' ? textObjectValue : textEventCodeValue
          }
        />
      </div>
      <div className="flex w-full flex-wrap justify-between items-center gap-2">
        <p
          onClick={handleClearFilters}
          className="text-primary mx-auto font-medium text-sm hover:cursor-pointer hover:underline"
        >
          Clear all
        </p>
        <div className="flex gap-2 justify-end flex-1 pt-2">
          <SecondaryButton onClick={closeModal} className="sm:max-w-40">
            Cancel
          </SecondaryButton>
          <PrimaryButton type="submit" className="sm:max-w-40">
            Apply
          </PrimaryButton>
        </div>
      </div>
    </form>
  )
}

export default AnalyticResultFilter
