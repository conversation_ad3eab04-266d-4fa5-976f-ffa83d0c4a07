import NoDataComponent from '../../../../components/NoDataComponent'
import { IAnalyticsResults } from '../../../../models/analytics'
import {
  capitalizeFirstLetter,
  classNames,
  handleCopy
} from '../../../../utils'
import { Fragment, useCallback, useState } from 'react'
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/20/solid'
import TagComponent from '../../../../components/Tags'
import moment from 'moment'
import { ClipboardDocumentIcon } from '@heroicons/react/24/outline'
import { Tooltip } from 'antd'

type Props = {
  listResults: IAnalyticsResults[]
  totalResults: number
}

const ListEventsComponent = ({ listResults }: Props) => {
  const [resultsExtended, setResultsExtended] = useState<string[]>([])
  const handleToggleExtendResult = useCallback((assignment_id: string) => {
    setResultsExtended((prev) =>
      prev.includes(assignment_id)
        ? prev.filter((item) => item !== assignment_id)
        : [...prev, assignment_id]
    )
  }, [])

  const renderJsonText = useCallback(
    (data: IAnalyticsResults) => {
      return (
        <div className="w-full">
          <p>{'{'}</p>
          {Object.entries(data).map(([key, value]) => (
            <div className="pl-4" key={key}>
              <span>"{key}":</span>{' '}
              {key === 'image_blob' && typeof value === 'string' ? (
                <span>
                  {value.length > 0
                    ? `"${value.slice(0, 100)}..."` // Show only the first 100 characters
                    : `"${value}"`}{' '}
                  {value.length > 0 && (
                    <Tooltip title="Copy image blob" mouseEnterDelay={0.5}>
                      <ClipboardDocumentIcon
                        className="cursor-pointer"
                        height={20}
                        onClick={() => handleCopy(value)}
                        width={20}
                      />
                    </Tooltip>
                  )}
                </span>
              ) : (
                JSON.stringify(value, null, 2)
              )}
            </div>
          ))}
          <p>{'}'}</p>
        </div>
      )
    },
    [resultsExtended]
  )

  if (listResults.length === 0) {
    return (
      <div className="w-full flex-1 flex flex-col gap-3 overflow-auto pr-2">
        <NoDataComponent />
      </div>
    )
  }
  return (
    <div className="w-full flex-1 flex flex-col gap-3 overflow-auto">
      {listResults.map((result) => (
        <Fragment key={result.id}>
          <div
            className={classNames(
              'px-3 sm:px-6 flex cursor-pointer items-center gap-4 w-full rounded-xl border-[#E3E3E3] border border-solid',
              resultsExtended.includes(result.id) ? 'bg-[#F7F3FF]' : 'bg-white'
            )}
          >
            <div
              className="flex items-center truncate gap-2 flex-1 py-5"
              onClick={() => handleToggleExtendResult(result.id)}
            >
              {resultsExtended.includes(result.id) ? (
                <ChevronUpIcon className="min-w-5 min-h-5 w-5 h-5" />
              ) : (
                <ChevronDownIcon className="min-w-5 min-h-5 w-5 h-5" />
              )}
              <p className="font-semibold text-sm w-fit truncate">{`Event ID: ${
                result.id
              } (${moment(result.timestamp).format(
                'D MMM YYYY HH:mm:ss'
              )})`}</p>
            </div>
            <TagComponent
              text={capitalizeFirstLetter(
                result.event_code.toString().toLocaleLowerCase()
              )}
              textColor="black"
              backgroundColor="white"
              fontSize="12px"
              border="1px solid lightgray"
            />
          </div>
          <code
            className={`bg-gray-50 flex justify-between whitespace-pre-wrap transition-all duration-200 ${
              resultsExtended.includes(result.id)
                ? 'translate-y-0 p-4'
                : 'max-h-0 opacity-0'
            }`}
          >
            {resultsExtended.includes(result.id) && (
              <>{renderJsonText(result)}</>
            )}
          </code>
        </Fragment>
      ))}
    </div>
  )
}

export default ListEventsComponent
