import { DatePicker } from 'antd'
import { disabledFromDateTime, disabledToDateTime } from '../../../../utils'

import dayjs, { Dayjs } from 'dayjs'

type Props = {
  toDateState: [Dayjs, React.Dispatch<React.SetStateAction<Dayjs>>]
  fromDateState: [Dayjs, React.Dispatch<React.SetStateAction<Dayjs>>]
}

const DateFilterComponent = ({ fromDateState, toDateState }: Props) => {
  const [fromDate, setFromDate] = fromDateState
  const [toDate, setToDate] = toDateState
  return (
    <>
      <div className="my-2">
        <p className="font-semibold mb-1 text-sm">Start Date</p>
        <div className="flex-1">
          <DatePicker
            format={{
              format: 'YYYY-MM-DD HH:mm',
              type: 'mask'
            }}
            needConfirm={false}
            disabledTime={(date) => disabledFromDateTime(date, toDate)}
            placeholder="YYYY-MM-DD HH:mm"
            maxDate={toDate || dayjs(new Date())}
            className="w-full min-h-[40px] text-xs"
            showTime
            allowClear={false}
            inputReadOnly // prevent tablet's vitual keyboard from popping up
            value={fromDate}
            onChange={setFromDate}
          />
        </div>
      </div>
      <div className="my-2">
        <p className="font-semibold mb-1 text-sm">End Date</p>
        <div className="flex-1">
          <DatePicker
            format={{
              format: 'YYYY-MM-DD HH:mm',
              type: 'mask'
            }}
            needConfirm={false}
            placeholder="YYYY-MM-DD HH:mm"
            disabledTime={(date) => disabledToDateTime(date, fromDate)}
            minDate={fromDate}
            allowClear={false}
            className="w-full min-h-[40px]"
            inputReadOnly // prevent tablet's vitual keyboard from popping up
            showTime
            value={toDate}
            onChange={setToDate}
          />
        </div>
      </div>
    </>
  )
}

export default DateFilterComponent
