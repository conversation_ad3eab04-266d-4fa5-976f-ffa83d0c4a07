import { IOption } from '../../../../interfaces'
import TextInputComponent from '../../../../components/TextInputComponent'
import { QueryEvents } from '../../../../models/analytics'
import { XMarkIcon } from '@heroicons/react/24/outline'
import SecondaryButton from '../../../../components/SecondaryButtons'
import { PlusIcon } from '@heroicons/react/20/solid'
import DropdownMenus from '../../../../components/DropdownMenu'

type Props = {
  textQueryValue: (option: IOption) => QueryEvents[]
  onTextQueryChange: (e: QueryEvents[], option: IOption) => void
}
const QUERY_OPTIONS: IOption[] = [
  { label: 'Object', value: 'label' },
  { label: 'Event code', value: 'event_code' }
]
const QueryFilterComponent = ({ onTextQueryChange, textQueryValue }: Props) => {
  const onTextInputChange = (query: QueryEvents, option: IOption) => {
    const queryList = textQueryValue(option)
    const itemIndex = queryList.findIndex((item) => item.index === query.index)
    onTextQueryChange(
      textQueryValue(option).map((item, index) =>
        itemIndex === index
          ? {
              ...item,
              value: query.value
            }
          : item
      ),
      option
    )
  }
  const onAddQueryItem = (option: IOption, value?: string) => {
    const valueList = textQueryValue(option)
    if (valueList.some((item) => item.value === value)) return
    onTextQueryChange(
      [...valueList, { index: valueList.length, value: value ?? '' }],
      option
    )
  }

  const onRemoveQueryItem = (index: number, option: IOption) => {
    const updatedList = textQueryValue(option).filter(
      (item) => item.index !== index
    )
    onTextQueryChange(updatedList, option)
  }

  const renderQueryMenuOptions = (option: IOption) => {
    const customOption = [
      {
        text: (
          <SecondaryButton className="mr-auto !gap-1 !px-3">
            <PlusIcon height={16} width={16} /> Add custom option
          </SecondaryButton>
        ),
        id: 'add',
        onClick: () => {
          onAddQueryItem(option, '')
        }
      }
    ]
    switch (option.value) {
      case 'label':
        return [
          ...customOption,
          {
            text: 'person',
            id: 'person',
            onClick: () => {
              onAddQueryItem(option, 'person')
            }
          },
          {
            text: 'face',
            id: 'face',
            onClick: () => {
              onAddQueryItem(option, 'face')
            }
          },
          {
            text: 'car',
            id: 'car',
            onClick: () => {
              onAddQueryItem(option, 'car')
            }
          }
        ]
      case 'event_code':
        return [
          ...customOption,
          {
            text: 'ready',
            id: 'ready',
            onClick: () => {
              onAddQueryItem(option, 'ready')
            }
          },
          {
            text: 'completed',
            id: 'completed',
            onClick: () => {
              onAddQueryItem(option, 'completed')
            }
          },
          {
            text: 'start',
            id: 'start',
            onClick: () => {
              onAddQueryItem(option, 'start')
            }
          },
          {
            text: 'stop',
            id: 'stop',
            onClick: () => {
              onAddQueryItem(option, 'stop')
            }
          },
          {
            text: 'detection',
            id: 'detection',
            onClick: () => {
              onAddQueryItem(option, 'detection')
            }
          },
          {
            text: 'classification',
            id: 'classification',
            onClick: () => {
              onAddQueryItem(option, 'classification')
            }
          },
          {
            text: 'segmentation',
            id: 'segmentation',
            onClick: () => {
              onAddQueryItem(option, 'segmentation')
            }
          }
        ]
      default:
        return []
    }
  }
  return (
    <div className="flex-1 flex flex-col gap-4 py-1">
      {QUERY_OPTIONS.map((option) => (
        <div
          key={option.value}
          className="flex-1 flex items-center flex-wrap gap-3 h-fit"
        >
          <p className="text-sm font-semibold text-gray-600">Where</p>
          <div className="flex-1">
            <TextInputComponent
              value={option.label}
              disabled
              customClassName="text-center"
            />
          </div>
          <p className="text-sm font-semibold text-gray-600">Contains</p>
          <div className="flex flex-wrap gap-2 flex-1 w-full">
            {textQueryValue(option).map((query) => (
              <div key={query.index}>
                <TextInputComponent
                  endfixIcon={
                    <XMarkIcon
                      onClick={() => onRemoveQueryItem(query.index, option)}
                      cursor={'pointer'}
                      height={16}
                      width={16}
                    />
                  }
                  value={query.value}
                  onChange={(e) =>
                    onTextInputChange(
                      {
                        index: query.index,
                        value: e.target.value.toLocaleLowerCase()
                      },
                      option
                    )
                  }
                  autoFocus
                  className="w-[100px] pr-8 text-sm font-medium text-gray-600 h-9 border border-solid border-[#E3E3E3] rounded-2xl"
                />
              </div>
            ))}
            <div className="flex items-center gap-2">
              {option.value === 'label' ? (
                <SecondaryButton
                  onClick={() => onAddQueryItem(option, '')}
                  className="mr-auto !gap-1 !px-3 hover:!bg-gray-50 hover:opacity-100"
                >
                  <PlusIcon height={16} width={16} /> Add
                </SecondaryButton>
              ) : (
                <DropdownMenus
                  label={<p className="font-semibold text-gray-500">Select</p>}
                  className="max-w-fit"
                  menus={renderQueryMenuOptions(option)}
                />
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default QueryFilterComponent
