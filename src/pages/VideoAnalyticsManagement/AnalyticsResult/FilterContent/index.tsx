import React from 'react'
import { IOption } from '../../../../interfaces'
import { Dayjs } from 'dayjs'
import { QueryEvents } from '../../../../models/analytics'
import QueryFilterComponent from './query-filter-component'
import DateFilterComponent from './date-filter.component'

type Props = {
  selectedFilter: string
  toDateState: [Dayjs, React.Dispatch<React.SetStateAction<Dayjs>>]
  fromDateState: [Dayjs, React.Dispatch<React.SetStateAction<Dayjs>>]
  onTextQueryChange: (e: QueryEvents[], option: IOption) => void
  textQueryValue: (option: IOption) => QueryEvents[]
}

const FilterContentByTypeComponent = React.memo(
  ({
    selectedFilter,
    fromDateState,
    toDateState,
    textQueryValue,
    onTextQueryChange
  }: Props) => {
    switch (selectedFilter) {
      case 'Query':
        return (
          <QueryFilterComponent
            onTextQueryChange={onTextQueryChange}
            textQueryValue={textQueryValue}
          />
        )

      case 'Date':
        return (
          <DateFilterComponent
            fromDateState={fromDateState}
            toDateState={toDateState}
          />
        )
      default:
        return <></>
    }
  }
)

export default FilterContentByTypeComponent
