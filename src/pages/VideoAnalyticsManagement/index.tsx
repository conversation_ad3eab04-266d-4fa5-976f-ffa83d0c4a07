import { useEffect, useRef, useState } from 'react'
import PrimaryButton from '../../components/PrimaryButtons'
import { IAnalytics } from '../../models/analytics'
import CustomModal from '../../components/Modal'
import CreateVAModal from './CreateVAModal'
import toast from 'react-hot-toast'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import {
  deleteVAThunk,
  fetchListVAThunk,
  revokeVAThunk,
  selectCurrentPage,
  selectCurretPageSize,
  selectListVA,
  selectTotalVA,
  selectVALoading,
  setAnalyticsPage
} from '../../stores/Reducers/analyticsReducer'
import ConfirmModal from '../../components/ConfirmModal'
import ListRecordingLoading from '../Recording/ListLoading'
import NoDataComponent from '../../components/NoDataComponent'
import AnalyticItemComponent from './AnalyticItem'
import { DisableVAParams } from '../../models/apiKeys'
import PaginationComponent from '../../components/Pagination'
import PageHeaderText from '../../components/PageHeaderText'

const VideoAnalysManagement = () => {
  const [openVAModal, setOpenVAModal] = useState<boolean>(false)
  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false)
  const [openConfirmRevokeModal, setOpenConfirmRevokeModal] =
    useState<boolean>(false)
  const listVa = useAppSelector(selectListVA)
  const totalVA = useAppSelector(selectTotalVA)
  const pageSize = useAppSelector(selectCurretPageSize)
  const currentPage = useAppSelector(selectCurrentPage)
  const loading = useAppSelector(selectVALoading)
  const dispatch = useAppDispatch()
  const deletedVA = useRef<IAnalytics>()
  const revokedVA = useRef<IAnalytics>()

  const triggerDeleteVA = (analytic: IAnalytics) => {
    deletedVA.current = analytic
    setOpenConfirmModal(true)
  }

  const triggerRevokeVA = (analytic: IAnalytics) => {
    revokedVA.current = analytic
    setOpenConfirmRevokeModal(true)
  }

  const onConfirmDeleteVA = async () => {
    const action = await dispatch(deleteVAThunk(deletedVA?.current?.id ?? ''))
    if (deleteVAThunk.fulfilled.match(action)) {
      toast.success(`Plugin ${deletedVA.current?.name} has been deleted!`)
    }
    setOpenConfirmModal(false)
  }

  const onConfirmRevokeVA = async () => {
    const data: DisableVAParams = {
      analytic_id: revokedVA.current?.id ?? '',
      data: {
        id: revokedVA.current?.api_keys?.[0]?.id ?? '',
        scope: 'analytic'
      }
    }
    const action = await dispatch(revokeVAThunk(data))
    if (revokeVAThunk.fulfilled.match(action)) {
      setOpenConfirmRevokeModal(false)
    }
  }

  useEffect(() => {
    dispatch(fetchListVAThunk())
  }, [currentPage])

  return (
    <div className="flex flex-col w-full h-full">
      <div className="flex w-full flex-col sm:flex-row items-center justify-between gap-2">
        <PageHeaderText>Video Analytics Management</PageHeaderText>
        <PrimaryButton
          onClick={() => setOpenVAModal(true)}
          className="sm:max-w-[200px] py-[10px]"
        >
          Add new VA
        </PrimaryButton>
      </div>
      {loading ? (
        <ListRecordingLoading />
      ) : (
        <div className="mt-4 w-full flex flex-1 overflow-auto pr-2">
          {listVa.length > 0 ? (
            <div className="h-fit grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-12 list-none w-full">
              {listVa.map((va) => (
                <AnalyticItemComponent
                  analytic={va}
                  key={va.id}
                  triggerDeleteVA={triggerDeleteVA}
                  triggerRevokeVA={triggerRevokeVA}
                />
              ))}
            </div>
          ) : (
            <NoDataComponent />
          )}
        </div>
      )}
      <PaginationComponent
        totalItems={totalVA}
        pageSize={pageSize}
        currentPage={currentPage}
        onPageChange={(page) => dispatch(setAnalyticsPage(page))}
      />
      <CustomModal
        openState={[openVAModal, setOpenVAModal]}
        title={'Add new Plugin'}
      >
        <CreateVAModal onCancel={() => setOpenVAModal(false)} />
      </CustomModal>
      <ConfirmModal
        onConfirm={onConfirmDeleteVA}
        text={'Are you sure you want to delete this plugin?'}
        openState={[openConfirmModal, setOpenConfirmModal]}
        title={
          <p className="truncate max-w-[70vh]">{`Delete Plugin ${deletedVA?.current?.name}`}</p>
        }
      />
      <ConfirmModal
        onConfirm={onConfirmRevokeVA}
        text={'The selected plugin will not be available for future usage. '}
        openState={[openConfirmRevokeModal, setOpenConfirmRevokeModal]}
        title={
          <p className="truncate max-w-[70vh]">{`Revoke Plugin '${revokedVA?.current?.name}'`}</p>
        }
      />
    </div>
  )
}

export default VideoAnalysManagement
