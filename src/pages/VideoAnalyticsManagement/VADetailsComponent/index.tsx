import moment from 'moment'
import PrimaryButton from '../../../components/PrimaryButtons'
import { Analytics_Streams, IAnalytics } from '../../../models/analytics'
import { Tooltip } from 'antd'
import { getAnalyticStreams, getVAById } from '../../../api/Analytics'
import { useEffect, useState } from 'react'
import ConfirmModal from '../../../components/ConfirmModal'
import { useNavigate, useParams } from 'react-router-dom'
import { useAppDispatch } from '../../../stores/hooks'
import { deleteVAThunk } from '../../../stores/Reducers/analyticsReducer'
import NavigateBackComponent from '../../../components/NavigateBackComponent'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import ListAnalyticStreamsComponent from '../ListAnalyticStreams'
import { ENV } from '../../../utils'
import NoDataComponent from '../../../components/NoDataComponent'

const VADetailsComponent = () => {
  const { analytic_id } = useParams()
  const navigate = useNavigate()
  const [listStreams, setListStreams] = useState<Analytics_Streams[] | null>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [showAPIKey, setShowAPIKey] = useState<boolean>(false)
  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false)
  const [selectedVa, setSelectedVa] = useState<IAnalytics | null>(null)
  const dispatch = useAppDispatch()

  const handleFetchListStream = async () => {
    setLoading(true)
    try {
      const analyticDetailResponse = await getVAById(analytic_id ?? '')
      setSelectedVa(analyticDetailResponse.data)
      const res = await getAnalyticStreams(
        analyticDetailResponse.data.api_keys[0].api_key,
        analyticDetailResponse.data.id
      )
      setListStreams(res.data)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
      setListStreams([])
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteVA = async () => {
    const action = await dispatch(deleteVAThunk(selectedVa?.id ?? ''))
    if (deleteVAThunk.fulfilled.match(action)) {
      navigate(-1)
    }
    setOpenConfirmModal(false)
  }

  useEffect(() => {
    handleFetchListStream()
  }, [])

  if (!selectedVa && !loading) {
    return (
      <>
        <NavigateBackComponent />
        <NoDataComponent text="This VA is not available or has been deleted by another user" />
      </>
    )
  }

  return (
    <div className="w-full h-full flex-1 flex flex-col gap-5">
      <NavigateBackComponent />
      <div className="flex w-full gap-4 flex-col sm:flex-row justify-between">
        <div className="rounded-xl flex flex-col gap-2 border border-solid border-[#E3E3E3] max-w-full sm:max-w-[50%] min-w-[300px] py-3 px-[18px]">
          {!listStreams ||
          listStreams?.every((stream) => stream.status === 'PROCESSED') ? (
            <div className="flex items-center gap-2 text-sm">
              <div className="min-w-3 min-h-3 max-h-3 max-w-3 rounded-full bg-red-400"></div>
              <span>Idle</span>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-sm">
              <div className="min-w-3 min-h-3 max-h-3 max-w-3 rounded-full bg-[#79DA4B]"></div>
              <span>Processing</span>
            </div>
          )}
          <div className="flex flex-wrap gap-6 w-full">
            <p className="text-xs flex gap-2 truncate max-w-[200px]">
              Provider:{' '}
              <Tooltip
                className="font-semibold block truncate"
                title={selectedVa?.provider}
                mouseEnterDelay={0.5}
              >
                {selectedVa?.provider}
              </Tooltip>
            </p>
            <p className="text-xs">
              Created at:{' '}
              <span className="font-semibold">
                {moment(selectedVa?.created_at).format('DD MMM YYYY')}
              </span>
            </p>
            <p className="text-xs">
              Updated at:{' '}
              <span className="font-semibold">
                {moment(selectedVa?.updated_at).format('DD MMM YYYY')}
              </span>
            </p>
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs">
              Api Key:{' '}
              <span className="font-semibold">
                {showAPIKey
                  ? selectedVa?.api_keys[0].api_key ?? selectedVa?.api_key
                  : '*******************************'}
              </span>
            </p>
            <div onClick={() => setShowAPIKey((prev) => !prev)}>
              {showAPIKey ? (
                <EyeIcon
                  className="mt-2"
                  height={16}
                  width={16}
                  cursor={'pointer'}
                />
              ) : (
                <EyeSlashIcon height={16} width={16} cursor={'pointer'} />
              )}
            </div>
          </div>
        </div>
        <PrimaryButton
          className="sm:max-w-[200px] h-[40px]"
          onClick={() => setOpenConfirmModal(true)}
        >
          <p className="font-semibold text-white">Delete VA</p>
        </PrimaryButton>
      </div>
      <ListAnalyticStreamsComponent
        listStreams={listStreams}
        loading={loading}
      />
      <ConfirmModal
        onConfirm={handleDeleteVA}
        text={'Are you sure you want to delete this plugin?'}
        openState={[openConfirmModal, setOpenConfirmModal]}
        title={`Delete Plugin ${selectedVa?.name}`}
      />
    </div>
  )
}

export default VADetailsComponent
