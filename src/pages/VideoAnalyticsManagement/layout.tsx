import { useEffect } from 'react'
import toast from 'react-hot-toast'
import { Outlet } from 'react-router-dom'
import { TOAST_LIST_UPDATED_ALERT } from '../../enum/AnalyticEvents'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

const VAPageLayout = () => {
  dayjs.extend(utc)
  useEffect(() => {
    return () => {
      toast.remove(TOAST_LIST_UPDATED_ALERT)
    }
  }, [])
  return <Outlet />
}

export default VAPageLayout
