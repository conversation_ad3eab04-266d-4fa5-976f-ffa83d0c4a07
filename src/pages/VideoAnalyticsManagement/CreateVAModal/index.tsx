import { useForm } from 'react-hook-form'
import SecondaryButton from '../../../components/SecondaryButtons'
import PrimaryButton from '../../../components/PrimaryButtons'
import { REGEX_PREVENT_ONLY_WHITESPACES } from '../../../enum/Regex'
import { AddVAInputs } from '../../../models/analytics'
import TextInputComponent from '../../../components/TextInputComponent'
import FormInputContainer from '../../../components/FormInputComponent'
import { useAppDispatch } from '../../../stores/hooks'
import { createVAThunk } from '../../../stores/Reducers/analyticsReducer'
import toast from 'react-hot-toast'

type Props = {
  onCancel: () => void
}
const MAXIMUM_TEXT_CHARACTERS = 50

const CreateVAModal = ({ onCancel }: Props) => {
  const {
    formState: { errors, isSubmitting },
    handleSubmit,
    control,
    watch
  } = useForm<AddVAInputs>({ mode: 'onChange' })
  const dispatch = useAppDispatch()
  const vaNameValue = watch('name')
  const vaProviderValue = watch('provider')
  const vaClassValue = watch('class')

  const handleCreateVA = async (data: AddVAInputs) => {
    const trimedData: AddVAInputs = {
      name: data.name.trim(),
      provider: data.provider.trim(), // remove whitespaces from both ends of the string
      class: data.class.trim()
    }
    const action = await dispatch(createVAThunk(trimedData))
    if (createVAThunk.fulfilled.match(action)) {
      onCancel()
      toast.success(`${trimedData.name} is created!`)
    }
  }

  return (
    <form
      onSubmit={handleSubmit(handleCreateVA)}
      className="w-full h-full flex-col gap-[10px]"
    >
      <div className="flex flex-col sm:flex-row sm:gap-4">
        <FormInputContainer<AddVAInputs>
          control={control}
          name="name"
          label={'Name'}
          vertialAlign
          errors={errors}
          required
          maxCharactersText={`${
            vaNameValue?.trim()?.length ?? 0
          }/${MAXIMUM_TEXT_CHARACTERS} characters`}
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_PREVENT_ONLY_WHITESPACES,
              message: 'Name cannot contain only spaces'
            }
          }}
          render={({ field }) => (
            <TextInputComponent
              {...field}
              maxLength={MAXIMUM_TEXT_CHARACTERS}
              placeholder="Enter your plugin's name"
            />
          )}
        />
        <FormInputContainer<AddVAInputs>
          control={control}
          name="provider"
          label={'Provider'}
          vertialAlign
          errors={errors}
          required
          maxCharactersText={`${
            vaProviderValue?.trim()?.length ?? 0
          }/${MAXIMUM_TEXT_CHARACTERS} characters`}
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_PREVENT_ONLY_WHITESPACES,
              message: 'Name cannot contain only spaces'
            }
          }}
          render={({ field }) => (
            <TextInputComponent
              {...field}
              onChange={field.onChange}
              maxLength={MAXIMUM_TEXT_CHARACTERS}
              placeholder="Enter Provider's name"
            />
          )}
        />
      </div>
      <div className="flex flex-col sm:flex-row sm:gap-4">
        <FormInputContainer<AddVAInputs>
          control={control}
          name="class"
          label={'Class'}
          maxCharactersText={`${
            vaClassValue?.trim()?.length ?? 0
          }/${MAXIMUM_TEXT_CHARACTERS} characters`}
          vertialAlign
          errors={errors}
          required
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_PREVENT_ONLY_WHITESPACES,
              message: 'Name cannot contain only spaces'
            }
          }}
          render={({ field }) => (
            <TextInputComponent
              {...field}
              maxLength={MAXIMUM_TEXT_CHARACTERS}
              placeholder="Enter class's name"
            />
          )}
        />
      </div>

      <div className="flex items-center justify-end py-4 sticky bottom-0 gap-3 w-full">
        <SecondaryButton className="max-w-[250px]" onClick={onCancel}>
          Cancel
        </SecondaryButton>
        <PrimaryButton
          className="max-w-[250px]"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          type="submit"
        >
          Create
        </PrimaryButton>
      </div>
    </form>
  )
}

export default CreateVAModal
