import SearchIcon from '../../assets/svgs/SearchIcon'
import DropdownOptions from '../../components/DropdownOptions'
import SecondaryButton from '../../components/SecondaryButtons'
import FilterIcon from '../../assets/svgs/FilterIcon'
import TagComponent from '../../components/Tags'
import { useEffect, useState } from 'react'
import { IOption, UpdateFilters } from '../../interfaces'
import CustomModal from '../../components/Modal'
import { ListCameraFilterParams } from '../../models/camera'
import { useParamsPagination } from '../../utils/hooks/useParamsPagination'
import { ROUTE_PATH } from '../../enum/RoutePath'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import {
  fetchListCameraThunk,
  selectListCamera,
  selectListCameraLoading,
  selectTotalCamera
} from '../../stores/Reducers/cameraReducers'
import LiveViewItem from './LiveViewItem'
import ListRecordingLoading from '../Recording/ListLoading'
import NoDataComponent from '../../components/NoDataComponent'
import { useDebounce } from '../../utils/hooks/useDebounce'
import TextInputComponent from '../../components/TextInputComponent'
import { FILTER_TYPES } from '../../enum/FilterTypes'
import FilterModalComponent from '../../components/FIilterModalComponent'
import PaginationComponent from '../../components/Pagination'
import { DEFAULT_PAGE_SIZE } from '../../utils'
import PageHeaderText from '../../components/PageHeaderText'

const options: IOption[] = [
  { value: 'created_at-desc', label: 'Newest to oldest' },
  { value: 'created_at-asc', label: 'Oldest to latest' },
  { value: 'name-asc', label: 'Camera name (A-Z)' },
  { value: 'name-desc', label: 'Camera name (Z-A)' }
]
const DEFAULT_CAMERA_PAGESIZE = 4
const LiveViewPage = () => {
  const { page, pageSize, navigatePage } = useParamsPagination({
    navigatePath: ROUTE_PATH.Liveview
  })
  const dispatch = useAppDispatch()
  const listCamera = useAppSelector(selectListCamera)
  const totalCamera = useAppSelector(selectTotalCamera)
  const loading = useAppSelector(selectListCameraLoading)
  const [searchText, setSearchText] = useState<string>('')
  const [orderBy, setOrderBy] = useState<string>('created_at-desc')
  const [filteredProject, setFilteredProject] = useState<IOption[]>([])
  const [filteredAgent, setFilteredAgent] = useState<IOption[]>([])
  const [filteredTag, setFilteredTag] = useState<IOption[]>([])
  const [filteredVA, setFilteredVA] = useState<IOption[]>([])
  const [openFilters, setOpenFilters] = useState<boolean>(false)
  const handleSorting = (value: string) => {
    setOrderBy(value)
  }
  const removeLiveViewFilter = (params: UpdateFilters) => {
    const { type, data } = params
    switch (type) {
      case FILTER_TYPES.AGENTS:
        setFilteredAgent((prev) =>
          prev.filter((old_item) => old_item.value !== data.value)
        )
        break
      case FILTER_TYPES.PROJECTS:
        setFilteredProject((prev) =>
          prev.filter((old_item) => old_item.value !== data.value)
        )
        break
      case FILTER_TYPES.TAGS:
        setFilteredTag((prev) =>
          prev.filter((old_item) => old_item.value !== data.value)
        )
        break
      case FILTER_TYPES.VA_PLUGINS:
        setFilteredVA((prev) =>
          prev.filter((old_item) => old_item.value !== data.value)
        )
        break
    }
  }
  const addLiveViewFilter = (params: UpdateFilters) => {
    const { type, data } = params
    switch (type) {
      case FILTER_TYPES.AGENTS:
        setFilteredAgent((prev) => [data, ...prev])
        break
      case FILTER_TYPES.PROJECTS:
        setFilteredProject((prev) => [data, ...prev])
        break
      case FILTER_TYPES.TAGS:
        setFilteredTag((prev) => [data, ...prev])
        break
      case FILTER_TYPES.VA_PLUGINS:
        setFilteredVA((prev) => [data, ...prev])
        break
    }
  }
  const clearAllFilter = () => {
    setFilteredAgent([])
    setFilteredProject([])
    setFilteredTag([])
    setFilteredVA([])
  }
  const handleFetchListCamera = async () => {
    const data: ListCameraFilterParams = {
      'page-no': Number(page),
      'page-size': Number(pageSize),
      'order-by': orderBy.split('-')[0],
      'order-direction': orderBy.split('-')[1],
      status: 'Online',
      search: searchText.trim()
    }
    if (filteredProject.length > 0) {
      data['project-ids'] = filteredProject.map((project) => project.value)
    }
    if (filteredTag.length > 0) {
      data['tag-ids'] = filteredTag.map((tag) => tag.value)
    }
    if (filteredAgent.length > 0) {
      data['agent-ids'] = filteredAgent.map((agent) => agent.value)
    }
    if (filteredVA.length > 0) {
      data['va-plugin-ids'] = filteredVA.map((va) => va.value)
    }
    await dispatch(fetchListCameraThunk(data))
    isMounted.current = true
  }
  const fetchListCameraWithFilterSorting = () =>
    page === '1' ? handleFetchListCamera() : navigatePage(1, 10)

  useEffect(() => {
    handleFetchListCamera()
  }, [page, pageSize])

  useEffect(() => {
    if (isMounted.current) {
      fetchListCameraWithFilterSorting()
    }
  }, [orderBy])

  const { isMounted } = useDebounce({
    func: fetchListCameraWithFilterSorting,
    searchText: searchText
  })

  useEffect(() => {
    if (isMounted.current && !openFilters) {
      // only fetch by filters when filter modal not opened
      fetchListCameraWithFilterSorting()
    }
  }, [filteredAgent, filteredProject, filteredTag, filteredVA])
  return (
    <div className="w-full overflow-auto h-full flex flex-col">
      <PageHeaderText>Live View</PageHeaderText>
      <div className="w-full flex-wrap lg:flex-nowrap justify-start lg:justify-between flex my-2 items-center gap-2">
        <TextInputComponent
          prefixIcon={<SearchIcon />}
          onChange={(e) => setSearchText(e.target.value)}
          placeholder="Search..."
        />
        <DropdownOptions
          onSelect={handleSorting}
          label="Sort by"
          options={options}
          selected={orderBy}
        />
        <SecondaryButton
          className={'justify-between sm:max-w-[200px]'}
          backgroundColor="#f4f4f4"
          onClick={() => setOpenFilters(true)}
        >
          <div className="flex items-center gap-2">
            <FilterIcon />
            <p className="font-normal">Filters</p>
          </div>
        </SecondaryButton>
      </div>
      <div className="flex w-full gap-2 flex-wrap mb-4">
        {filteredAgent.map((item) => (
          <TagComponent
            key={item.value}
            text={item.label}
            removable
            textColor="#843BAC"
            border="1px solid #843BAC"
            onRemove={() =>
              removeLiveViewFilter({ type: FILTER_TYPES.AGENTS, data: item })
            }
          />
        ))}
        {filteredProject.map((item) => (
          <TagComponent
            key={item.value}
            text={item.label}
            removable
            textColor="#843BAC"
            border="1px solid #843BAC"
            onRemove={() =>
              removeLiveViewFilter({ type: FILTER_TYPES.PROJECTS, data: item })
            }
          />
        ))}
        {filteredTag.map((item) => (
          <TagComponent
            key={item.value}
            text={item.label}
            removable
            textColor="#843BAC"
            border="1px solid #843BAC"
            onRemove={() =>
              removeLiveViewFilter({ type: FILTER_TYPES.TAGS, data: item })
            }
          />
        ))}
        {filteredVA.map((item) => (
          <TagComponent
            key={item.value}
            text={item.label}
            onRemove={() =>
              removeLiveViewFilter({
                type: FILTER_TYPES.VA_PLUGINS,
                data: item
              })
            }
            removable
            textColor="#843BAC"
            border="1px solid #843BAC"
          />
        ))}
      </div>
      <div className="w-full sm:pr-2 mb-4 flex-1 justify-center md:justify-between flex flex-wrap gap-6">
        {loading ? (
          <ListRecordingLoading />
        ) : listCamera.length === 0 ? (
          <NoDataComponent />
        ) : (
          listCamera.map((camera) => (
            <div key={camera.id} className="w-[48%]">
              <LiveViewItem project={camera.project?.name} camera={camera} />
            </div>
          ))
        )}
      </div>
      <PaginationComponent
        totalItems={totalCamera}
        onPageChange={(page) => navigatePage(page, DEFAULT_PAGE_SIZE)}
        currentPage={Number(page)}
        pageSize={DEFAULT_CAMERA_PAGESIZE}
      />
      <CustomModal
        title=""
        className="max-w-[50vw]"
        openState={[openFilters, setOpenFilters]}
      >
        <FilterModalComponent
          onApply={fetchListCameraWithFilterSorting}
          closeModal={() => setOpenFilters(false)}
          filteredAgent={filteredAgent}
          filteredProject={filteredProject}
          filteredTag={filteredTag}
          filteredVA={filteredVA}
          onAddFilters={(data: UpdateFilters) => addLiveViewFilter(data)}
          onRemoveFilters={(data: UpdateFilters) => removeLiveViewFilter(data)}
          clearAllFilters={() => clearAllFilter()}
        />
      </CustomModal>
    </div>
  )
}

export default LiveViewPage
