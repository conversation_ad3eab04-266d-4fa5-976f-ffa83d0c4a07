import dayjs from 'dayjs'
import DefaultThumbnail from '../../../assets/svgs/DefaultThumbnail'
import { ICameras } from '../../../models/camera'
import { ENV, classNames } from '../../../utils'
import { previewCamera } from '../../../api/Camera'
import { useState } from 'react'
import toast, { LoaderIcon } from 'react-hot-toast'
import HlsPlayer from '../../../components/HLSPlayer'
type Props = {
  camera?: ICameras
  project?: string
}

const LiveViewItem = ({ camera, project = '' }: Props) => {
  const [previewUrl, setPreviewUrl] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const fetchCameraPreview = async () => {
    setLoading(true)
    try {
      const res = await previewCamera(camera?.id ?? '')
      setPreviewUrl(res.data.playblack_stream_url)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
      toast.error('Error Preview Camera stream, please try again!', {
        position: 'top-right'
      })
    } finally {
      setLoading(false)
    }
  }
  return (
    <div className="min-w-[300px] bg-white w-full min-h-[300px] max-h-[380px] flex flex-col border border-solid cursor-pointer border-[#E3E3E3] rounded-[4px]">
      <div className="w-full flex flex-col justify-between py-1 px-3 gap-[10px]">
        <div className="flex w-full mt-1 gap-3 items-center truncate">
          <div className="flex items-center justify-center gap-2">
            <div
              className={classNames(
                'min-w-3 min-h-3 h-3 w-3 rounded-full',
                camera?.status.toLocaleLowerCase() === 'online'
                  ? 'bg-green-400'
                  : 'bg-gray-300'
              )}
            ></div>
            <p className="text-sm font-medium truncate w-full">
              {camera?.status}
            </p>
          </div>
          <p className="text-sm font-medium truncate">
            {camera?.name}{' '}
            <span className="ml-1 truncate">({camera?.rtsp})</span>
          </p>
        </div>
        <div className="flex items-center gap-2">
          <p className="text-sm font-medium truncate">Project: {project}</p>
          <span> - </span>
          <p className="text-sm font-medium truncate">
            {dayjs(camera?.created_at).format('D MMM YYYY HH:mm:ss')}
          </p>
        </div>
      </div>

      {loading ? (
        <div className="w-full min-h-[300px] flex items-center">
          <LoaderIcon className="m-auto min-h-[40px] min-w-[40px]" />
        </div>
      ) : (
        <div className="relative w-full h-fit max-h-full">
          {previewUrl === '' ? (
            <div
              onClick={fetchCameraPreview}
              className="w-full h-[300px] bg-cover bg-center bg-opacity-20 bg-default-thumbnail flex justify-center items-center"
            >
              <DefaultThumbnail />
            </div>
          ) : (
            <div className="w-full  max-h-[300px] flex justify-center items-center">
              <HlsPlayer isStreaming src={previewUrl} />
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default LiveViewItem
