import { Select, Skeleton } from 'antd'
import { RETENTION_OPTIONS } from '../../../../stores/Reducers/logsReducer'
import {
  IRetentionSetting,
  IUpdateRetention
} from '../../../../models/retention'
import { useEffect, useRef, useState } from 'react'
import {
  getRetentionPeriod,
  updateRetentionPeriod
} from '../../../../api/Retention'
import { getErrorMessage } from '../../../../utils'
import PrimaryButton from '../../../../components/PrimaryButtons'
import SecondaryButton from '../../../../components/SecondaryButtons'
import { toast } from 'react-hot-toast'
import NoDataComponent from '../../../../components/NoDataComponent'
// import NoDataComponent from '../../../../components/NoDataComponent'
const DEFAULT_RETENTION_SETTING: IRetentionSetting = {
  id: '',
  retention_months: 6,
  created_at: '',
  updated_at: '',
  deleted_at: null
}
const StorageManagementHeader = () => {
  const [retentionSetting, setRetentionSetting] = useState<IRetentionSetting>(
    DEFAULT_RETENTION_SETTING
  )
  const [loading, setLoading] = useState<boolean>(false)
  const [errorMsg, setErrorMsg] = useState<string>('')
  const [processing, setProcessing] = useState<boolean>(false)
  const defaultPeriod = useRef<number>(0)
  const abortController = useRef<AbortController>()

  const handleGetRetentionSetting = async () => {
    setLoading(true)
    try {
      const res = await getRetentionPeriod()
      setRetentionSetting(res.data)
      defaultPeriod.current = res.data.retention_months
    } catch (error) {
      setErrorMsg(getErrorMessage(error))
      console.error(errorMsg)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateRetention = async () => {
    abortController.current = new AbortController()
    const data: IUpdateRetention = {
      id: retentionSetting.id,
      retention_period: retentionSetting.retention_months
    }
    setProcessing(true)
    try {
      await updateRetentionPeriod(data)
      toast.success('Your changes have been saved')
      defaultPeriod.current = data.retention_period
    } catch (error) {
      toast.error(getErrorMessage(error))
    } finally {
      setProcessing(false)
      abortController.current = undefined
    }
  }

  const handleCancel = () => {
    setRetentionSetting((prev) => ({
      ...prev,
      retention_months: defaultPeriod.current
    }))
    abortController.current?.abort()
  }

  useEffect(() => {
    handleGetRetentionSetting()
  }, [])

  if (loading) {
    return <Skeleton />
  }

  if (errorMsg) {
    return <NoDataComponent text={errorMsg} />
  }

  return (
    <>
      <div className="flex flex-col sm:flex-row gap-1 sm:gap-4 items-center">
        <p className="text-base font-bold">Retention Policy</p>
        <Select
          options={RETENTION_OPTIONS}
          onChange={(value) =>
            setRetentionSetting((prev) => ({
              ...prev,
              retention_months: Number(value)
            }))
          }
          className="min-w-full sm:min-w-40 min-h-10"
          value={retentionSetting?.retention_months.toString()}
        />
        {retentionSetting &&
          retentionSetting?.retention_months !== defaultPeriod.current && (
            <div className="flex gap-2 justify-between">
              <SecondaryButton onClick={handleCancel}>Cancel</SecondaryButton>
              <PrimaryButton
                isDisabled={processing}
                isLoading={processing}
                onClick={handleUpdateRetention}
              >
                Save
              </PrimaryButton>
            </div>
          )}
      </div>
      <p className="text-xs sm:text-sm">
        Your data will be automatically deleted at the end of the retention
        period. You will receive an email reminder 5 days before deletion.
      </p>
    </>
  )
}

export default StorageManagementHeader
