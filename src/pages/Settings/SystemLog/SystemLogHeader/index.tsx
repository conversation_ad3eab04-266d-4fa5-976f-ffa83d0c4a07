import React, { useCallback, useEffect, useState } from 'react'
import DateTimeRangeFilter from '../../../../components/DateTimeRangeFilter'
import TextInputComponent from '../../../../components/TextInputComponent'
import SearchIcon from '../../../../assets/svgs/SearchIcon'
import PrimaryButton from '../../../../components/PrimaryButtons'
import dayjs, { Dayjs } from 'dayjs'
import { useDebounce } from '../../../../utils/hooks/useDebounce'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  changeDateRange,
  changeLogContentSearch,
  selectLogFromDate,
  selectLogContentSearch,
  selectLogToDate,
  fetchListSystemLogThunk,
  selectSystemLogsPage,
  changeSystemLogPage,
  selectSystemLogSortOption,
  selectSystemLogTypeOptions
} from '../../../../stores/Reducers/logsReducer'
import { IExportLogQuery } from '../../../../models/systemLogs'
import { exportSystemLogs } from '../../../../api/SystemLogs'
import { toast } from 'react-hot-toast'
import { downloadFromAPI, getErrorMessage } from '../../../../utils'
import moment from 'moment'
import { ExclamationCircleIcon } from '@heroicons/react/24/outline'

const SystemLogHeaderFilterComponent = () => {
  const startDate = useAppSelector(selectLogFromDate)
  const endDate = useAppSelector(selectLogToDate)
  const logSearchText = useAppSelector(selectLogContentSearch)
  const systemLogPage = useAppSelector(selectSystemLogsPage)
  const systemLogSort = useAppSelector(selectSystemLogSortOption)
  const systemLogTypes = useAppSelector(selectSystemLogTypeOptions)
  const dispatch = useAppDispatch()
  const [errorMessage, setErrorMessage] = useState<string>('')

  const onDateRangeChange = useCallback(
    (dateRange: null | (Dayjs | null)[]) => {
      dispatch(
        changeDateRange(
          dateRange?.map((date) => date?.toISOString() ?? '') ?? []
        )
      )
    },
    [startDate, endDate]
  )

  const onLogSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      dispatch(changeLogContentSearch(e.target.value))
    },
    [logSearchText]
  )

  const onSystemLogQueryChange = () => {
    if ((startDate && !endDate) || (!startDate && endDate)) {
      // if missing only one of the dates
      setErrorMessage('Both start date and end date must be provided!')
      return
    }
    return systemLogPage > 1
      ? dispatch(changeSystemLogPage(1))
      : handleFetchListSystemLogs()
  }

  //fetch data when searching
  const { isMounted } = useDebounce({
    func: onSystemLogQueryChange,
    searchText: logSearchText
  })

  const handleFetchListSystemLogs = async () => {
    if (errorMessage !== '') {
      setErrorMessage('')
    }
    await dispatch(fetchListSystemLogThunk())
    isMounted.current = true
  }

  const handleExportLogs = async () => {
    const toastId = toast.loading('Your file is being prepared...')
    const data: IExportLogQuery = {
      content: logSearchText,
      type: systemLogTypes
    }
    if (startDate) {
      data['start-time'] = startDate
    }
    if (endDate) {
      data['end-time'] = endDate
    }

    try {
      const res = await exportSystemLogs(data)
      const blob = new Blob([res], { type: 'text/csv' })
      downloadFromAPI(
        blob,
        `SystemLogs_${moment(new Date()).format('yyyy/DD/MM HH:mm:ss')}.csv`
      )
      toast.success('Your file is being downloaded!', { id: toastId })
    } catch (error) {
      toast.error(getErrorMessage(error), { id: toastId })
    }
  }

  // refetch data with sortings and filters
  useEffect(() => {
    if (isMounted.current) {
      onSystemLogQueryChange()
    }
  }, [systemLogSort, startDate, endDate, systemLogTypes])

  //fetch data when changing page
  useEffect(() => {
    handleFetchListSystemLogs()
  }, [systemLogPage])

  return (
    <>
      <div className="flex flex-wrap gap-2 items-center">
        <DateTimeRangeFilter
          startDate={!startDate ? undefined : dayjs(startDate)}
          endDate={!endDate ? undefined : dayjs(endDate)}
          error={Boolean(errorMessage)}
          onDateRangeChange={onDateRangeChange}
        />
        {errorMessage && (
          <>
            <ExclamationCircleIcon
              aria-hidden="true"
              color="red"
              fill="red"
              className="h-5 w-5 stroke-red-500  fill-white"
            />
            <p className="text-error text-xs mt-0">{errorMessage}</p>
          </>
        )}
      </div>
      <div className="flex w-full flex-wrap justify-between gap-4">
        <TextInputComponent
          prefixIcon={<SearchIcon />}
          placeholder="Search.."
          onChange={onLogSearchChange}
          customClassName="lg:!max-w-[50%]"
        />
        <PrimaryButton onClick={handleExportLogs} className="sm:max-w-52">
          Export Logs
        </PrimaryButton>
      </div>
    </>
  )
}

export default SystemLogHeaderFilterComponent
