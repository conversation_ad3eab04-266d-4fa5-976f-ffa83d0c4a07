import { Skeleton } from 'antd'
import DropdownOptions from '../../../../components/DropdownOptions'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import NoDataComponent from '../../../../components/NoDataComponent'
import {
  DATE_ORDER_OPTIONS,
  SYSTEM_LOG_TYPES_OPTIONS,
  addAllEventTypeFilter,
  addEventTypeFilter,
  changeSystemLogPage,
  changeSystemLogSortOption,
  clearEventTypeFilter,
  removeEventTypeFilter,
  selectListSystemLogs,
  selectSystemLogLoading,
  selectSystemLogSortOption,
  selectSystemLogTypeOptions,
  selectSystemLogsPage,
  selectSystemLogsPageSize,
  selectTotalSystemLogs
} from '../../../../stores/Reducers/logsReducer'
import Markdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import PaginationComponent from '../../../../components/Pagination'
import { useCallback } from 'react'
import moment from 'moment'
import { SYSTEM_LOG_TYPES } from '../../../../enum/SystemLogTypes'

const SystemLogTable = () => {
  const listSystemLogs = useAppSelector(selectListSystemLogs)
  const loading = useAppSelector(selectSystemLogLoading)
  const systemLogPage = useAppSelector(selectSystemLogsPage)
  const systemLogPageSize = useAppSelector(selectSystemLogsPageSize)
  const totalSystemLogs = useAppSelector(selectTotalSystemLogs)
  const selectedSystemLogTypes = useAppSelector(selectSystemLogTypeOptions)
  const systemLogSort = useAppSelector(selectSystemLogSortOption)
  const dispatch = useAppDispatch()

  const handleChangePage = useCallback((page: number) => {
    dispatch(changeSystemLogPage(page))
  }, [])

  const onChangeEventType = useCallback(
    (value: string) => {
      if (value === '') {
        return selectedSystemLogTypes.length === SYSTEM_LOG_TYPES_OPTIONS.length
          ? dispatch(clearEventTypeFilter())
          : dispatch(addAllEventTypeFilter())
      }

      if (selectedSystemLogTypes.includes(value as SYSTEM_LOG_TYPES)) {
        dispatch(removeEventTypeFilter(value as SYSTEM_LOG_TYPES))
        return
      }

      dispatch(addEventTypeFilter(value as SYSTEM_LOG_TYPES))
    },
    [selectedSystemLogTypes]
  )

  return (
    <>
      <div className="w-full flex-1 overflow-auto">
        <div className="-my-2 overflow-x-auto w-full h-full">
          <div className="min-w-full py-2 align-middle">
            <table className="min-w-full relative table w-full divide-gray-300">
              <thead className="sticky top-2 w-full bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="py-3.5 px-4 w-[200px] text-center text-sm font-semibold text-mainBlack"
                  >
                    <div className="flex w-fit mx-auto justify-center gap-2 items-center">
                      <p>Date</p>
                      <DropdownOptions
                        label={''}
                        onlyButtonIcon
                        selected={systemLogSort}
                        options={DATE_ORDER_OPTIONS}
                        onSelect={(value: string) =>
                          dispatch(changeSystemLogSortOption(value))
                        }
                      />
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3.5 min-w-[150px] w-[200px] text-center text-sm font-semibold text-mainBlack"
                  >
                    Time
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-center min-w-[150px] w-[200px] text-sm font-semibold text-mainBlack"
                  >
                    <div className="flex w-fit mx-auto justify-center gap-2 items-center">
                      <p>
                        Event type{' '}
                        {selectedSystemLogTypes.length > 0 && (
                          <span>{`(${selectedSystemLogTypes.length})`}</span>
                        )}{' '}
                      </p>
                      <DropdownOptions
                        label={''}
                        onlyButtonIcon
                        selected={selectedSystemLogTypes}
                        options={SYSTEM_LOG_TYPES_OPTIONS}
                        onSelect={onChangeEventType}
                        type="checkbox"
                      />
                    </div>
                  </th>

                  <th
                    scope="col"
                    className="py-3.5 px-3 min-w-[200px] text-left text-sm font-semibold text-mainBlack"
                  >
                    Description
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white w-full overflow-auto">
                {loading ? (
                  <>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                  </>
                ) : listSystemLogs?.length > 0 ? (
                  listSystemLogs?.map((logItem) => (
                    <tr key={logItem.id} className="even:bg-gray-50">
                      <td className="whitespace-nowrap px-4 text-center py-4 text-sm text-mainBlack">
                        {moment(logItem.created_at).format('MMMM DD, YYYY')}
                      </td>
                      <td className="whitespace-nowrap text-center px-3 py-4 text-sm truncate text-mainBlack">
                        {moment(logItem.created_at).format('HH:mm:ss')}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-center text-sm">
                        <p className="text-mainBlack capitalize cursor-pointer hover:underline">
                          {logItem?.type.toLocaleLowerCase()}
                        </p>
                      </td>
                      <td className="py-4 px-3 text-sm">
                        <Markdown
                          remarkPlugins={[remarkGfm]}
                          className={'text-sm line-clamp-2'}
                        >
                          {logItem.content}
                        </Markdown>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="h-[300px]">
                      <NoDataComponent />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <PaginationComponent
        totalItems={totalSystemLogs}
        currentPage={systemLogPage}
        onPageChange={handleChangePage}
        pageSize={systemLogPageSize}
      />
    </>
  )
}

export default SystemLogTable
