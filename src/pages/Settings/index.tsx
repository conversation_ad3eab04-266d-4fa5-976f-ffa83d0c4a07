import { Outlet, useLocation } from 'react-router-dom'
import { ROUTE_PATH } from '../../enum/RoutePath'
import { ITabNavigationLinks } from '../../interfaces'
import HorizontalTabNavigation from '../../components/HorizontalTabNavigation'
import PageHeaderText from '../../components/PageHeaderText'

const SettingsPage = () => {
  const location = useLocation()
  const tabs: ITabNavigationLinks[] = [
    {
      name: 'Storage management',
      href: ROUTE_PATH.Settings,
      current: location.pathname === ROUTE_PATH.Settings
    },
    {
      name: 'System logs',
      href: ROUTE_PATH.System_Logs,
      current: location.pathname === ROUTE_PATH.System_Logs
    }
  ]
  return (
    <div className="w-full flex flex-col overflow-hidden flex-1">
      <div className="flex w-full flex-wrap items-center justify-between gap-2">
        <PageHeaderText>Settings</PageHeaderText>
      </div>
      <HorizontalTabNavigation tabs={tabs} />
      <div className="flex-1 flex flex-col w-full overflow-auto">
        <Outlet />
      </div>
    </div>
  )
}

export default SettingsPage
