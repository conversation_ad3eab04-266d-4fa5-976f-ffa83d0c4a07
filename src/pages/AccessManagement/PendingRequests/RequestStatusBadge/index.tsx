import { IPendingUsers } from '../../../../models/auth'
import { USER_PENDING_STATUS } from '../../../../enum/UserPendingStatus'
import { useMemo } from 'react'

type Props = {
  pendingRequest: IPendingUsers
}

const RequestStatusBadge = ({ pendingRequest }: Props) => {
  const renderStatusColor = useMemo(() => {
    switch (pendingRequest.status) {
      case USER_PENDING_STATUS.APPROVED:
      case USER_PENDING_STATUS.COMPLETED:
        return '!bg-[#79DA4B] text-white'

      case USER_PENDING_STATUS.CANCELED:
      case USER_PENDING_STATUS.REJECTED:
        return 'bg-red-400 text-white'

      case USER_PENDING_STATUS.INVITED:
        return '!bg-[#E3E3E3] text-[#272727]'
      case USER_PENDING_STATUS.PENDING:
        return '!bg-[#E3E3E3] text-[#272727]'

      default:
        return '!bg-[#E3E3E3] text-[#272727]'
    }
  }, [pendingRequest])
  return (
    <div
      className={`${renderStatusColor} rounded-3xl min-w-full capitalize max-w-[220px] px-3 py-2`}
    >
      {pendingRequest.status.toLocaleLowerCase()}
    </div>
  )
}

export default RequestStatusBadge
