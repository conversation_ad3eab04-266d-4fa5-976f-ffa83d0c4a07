import { useForm } from 'react-hook-form'
import FormInputContainer from '../../../../components/FormInputComponent'
import { InviteUserParams } from '../../../../models/auth'
import TextInputComponent from '../../../../components/TextInputComponent'
import { Select } from 'antd'
import { useEffect, useState } from 'react'
import { IGroupQuery, ListGroup } from '../../../../models/groups'
import { toast } from 'react-hot-toast'
import { DEFAULT_PAGE_SIZE, ENV } from '../../../../utils'
import { getAuthorizedGroupList } from '../../../../api/Groups'
import { IRoles } from '../../../../models/roles'
import { getListRoles } from '../../../../api/Roles'
import SecondaryButton from '../../../../components/SecondaryButtons'
import PrimaryButton from '../../../../components/PrimaryButtons'
import {
  REGEX_EMAIL,
  REGEX_PREVENT_ONLY_WHITESPACES
} from '../../../../enum/Regex'
import { useAppDispatch } from '../../../../stores/hooks'
import { inviteUserThunk } from '../../../../stores/Reducers/requestsReducer'
import LazyScrollSelectComponent from '../../../../components/LazyScrollSelect'
import useLazyScroll from '../../../../utils/hooks/useLazyScroll'

type Props = {
  closeModal: () => void
}

const InviteUserModal = ({ closeModal }: Props) => {
  const {
    control,
    formState: { errors, isSubmitting },
    handleSubmit,
    setError,
    watch
  } = useForm<InviteUserParams>({
    mode: 'onChange'
  })
  const dispatch = useAppDispatch()
  //group states
  const [listGroup, setListGroup] = useState<ListGroup[]>([])
  const [isFetchingMoreGroup, setIsFetchingMoreGroup] = useState<boolean>(false)
  const [groupPage, setGroupPage] = useState<number>(1)
  const [searchGroup, setSearchGroup] = useState<string>('')
  //roles states
  const [listRoles, setListRoles] = useState<IRoles[]>([])

  const {
    noMoreData,
    markAsMounted,
    fetchMoreFunc: fetchMoreGroupList
  } = useLazyScroll({
    currentPage: groupPage,
    onPageChange: (page) => setGroupPage(page),
    fetchDataFunc: () => fetchListGroup(),
    searchText: searchGroup.trim()
  })

  const usernameValue = watch('name')

  const fetchListGroup = async () => {
    setIsFetchingMoreGroup(true)
    const data: IGroupQuery = {
      'page-no': groupPage,
      'page-size': DEFAULT_PAGE_SIZE,
      name: searchGroup
    }
    try {
      const res = await getAuthorizedGroupList(data)
      const totalPages = Math.floor(res.total / DEFAULT_PAGE_SIZE)
      setListGroup((prev) =>
        groupPage === 1 ? res.data : prev.concat(res.data)
      )
      if (groupPage > Math.floor(totalPages)) {
        // if the current page is the last page
        noMoreData()
      }
      markAsMounted()
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    } finally {
      setIsFetchingMoreGroup(false)
    }
  }

  const fetchListRoles = async () => {
    try {
      const res = await getListRoles()
      setListRoles(res.data)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    }
  }

  const onInviteUserSubmit = async (params: InviteUserParams) => {
    const trimmedData: InviteUserParams = {
      ...params,
      email: params.email.trim(),
      name: params.name.trim()
    }
    const inviteUserAction = await dispatch(inviteUserThunk(trimmedData))
    if (inviteUserThunk.rejected.match(inviteUserAction)) {
      setError('email', {
        type: 'invite_error',
        message: inviteUserAction.payload as string
      })
      setError('name', {
        type: 'invite_error',
        message: inviteUserAction.payload as string
      })
    } else {
      toast.success(`Invitation has been sent to ${params.email}`)
      closeModal()
    }
  }
  useEffect(() => {
    fetchListRoles()
  }, [])

  return (
    <form
      onSubmit={handleSubmit(onInviteUserSubmit)}
      className="w-full flex flex-col gap-4 h-full"
    >
      <div className="flex flex-col sm:flex-row gap-4">
        <FormInputContainer
          label={'Name'}
          name={'name'}
          control={control}
          vertialAlign
          required
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_PREVENT_ONLY_WHITESPACES,
              message: 'Required'
            }
          }}
          maxCharactersText={`${usernameValue?.length ?? 0}/50 characters`}
          errors={errors}
          render={({ field }) => (
            <TextInputComponent
              maxLength={50}
              {...field}
              placeholder="Enter your name"
            />
          )}
        />
        <FormInputContainer
          label={'Email'}
          name={'email'}
          control={control}
          required
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_EMAIL,
              message: 'Your email is invalid'
            }
          }}
          vertialAlign
          errors={errors}
          render={({ field }) => (
            <TextInputComponent
              {...field}
              placeholder="Enter your email"
              type="email"
            />
          )}
        />
      </div>
      <div className="flex flex-col sm:flex-row gap-4">
        <FormInputContainer
          label={'Group'}
          name={'group_id'}
          control={control}
          vertialAlign
          required
          rules={{
            required: 'Required'
          }}
          errors={errors}
          render={({ field }) => (
            <LazyScrollSelectComponent
              field={field}
              options={listGroup.map((group) => ({
                label: group.name,
                value: group.id
              }))}
              placeholder="Select a group"
              searchInputPlaceholder="Search groups by name"
              fetchMoreData={fetchMoreGroupList}
              isFetchMore={isFetchingMoreGroup}
              onSearch={(e) => {
                setIsFetchingMoreGroup(true)
                setSearchGroup(e.target.value)
              }}
            />
          )}
        />
        <FormInputContainer
          label={'Role'}
          name={'role_id'}
          control={control}
          required
          rules={{
            required: 'Required'
          }}
          vertialAlign
          errors={errors}
          render={({ field }) => (
            <Select
              {...field}
              options={listRoles?.map((role) => ({
                label: role.name.replace('_', ' '),
                value: role.id
              }))}
              placeholder="Select a role"
              onChange={field.onChange}
              className="w-full min-h-[36px]"
            />
          )}
        />
      </div>
      <div className="flex gap-4 w-full bg-white py-4 sticky bottom-0 justify-end">
        <SecondaryButton className="sm:max-w-[200px]" onClick={closeModal}>
          Cancel
        </SecondaryButton>
        <PrimaryButton
          type="submit"
          className="sm:max-w-[200px]"
          isDisabled={isSubmitting}
          isLoading={isSubmitting}
        >
          Invite
        </PrimaryButton>
      </div>
    </form>
  )
}

export default InviteUserModal
