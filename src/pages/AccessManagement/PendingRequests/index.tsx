import { Select, Skeleton, Tooltip } from 'antd'
import NoDataComponent from '../../../components/NoDataComponent'
import { useEffect, useRef, useState } from 'react'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  addFilterStatus,
  approveRequestThunk,
  changeInvitationUserRole,
  clearAllStatusFilter,
  fetchListRequestThunk,
  inviteUserThunk,
  rejectRequestThunk,
  removeActionPending,
  removeFilterStatus,
  selectActionPending,
  selectFilterRequestStatus,
  selectListRequests,
  selectRequestLoading,
  selectRequestPage,
  selectRequestTotal,
  setActionPending,
  setRequestPage
} from '../../../stores/Reducers/requestsReducer'
import { USER_PENDING_STATUS } from '../../../enum/UserPendingStatus'
import { CheckCircleIcon, MinusCircleIcon } from '@heroicons/react/24/solid'
import { IPendingUsers, InviteUserParams } from '../../../models/auth'
import { LoaderIcon, toast } from 'react-hot-toast'
import ResendEmailIcon from '../../../assets/svgs/ResendEmailIcon'
import moment from 'moment'
import { IRoles } from '../../../models/roles'
import { getListRoles } from '../../../api/Roles'
import DropdownOptions from '../../../components/DropdownOptions'
import { IOption } from '../../../interfaces'
import RequestStatusBadge from './RequestStatusBadge'
import PaginationComponent from '../../../components/Pagination'
import { DEFAULT_PAGE_SIZE, ENV } from '../../../utils'
import ProfileIcon from '../../../assets/svgs/ProfileIcon'

const REQUEST_STATUS_OPTIONS: IOption[] = Object.values(
  USER_PENDING_STATUS
).map((status) => ({
  label: status,
  value: status
}))
const RequestManagementPage = () => {
  const dispatch = useAppDispatch()
  const listPendingRequests = useAppSelector(selectListRequests)
  const requestPage = useAppSelector(selectRequestPage)
  const totalRequests = useAppSelector(selectRequestTotal)
  const loading = useAppSelector(selectRequestLoading)
  const actionPendingItem = useAppSelector(selectActionPending)
  const filteredRequestStatus = useAppSelector(selectFilterRequestStatus)
  const [listRoles, setListRoles] = useState<IRoles[]>([])
  const isMounted = useRef<boolean>(false)

  const fetchListRoles = async () => {
    try {
      const res = await getListRoles()
      setListRoles(res.data)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    }
  }

  const handleFetchListRequests = async () => {
    await dispatch(fetchListRequestThunk())
    isMounted.current = true
  }

  const resendInvitation = async (params: IPendingUsers, role: string) => {
    dispatch(setActionPending(params.id))
    const data: InviteUserParams = {
      email: params.email,
      group_id: params.group_id,
      name: params.name,
      role_id: role
    }
    const resendInvitationAction = await dispatch(inviteUserThunk(data))
    if (inviteUserThunk.rejected.match(resendInvitationAction)) {
      toast.error(resendInvitationAction.payload as string)
    } else {
      toast.success(`Invitation has been resent to ${params.email}`)
    }
    dispatch(removeActionPending(params.id))
  }

  const handleSelectFilterStatus = (value: USER_PENDING_STATUS | '') => {
    if (value === '') {
      return filteredRequestStatus.length === REQUEST_STATUS_OPTIONS.length
        ? dispatch(clearAllStatusFilter())
        : dispatch(addFilterStatus(value))
    } else {
      return filteredRequestStatus.includes(value)
        ? dispatch(removeFilterStatus(value))
        : dispatch(addFilterStatus(value))
    }
  }

  const handleFetchRequestsWithFilter = () =>
    requestPage > 1
      ? dispatch(setRequestPage(1))
      : dispatch(fetchListRequestThunk())

  useEffect(() => {
    handleFetchListRequests()
  }, [requestPage])

  useEffect(() => {
    if (isMounted.current) {
      handleFetchRequestsWithFilter()
    }
  }, [filteredRequestStatus])

  useEffect(() => {
    fetchListRoles()
  }, [])

  return (
    <div className="flex flex-1 overflow-auto flex-col">
      <div className="min-h-[400px] w-full flex-1 overflow-auto">
        <div className="-my-2 overflow-x-auto overflow-y-scroll w-full h-full">
          <div className="min-w-full py-2 align-middle">
            <table className="min-w-full relative table-fixed table w-full divide-gray-300">
              <thead className="sticky z-10 top-2 w-full bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="py-3.5 px-4 text-left truncate text-sm font-semibold text-mainBlack w-[250px] max-w-[380px] min-w-[0px]"
                  >
                    User Details
                  </th>
                  <th
                    scope="col"
                    className="py-3.5 px-4 text-left text-sm font-semibold text-mainBlack w-[150px] max-w-[150px]"
                  >
                    <div className="flex w-full justify-center gap-2 items-center">
                      <p>Group</p>
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="p-3.5 text-center text-sm font-semibold text-mainBlack w-[150px] max-w-[150px]"
                  >
                    <p>Role</p>
                  </th>
                  <th
                    scope="col"
                    className="p-3.5 text-center text-sm font-semibold text-mainBlack w-[150px] max-w-[150px]"
                  >
                    <div className="w-fit mx-auto justify-center items-center flex gap-1">
                      <p>Status</p>
                      <DropdownOptions
                        label={''}
                        type="checkbox"
                        onlyButtonIcon={true}
                        selected={filteredRequestStatus}
                        options={REQUEST_STATUS_OPTIONS}
                        onSelect={(e) =>
                          handleSelectFilterStatus(e as USER_PENDING_STATUS)
                        }
                      />
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="p-3 text-center text-sm font-semibold text-mainBlack w-[150px] min-w-[150px] max-w-[150px]"
                  >
                    Last Updated
                  </th>
                  <th
                    scope="col"
                    className="p-3 text-center text-sm font-semibold text-mainBlack w-[80px] max-w-[80px]"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white w-full overflow-auto">
                {loading ? (
                  <>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                  </>
                ) : listPendingRequests.length > 0 ? (
                  listPendingRequests.map((user, index) => (
                    <tr key={index} className="even:bg-gray-50">
                      <td className="whitespace-nowrap truncate max-w-[380px] min-w-[0px] px-3.5 text-left py-4 text-sm font-medium text-mainBlack">
                        <div className="flex truncate max-w-full items-center gap-4">
                          <ProfileIcon height={40} width={40} />

                          <div className="flex truncate flex-col gap-2">
                            <Tooltip
                              title={user.name}
                              mouseEnterDelay={0.5}
                              className="text-sm truncate"
                            >
                              {user.name}
                            </Tooltip>
                            <Tooltip
                              title={user.email}
                              mouseEnterDelay={0.5}
                              className="text-sm truncate text-mainBlack"
                            >
                              {user.email}
                            </Tooltip>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap text-center max-w-[10vw] px-3 py-4 text-sm truncate text-mainBlack">
                        {user.group.name}
                      </td>

                      <td className="whitespace-nowrap text-mainBlack py-4 px-3.5 text-left text-sm ">
                        <Select
                          options={listRoles.map((role) => ({
                            label: role.name.replace('_', ' '),
                            value: role.id
                          }))}
                          disabled={user.status !== USER_PENDING_STATUS.INVITED}
                          onChange={(value) =>
                            dispatch(
                              changeInvitationUserRole({
                                request_id: user.id,
                                role_id: value
                              })
                            )
                          }
                          className="w-full"
                          defaultValue={user.role.id}
                        />
                      </td>
                      <td className="whitespace-nowrap p-3 text-sm text-center">
                        <RequestStatusBadge pendingRequest={user} />
                      </td>
                      <td className="whitespace-nowrap p-3 ">
                        <p className="text-sm text-center text-mainBlack">
                          {moment(user.updated_at).format('MMMM Do YYYY')}
                        </p>
                        <p className="text-sm text-center text-mainBlack">
                          {moment(user.updated_at).format('HH:mm:ss')}
                        </p>
                      </td>
                      <td className="whitespace-nowrap py-4 text-sm text-center text-mainBlack">
                        {actionPendingItem.includes(user.id) ? (
                          <div className="flex w-full justify-center">
                            <LoaderIcon />
                          </div>
                        ) : (
                          <div className="w-full flex justify-center gap-2 items-center">
                            {user.status === USER_PENDING_STATUS.PENDING && (
                              <>
                                <Tooltip title="Approve" mouseEnterDelay={0.5}>
                                  <CheckCircleIcon
                                    height={20}
                                    width={20}
                                    fill="#79DA4B"
                                    onClick={() =>
                                      dispatch(approveRequestThunk(user.id))
                                    }
                                    cursor={'pointer'}
                                  />
                                </Tooltip>
                                <Tooltip title="Reject" mouseEnterDelay={0.5}>
                                  <MinusCircleIcon
                                    height={20}
                                    width={20}
                                    onClick={() =>
                                      dispatch(rejectRequestThunk(user.id))
                                    }
                                    fill="red"
                                    cursor={'pointer'}
                                  />
                                </Tooltip>
                              </>
                            )}
                            {user.status === USER_PENDING_STATUS.INVITED && (
                              <Tooltip
                                title="Resend invitation"
                                mouseEnterDelay={0.5}
                              >
                                <ResendEmailIcon
                                  cursor={'pointer'}
                                  height={25}
                                  width={25}
                                  onClick={() =>
                                    resendInvitation(user, user.role.id)
                                  }
                                />
                              </Tooltip>
                            )}
                          </div>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr className="h-[15vw]">
                    <td className="min-h-[300px]" colSpan={6}>
                      <NoDataComponent />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <PaginationComponent
        totalItems={totalRequests}
        onPageChange={(page) => dispatch(setRequestPage(page))}
        currentPage={requestPage}
        pageSize={DEFAULT_PAGE_SIZE}
      />
    </div>
  )
}

export default RequestManagementPage
