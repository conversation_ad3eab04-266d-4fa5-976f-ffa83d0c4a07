import { Skeleton } from 'antd'
import TextInputComponent from '../../../components/TextInputComponent'
import NoDataComponent from '../../../components/NoDataComponent'
import { useEffect, useRef, useState } from 'react'
import { ICurrentUser, IUserQuery } from '../../../models/users'
import { DEFAULT_PAGE_SIZE, getErrorMessage } from '../../../utils'
import { getUsersList } from '../../../api/Users'
import { useDebounce } from '../../../utils/hooks/useDebounce'
import { AxiosError } from 'axios'
import UserTableItem from './user-table-item'
import PaginationComponent from '../../../components/Pagination'
import SearchIcon from '../../../assets/svgs/SearchIcon'
import { useAppSelector } from '../../../stores/hooks'
import { selectCurrentUser } from '../../../stores/Reducers/usersReducer'

const UserManagementPage = () => {
  const userProfile = useAppSelector(selectCurrentUser)
  const [loading, setLoading] = useState<boolean>(true)
  const [userSearchText, setUserSearchText] = useState<string>('')
  const [listUsers, setListUsers] = useState<ICurrentUser[]>([])
  const [totalUsers, setTotalUsers] = useState<number>(0)
  const [userPage, setUserPage] = useState<number>(1)
  const [errorMsg, setErrorMsg] = useState<string>('')
  const abortController = useRef<AbortController>()
  const handleGetListUsers = async () => {
    if (!userProfile?.is_super_admin) {
      return
    }

    abortController.current = new AbortController()
    setLoading(true)
    setErrorMsg('')
    const data: IUserQuery = {
      email: userSearchText,
      'page-no': userPage,
      'page-size': DEFAULT_PAGE_SIZE
    }
    try {
      const res = await getUsersList(data, abortController.current)
      setTotalUsers(res.total)
      setListUsers(res.data)
      isMounted.current = true
    } catch (error) {
      const axiosError = error as AxiosError
      if (axiosError.code === AxiosError.ERR_CANCELED) {
        return
      }
      setErrorMsg(getErrorMessage(error))
    } finally {
      setLoading(false)
    }
  }

  const refetchListUser = () => {
    if (listUsers.length === 1 && userPage > 1) {
      setUserPage((prev) => prev - 1)
      return
    }
    handleGetListUsers()
  }

  const { isMounted } = useDebounce({
    func: () => (userPage > 1 ? setUserPage(1) : handleGetListUsers()),
    searchText: userSearchText
  })

  useEffect(() => {
    handleGetListUsers()
    return () => {
      abortController.current?.abort()
    }
  }, [userPage])

  if (!userProfile?.is_super_admin) {
    return <NoDataComponent text="Unauthorized personnel" />
  }

  if (errorMsg) {
    return <NoDataComponent text={errorMsg} />
  }

  return (
    <div className="flex flex-1 gap-2 overflow-auto flex-col h-full">
      <div className="sm:max-w-[40%]">
        <TextInputComponent
          onChange={(e) => setUserSearchText(e.target.value)}
          placeholder="Search users by email"
          endfixIcon={<SearchIcon />}
        />
      </div>
      <div className="w-full flex-1 overflow-auto">
        <div className="-my-2 overflow-x-auto w-full h-full">
          <div className="min-w-full py-2 align-middle">
            <table className="min-w-full relative table w-full divide-gray-300">
              <thead className="sticky top-2 w-full bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="py-3.5 px-4 w-[200px] text-center text-sm font-semibold text-mainBlack"
                  >
                    Name
                  </th>

                  <th
                    scope="col"
                    className="px-3 py-3.5 text-center text-sm font-semibold text-mainBlack"
                  >
                    Groups
                  </th>

                  <th
                    scope="col"
                    className="py-3.5 px-3 text-center text-sm font-semibold text-mainBlack"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white w-full overflow-auto">
                {loading ? (
                  <>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={4}>
                        <Skeleton active />
                      </td>
                    </tr>
                  </>
                ) : listUsers?.length > 0 ? (
                  listUsers?.map((user) => (
                    <UserTableItem
                      refetchListUser={refetchListUser}
                      key={user.id}
                      user={user}
                    />
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="h-[300px]">
                      <NoDataComponent />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <PaginationComponent
        totalItems={totalUsers}
        currentPage={userPage}
        onPageChange={setUserPage}
        pageSize={DEFAULT_PAGE_SIZE}
      />
    </div>
  )
}

export default UserManagementPage
