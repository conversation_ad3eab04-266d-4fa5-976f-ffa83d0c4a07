import { ICurrentUser } from '../../../models/users'
import { Tooltip } from 'antd'
import TrashIcon from '../../../assets/svgs/TrashIcon'
import { useState } from 'react'
import ConfirmModal from '../../../components/ConfirmModal'
import { removeUser } from '../../../api/Users'
import { toast } from 'react-hot-toast'
import { getErrorMessage } from '../../../utils'
import ProfileIcon from '../../../assets/svgs/ProfileIcon'

type Props = {
  user: ICurrentUser
  refetchListUser: VoidFunction
}

const UserTableItem = ({ user, refetchListUser }: Props) => {
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false)
  const [isProcessing, setIsProcessing] = useState<boolean>(false)
  const onConfirmRemoveUser = async () => {
    setIsProcessing(true)
    try {
      await removeUser(user.id)
      toast.success(`User <${user.email}> has been removed`)
      setOpenConfirmDelete(false)
      refetchListUser()
    } catch (error) {
      toast.error(getErrorMessage(error))
    } finally {
      setIsProcessing(false)
    }
  }
  return (
    <>
      <tr key={user.id} className="even:bg-gray-50">
        <td className="whitespace-nowrap min-w-[250px] max-w-[20vw]  p-4 text-sm font-medium text-mainBlack">
          <div className="flex truncate max-w-full items-center gap-4">
            <ProfileIcon height={40} width={40} />
            <div className="flex truncate flex-col gap-2">
              <Tooltip
                title={user.name}
                mouseEnterDelay={0.5}
                className="text-sm truncate"
              >
                {user.name || user.email.split('@')[0]}
              </Tooltip>
              <Tooltip
                title={user.email}
                mouseEnterDelay={0.5}
                className="text-sm truncate text-mainBlack"
              >
                {user.email}
              </Tooltip>
            </div>
          </div>
        </td>

        <td className="whitespace-nowrap px-3 min-w-[150px] max-w-[15vw] truncate py-4 text-center text-sm ">
          <Tooltip
            title={(user.groups ?? []).map((group) => group.name).join(', ')}
            mouseEnterDelay={0.5}
            className="text-mainBlack truncate cursor-pointer hover:underline"
          >
            {user.groups?.length === 0
              ? 'No information'
              : user.groups?.map((group) => group.name).join(', ')}
          </Tooltip>
        </td>
        <td className="whitespace-nowrap py-4 px-3 text-center text-sm font-medium ">
          {!user.is_super_admin && (
            <Tooltip title="Remove user">
              <TrashIcon
                onClick={() => setOpenConfirmDelete(true)}
                cursor={'pointer'}
                className="hover:fill-primary"
              />
            </Tooltip>
          )}
        </td>
      </tr>
      <ConfirmModal
        onConfirm={onConfirmRemoveUser}
        disable={isProcessing}
        text={'This user will not be able to access the platform.'}
        openState={[openConfirmDelete, setOpenConfirmDelete]}
        title={`Remove user <${user.email}>`}
      />
    </>
  )
}

export default UserTableItem
