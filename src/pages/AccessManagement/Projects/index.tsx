import SearchIcon from '../../../assets/svgs/SearchIcon'
import { DEFAULT_PAGE_SIZE } from '../../../utils'
import { useRef, useState } from 'react'
import CustomModal from '../../../components/Modal'
import ProjectDetailModal from './ProjectDetailModal'
import { IProject } from '../../../models/projects'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  selectProjectPage,
  selectTotalProject,
  setPage,
  setProjectSearchText,
  setProjectUsers
} from '../../../stores/Reducers/projectsReducer'
import TextInputComponent from '../../../components/TextInputComponent'
import ListProjectTable from './ListProjectTable'
import PaginationComponent from '../../../components/Pagination'
import AssignProjectUserTable from './AssignProjectUserTable'

const ProjectsManagement = () => {
  const dispatch = useAppDispatch()
  const totalProject = useAppSelector(selectTotalProject)
  const [openEditProjectModal, setOpenEditProjectModal] =
    useState<boolean>(false)
  const page = useAppSelector(selectProjectPage)
  const [isProjectModalDirty, setIsProjectModalDirty] = useState<boolean>(false) // check if theres any modification in the modal
  const [addProjectUserModal, setAddProjectUserModal] = useState<boolean>(false)
  const selectedProject = useRef<IProject>()

  const triggerOpenEditProjectModal = (project: IProject) => {
    setOpenEditProjectModal(true)
    selectedProject.current = project
  }

  const triggerOpenAddUserProjectModal = (project: IProject) => {
    setAddProjectUserModal(true)
    dispatch(setProjectUsers(project.users))
    selectedProject.current = project
  }

  return (
    <div className="flex flex-1 overflow-auto flex-col">
      <div className="sm:w-1/2 mb-4">
        <TextInputComponent
          prefixIcon={<SearchIcon />}
          onChange={(e) => dispatch(setProjectSearchText(e.target.value))}
          placeholder="Search..."
        />
      </div>

      <ListProjectTable
        openAddUserProjectModal={triggerOpenAddUserProjectModal}
        openEditProjectModal={triggerOpenEditProjectModal}
      />
      <PaginationComponent
        totalItems={totalProject}
        onPageChange={(page) => dispatch(setPage(page))}
        pageSize={DEFAULT_PAGE_SIZE}
        currentPage={page}
      />

      <CustomModal
        isModalDirty={isProjectModalDirty}
        openState={[openEditProjectModal, setOpenEditProjectModal]}
        title={'Edit Project'}
      >
        <ProjectDetailModal
          isProjectModalDirty={isProjectModalDirty}
          setIsProjectModalDirty={setIsProjectModalDirty}
          projectDetail={selectedProject.current}
          onClose={() => setOpenEditProjectModal(false)}
        />
      </CustomModal>
      <CustomModal
        openState={[addProjectUserModal, setAddProjectUserModal]}
        title={`Project ${selectedProject?.current?.name}'s Users`}
        className="md:!min-w-[60vw]"
      >
        <AssignProjectUserTable
          closeModal={() => setAddProjectUserModal(false)}
          project_id={selectedProject.current?.id}
        />
      </CustomModal>
    </div>
  )
}

export default ProjectsManagement
