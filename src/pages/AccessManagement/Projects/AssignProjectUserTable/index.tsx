import { useCallback, useEffect, useRef, useState } from 'react'
import { IModifyUserParams, IProjectUsers } from '../../../../models/projects'
import NoDataComponent from '../../../../components/NoDataComponent'
import { getListRoles } from '../../../../api/Roles'
import { IRoles } from '../../../../models/roles'
import { Select, Tooltip } from 'antd'
import TextInputComponent from '../../../../components/TextInputComponent'
import SearchIcon from '../../../../assets/svgs/SearchIcon'
import { LoaderIcon } from 'react-hot-toast'
import { MinusCircleIcon } from '@heroicons/react/20/solid'
import {
  editProjectUserRoleThunk,
  removeProjectUserThunk,
  selectProjectUsers
} from '../../../../stores/Reducers/projectsReducer'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import { ENV, classNames } from '../../../../utils'
import { selectCurrentUser } from '../../../../stores/Reducers/usersReducer'
import CustomModal from '../../../../components/Modal'
import AddProjectMemberModal from '../AddProjectUserModal'
import PrimaryButton from '../../../../components/PrimaryButtons'
import ConfirmModal from '../../../../components/ConfirmModal'
import ProfileIcon from '../../../../assets/svgs/ProfileIcon'

type Props = {
  project_id?: string
  closeModal: () => void
}

const AssignProjectUserTable = ({ project_id = '', closeModal }: Props) => {
  const currentUser = useAppSelector(selectCurrentUser)
  const [listRoles, setListRoles] = useState<IRoles[]>([])
  const [searchText, setSearchText] = useState<string>('')
  const [usersBeingRemoved, setUsersBeingRemoved] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState<string[]>([])
  const [openAddUserModal, setOpenAddUserModal] = useState<boolean>(false)
  const [openConfirmRemoveModal, setOpenConfirmRemoveModal] =
    useState<boolean>(false)

  const selectedUsers = useAppSelector(selectProjectUsers)
  const removedUserRef = useRef<IProjectUsers>()
  const dispatch = useAppDispatch()

  const fetchListRoles = async () => {
    try {
      const res = await getListRoles({ layer: 'project' })
      setListRoles(res.data)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    }
  }

  const getDirectRole = useCallback(
    (user: IProjectUsers) =>
      user?.roles?.find((role) => role.membership_type === 'direct'),
    []
  )

  const renderUserRole = useCallback((user: IProjectUsers) => {
    const directRole = getDirectRole(user)
    if (directRole) {
      return directRole.id
    }
    return user.roles?.[0].id
  }, [])

  const isUserAdmin = useCallback(
    (user: IProjectUsers): boolean =>
      Boolean(
        user.roles?.find((role) =>
          role.name.toLocaleLowerCase().includes('admin')
        )
      ),
    []
  )

  const onSelectUserRole = useCallback(
    async (user: IProjectUsers, role_id: string) => {
      setIsProcessing((prev) => [...prev, user.id])
      const data: IModifyUserParams = {
        project_id: project_id,
        data: {
          user_id: user.id,
          role_id: role_id
        }
      }
      await dispatch(editProjectUserRoleThunk(data))
      setIsProcessing((prev) =>
        prev.filter((processingUser) => processingUser !== user.id)
      )
    },
    []
  )

  const triggerRemoveUser = useCallback((user: IProjectUsers) => {
    removedUserRef.current = user
    setOpenConfirmRemoveModal(true)
  }, [])

  const onConfirmRemoveUser = useCallback(async () => {
    const data: IModifyUserParams = {
      project_id: project_id,
      data: {
        user_id: removedUserRef.current?.id ?? ''
      }
    }
    setUsersBeingRemoved((prev) => [...prev, removedUserRef.current?.id ?? ''])
    const removeAction = await dispatch(removeProjectUserThunk(data))
    if (removeProjectUserThunk.fulfilled.match(removeAction)) {
      setOpenConfirmRemoveModal(false)
    }
    setUsersBeingRemoved((prev) =>
      prev.filter((removedUser) => removedUser !== removedUserRef.current?.id)
    )
  }, [])

  const handleOpenAddUserModal = () => {
    setOpenAddUserModal(true)
  }

  useEffect(() => {
    fetchListRoles()
  }, [])

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-1 items-center">
        <p className="font-semibold mb-0 text-black text-sm">Added Users</p>
        <span className="h-6 w-6 ml-1 flex justify-center items-center font-normal text-sm bg-[#F7F3FF] rounded-full">
          {selectedUsers?.length}
        </span>
      </div>
      <TextInputComponent
        prefixIcon={<SearchIcon />}
        placeholder="Search users by their email..."
        onChange={(e) => setSearchText(e.target.value.trim())}
      />
      <div className="min-h-fit max-h-[300px] w-full flex-1 relative overflow-auto">
        <table className="min-w-full overflow-auto table relative w-full divide-gray-300">
          <thead className="sticky z-10 top-0 w-full bg-gray-50">
            <tr>
              <th
                scope="col"
                className="py-3.5 px-4 w-[70px] text-center text-sm font-semibold text-mainBlack"
              >
                No.
              </th>
              <th
                scope="col"
                className="px-4 py-3.5 text-left text-sm font-semibold text-mainBlack"
              >
                User Details
              </th>

              <th
                scope="col"
                className="p-3.5 text-left text-sm font-semibold text-mainBlack"
              >
                <div className="flex w-full gap-2 justify-center items-center">
                  <p>Role</p>
                </div>
              </th>
              <th
                scope="col"
                className="px-4 py-3.5 text-center text-sm font-semibold text-mainBlack"
              >
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white w-full overflow-auto">
            {selectedUsers.filter((user) =>
              user.email.toLocaleLowerCase().includes(searchText)
            )?.length > 0 ? (
              selectedUsers
                ?.filter((user) =>
                  user.email.toLocaleLowerCase().includes(searchText)
                )
                .map((user: IProjectUsers, index) => (
                  <tr key={user.id} className="even:bg-gray-50">
                    <td className="whitespace-nowrap px-4 pl-0 text-center py-4 text-sm font-medium text-mainBlack">
                      {index + 1}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm  text-mainBlack">
                      <div className="flex w-full items-center gap-4">
                        <ProfileIcon height={40} width={40} />
                        <div className="flex flex-col gap-2">
                          <p>{user.name}</p>
                          <p className="text-mainBlack text-xs">{user.email}</p>
                        </div>
                      </div>
                    </td>

                    <td className="whitespace-nowrap text-mainBlack p-4 text-center text-sm ">
                      <Tooltip
                        title={
                          getDirectRole(user)
                            ? ''
                            : "This user's role is inherited from their group"
                        }
                        mouseEnterDelay={0.5}
                      >
                        <Select
                          options={listRoles.map((role) => ({
                            label: role.name.replace('_', ' '),
                            value: role.id
                          }))}
                          loading={isProcessing.includes(user.id)}
                          disabled={
                            // cant be modified if:
                            Boolean(!getDirectRole(user)) || //the role is not direct
                            isProcessing.includes(user.id) || // the action is processing
                            currentUser?.email === user.email || // the user is yourself
                            isUserAdmin(user) // if ths user is admin
                          }
                          className="w-[200px]"
                          value={
                            getDirectRole(user)
                              ? renderUserRole(user)
                              : user.roles?.[0].name
                          }
                          onChange={(value) => onSelectUserRole(user, value)}
                        />
                      </Tooltip>
                    </td>
                    <td className="whitespace-nowrap text-mainBlack p-4 text-center text-sm ">
                      {usersBeingRemoved.includes(user.id) ? (
                        <LoaderIcon />
                      ) : (
                        <Tooltip title="Remove this user" mouseEnterDelay={0.5}>
                          {getDirectRole(user) && ( // only Diriect user can be removed
                            <MinusCircleIcon
                              height={20}
                              width={20}
                              fill="red"
                              onClick={() => triggerRemoveUser(user)}
                              className={classNames(
                                (isUserAdmin(user) ||
                                  currentUser?.email === user.email) &&
                                  'hidden'
                              )}
                              cursor={'pointer'}
                            />
                          )}
                        </Tooltip>
                      )}
                    </td>
                  </tr>
                ))
            ) : (
              <tr className="pt-6 h-[100px]">
                <td colSpan={5}>
                  <NoDataComponent text="No users selected" />
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div className="flex bg-white items-center sticky bottom-0 py-4 justify-between">
        <p
          onClick={handleOpenAddUserModal}
          className="text-sm font-medium text-primary hover:underline cursor-pointer"
        >
          Add a member
        </p>
        <PrimaryButton
          onClick={closeModal}
          className="mt-auto max-w-[200px] ml-auto"
        >
          {' '}
          Close
        </PrimaryButton>
      </div>

      <ConfirmModal
        onConfirm={onConfirmRemoveUser}
        text={`User ${removedUserRef.current?.name} will not be able to view this project's resources.`}
        openState={[openConfirmRemoveModal, setOpenConfirmRemoveModal]}
        title={`Remove user ${removedUserRef?.current?.name}`}
      />
      {/* Add Group User Modal */}
      <CustomModal
        title=""
        className="!p-0 lg:!min-w-[40vw]"
        openState={[openAddUserModal, setOpenAddUserModal]}
      >
        <AddProjectMemberModal
          closeModal={() => setOpenAddUserModal(false)}
          selectedProject={project_id}
          memberRole={
            listRoles.find((role) => role.name.toLowerCase() === 'member')?.id
          }
        />
      </CustomModal>
    </div>
  )
}

export default AssignProjectUserTable
