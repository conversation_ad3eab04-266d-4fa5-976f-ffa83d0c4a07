import { Skeleton, Tooltip } from 'antd'
import { classNames } from '../../../../utils'
import moment from 'moment'
import EditIcon from '../../../../assets/svgs/EditIcon'
import NoDataComponent from '../../../../components/NoDataComponent'
import { IProject } from '../../../../models/projects'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  deleteProjectThunk,
  fetchListProjectThunk,
  selectListProjects,
  selectProjectPage,
  selectProjectSearchText,
  selectProjectsLoading,
  selectProjectsSortOrder,
  setPage,
  toggleProjectOrder
} from '../../../../stores/Reducers/projectsReducer'
import TrashIcon from '../../../../assets/svgs/TrashIcon'
import { IOption } from '../../../../interfaces'
import DropdownOptions from '../../../../components/DropdownOptions'
import { useEffect, useRef, useState } from 'react'
import { useDebounce } from '../../../../utils/hooks/useDebounce'
import ConfirmModal from '../../../../components/ConfirmModal'
import toast from 'react-hot-toast'
import ManageUsersIcon from '../../../../assets/svgs/ManageUsersIcon'

type Props = {
  openEditProjectModal: (project: IProject) => void
  openAddUserProjectModal: (project: IProject) => void
}
const PROJECT_NAME_OPTIONS: IOption[] = [
  { value: 'name-asc', label: 'Project name (A-Z)' },
  { value: 'name-desc', label: 'Project name (Z-A)' }
]

const PROJECT_CREATED_DATE_OPTIONS: IOption[] = [
  { value: 'created_at-desc', label: 'Newest to oldest' },
  { value: 'created_at-asc', label: 'Oldest to latest' }
]
const ListProjectTable = ({
  openEditProjectModal,
  openAddUserProjectModal
}: Props) => {
  const loading = useAppSelector(selectProjectsLoading)
  const dispatch = useAppDispatch()
  const orderBy = useAppSelector(selectProjectsSortOrder)
  const searchText = useAppSelector(selectProjectSearchText)
  const page = useAppSelector(selectProjectPage)
  const listProject = useAppSelector(selectListProjects)
  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false)
  const [isDeleting, setIsDeleting] = useState<boolean>(false)
  const deletedProject = useRef<IProject>()

  const fetchListProjects = async () => {
    await dispatch(fetchListProjectThunk())
    isMounted.current = true
  }

  const fetchListProjectsWithSearchSort = () =>
    page > 1 ? dispatch(setPage(1)) : fetchListProjects()

  const { isMounted } = useDebounce({
    func: fetchListProjectsWithSearchSort, //debounce search with text
    searchText: searchText
  })

  const onConfirmDelete = async () => {
    setIsDeleting(true)
    const deleteAction = await dispatch(
      deleteProjectThunk(deletedProject.current?.id ?? '')
    )
    if (deleteProjectThunk.rejected.match(deleteAction)) {
      setIsDeleting(false)
      return
    }

    if (listProject.length === 1 && Number(page) > 1) {
      dispatch(setPage(1))
    }
    toast.success('Project is deleted!')
    setOpenDeleteModal(false)
    setIsDeleting(false)
  }

  useEffect(() => {
    if (isMounted.current) {
      fetchListProjectsWithSearchSort()
    }
  }, [orderBy])

  useEffect(() => {
    fetchListProjects()
  }, [page])

  return (
    <div className="min-h-[400px] w-full flex-1 overflow-auto">
      <div className="-my-2 overflow-x-auto w-full h-full">
        <div className="min-w-full py-2 align-middle">
          <table className="min-w-full relative table w-full divide-gray-300">
            <thead className={classNames('sticky top-2 w-full bg-gray-50')}>
              <tr>
                <th
                  scope="col"
                  className="py-3.5 px-4 w-[200px] text-center text-sm font-semibold text-mainBlack"
                >
                  <div className="flex w-fit mx-auto justify-center gap-2 items-center">
                    <p>Project</p>
                    <DropdownOptions
                      onlyButtonIcon
                      label={''}
                      selected={orderBy}
                      options={PROJECT_NAME_OPTIONS}
                      onSelect={(value: string) =>
                        dispatch(toggleProjectOrder(value))
                      }
                    />
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-4 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  Group
                </th>
                <th
                  scope="col"
                  className="px-4 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  Description
                </th>
                <th
                  scope="col"
                  className="px-3 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  <div className="flex w-full justify-center gap-2 items-center">
                    <p>Users</p>
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-3 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  <div className="flex mx-auto w-fit justify-center gap-2 items-center">
                    <p>Created Date</p>
                    <DropdownOptions
                      onlyButtonIcon
                      label={''}
                      selected={orderBy}
                      options={PROJECT_CREATED_DATE_OPTIONS}
                      onSelect={(value: string) =>
                        dispatch(toggleProjectOrder(value))
                      }
                    />
                  </div>
                </th>
                <th
                  scope="col"
                  className="py-3.5 px-3 text-center text-sm font-semibold text-mainBlack"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white w-full overflow-auto">
              {loading ? (
                <>
                  <tr>
                    <td colSpan={4}>
                      <Skeleton active />
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={4}>
                      <Skeleton active />
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={4}>
                      <Skeleton active />
                    </td>
                  </tr>
                </>
              ) : listProject?.length > 0 ? (
                listProject?.map((project) => (
                  <tr key={project?.id} className="even:bg-gray-50">
                    <td className="whitespace-nowrap max-w-[25vw] truncate px-4 text-center py-4 text-sm font-medium text-mainBlack">
                      <Tooltip
                        title={project?.name}
                        mouseEnterDelay={0.5}
                        className="text-sm truncate block font-medium leading-6"
                      >
                        {project?.name}
                      </Tooltip>
                    </td>
                    <td className="whitespace-nowrap max-w-[10vw] text-center px-3 py-4 text-sm truncate text-mainBlack">
                      {project?.group?.name}
                    </td>
                    <td className="whitespace-nowrap max-w-[10vw] text-center px-3 py-4 text-sm truncate text-mainBlack">
                      <Tooltip
                        title={project?.description}
                        mouseEnterDelay={0.5}
                        className="text-sm truncate block font-medium leading-6 text-mainBlack"
                      >
                        {project?.description}
                      </Tooltip>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-center text-sm ">
                      <p
                        onClick={() => openAddUserProjectModal(project)}
                        className="text-mainBlack hover:cursor-pointer hover:underline"
                      >
                        {project?.users?.length ?? 0} users
                      </p>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-center text-sm ">
                      <p className="text-mainBlack hover:cursor-pointer hover:underline">
                        {moment(project?.created_at).format(
                          'MMMM Do YYYY, HH:mm:ss '
                        )}
                      </p>
                    </td>
                    <td className="whitespace-nowrap py-4 px-3 text-center text-sm font-medium ">
                      <div className="w-full flex justify-center gap-3 items-center">
                        <EditIcon
                          onClick={() => {
                            openEditProjectModal(project)
                          }}
                          className="cursor-pointer hover:fill-primary"
                        />
                        <TrashIcon
                          cursor={'pointer'}
                          onClick={() => {
                            deletedProject.current = project
                            setOpenDeleteModal(true)
                          }}
                          className="hover:fill-primary"
                        />
                        <ManageUsersIcon
                          width={24}
                          height={24}
                          onClick={() => openAddUserProjectModal(project)}
                          cursor={'pointer'}
                          className="fill-gray-500 hover:fill-primary"
                        />
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="h-[300px]">
                    <NoDataComponent />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      <ConfirmModal
        onConfirm={onConfirmDelete}
        text={
          <p className="inline-flex gap-1">
            Are you sure you want to delete{' '}
            <span className="font-bold text-sm">
              {`${deletedProject.current?.name} `}{' '}
            </span>
            project?
          </p>
        }
        openState={[openDeleteModal, setOpenDeleteModal]}
        title={'Delete this project?'}
        disable={isDeleting}
      />
    </div>
  )
}

export default ListProjectTable
