import { useForm } from 'react-hook-form'
import { Select } from 'antd'
import { useEffect, useState } from 'react'
import SecondaryButton from '../../../../components/SecondaryButtons'
import PrimaryButton from '../../../../components/PrimaryButtons'
import {
  EditProjectParams,
  IProject,
  ProjectDetailFormInput
} from '../../../../models/projects'
import { toast } from 'react-hot-toast'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  createProjectThunk,
  editProjectThunk
} from '../../../../stores/Reducers/projectsReducer'
import TextInputComponent from '../../../../components/TextInputComponent'
import FormInputContainer from '../../../../components/FormInputComponent'
import ConfirmModal from '../../../../components/ConfirmModal'
import {
  fetchListGroupThunk,
  selectListGroup,
  selectListGroupLoading
} from '../../../../stores/Reducers/groupReducers'
import { REGEX_PREVENT_ONLY_WHITESPACES } from '../../../../enum/Regex'

type Props = {
  projectDetail?: IProject
  onClose: () => void
  setIsProjectModalDirty: React.Dispatch<React.SetStateAction<boolean>>
  isProjectModalDirty: boolean // check if there's any modification in the modal
}

const ProjectDetailModal = ({
  projectDetail,
  onClose,
  setIsProjectModalDirty,
  isProjectModalDirty
}: Props) => {
  const listGroup = useAppSelector(selectListGroup)
  const listGroupLoading = useAppSelector(selectListGroupLoading)
  const dispatch = useAppDispatch()
  const [openConfirmCancel, setOpenConfirmCancel] = useState<boolean>(false) //list of removed users to pass in the edit project api

  const {
    formState: { errors, isValid, isSubmitting, isDirty },
    setFocus,
    handleSubmit,
    control,
    watch
  } = useForm<ProjectDetailFormInput>({
    mode: 'onChange',
    defaultValues: projectDetail
      ? {
          name: projectDetail.name,
          description: projectDetail.description,
          group_id: projectDetail?.group?.id
        }
      : {
          name: '',
          description: '',
          group_id: undefined
        }
  })

  const descriptionFieldValue = watch('description')
  const nameFieldValue = watch('name')
  const handleCreateProject = async (data: ProjectDetailFormInput) => {
    const trimmedData: ProjectDetailFormInput = {
      ...data,
      name: data.name.trim(),
      description: data.description.trim()
    }
    const createAction = await dispatch(createProjectThunk(trimmedData))
    if (createProjectThunk.rejected.match(createAction)) {
      const errorMsg = createAction.payload as string
      if (errorMsg.includes('duplicated')) {
        toast.error("Duplicated Project's name")
        return
      }
      toast.error(errorMsg)
    } else {
      toast.success('A new project is successfully created')
      onClose()
    }
  }

  const handleEditProject = async (formData: ProjectDetailFormInput) => {
    const data: EditProjectParams = {
      id: projectDetail?.id ?? '',
      data: {
        ...formData,
        name: formData.name.trim(),
        description: formData.description.trim()
      }
    }
    const editAction = await dispatch(editProjectThunk(data))
    if (editProjectThunk.rejected.match(editAction)) {
      if ((editAction.payload as string).includes('project_name_unique_idx')) {
        toast.error("Duplicated Project's name")
      }
    } else {
      toast.success('Your changes are saved successfully')
      onClose()
    }
  }

  const onSubmit = async (data: ProjectDetailFormInput) =>
    projectDetail
      ? await handleEditProject(data)
      : await handleCreateProject(data)

  const onCancelModal = () => {
    if (isDirty) {
      setOpenConfirmCancel(true)
    } else {
      onClose()
    }
  }

  useEffect(() => {
    dispatch(fetchListGroupThunk())
    setFocus('name') //auto focus the Project name field when mounted
  }, [])

  useEffect(() => {
    setIsProjectModalDirty(isDirty)
  }, [isDirty])

  return (
    <>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="w-full relative h-full flex gap-2 flex-col"
      >
        <div className="w-full overflow-auto gap-2 flex-col flex flex-1">
          <div className="flex gap-4 flex-col sm:flex-row">
            <FormInputContainer<ProjectDetailFormInput>
              control={control}
              errors={errors}
              maxCharactersText={`${
                nameFieldValue?.length ?? 0
              }/100 characters`}
              vertialAlign
              label={'Project Name'}
              required
              name={'name'}
              rules={{
                required: 'Required',
                pattern: {
                  value: REGEX_PREVENT_ONLY_WHITESPACES,
                  message: 'Required'
                }
              }}
              render={({ field }) => (
                <TextInputComponent
                  onChange={field.onChange}
                  defaultValue={nameFieldValue}
                  maxLength={100}
                  placeholder="Enter Project name"
                />
              )}
            />
            <FormInputContainer<ProjectDetailFormInput>
              control={control}
              errors={errors}
              vertialAlign
              label={'Group'}
              required
              name={'group_id'}
              rules={{
                required: 'Required'
              }}
              render={({ field }) => (
                <Select
                  {...(projectDetail ? {} : { field })}
                  options={listGroup.map((group) => ({
                    label: group.name,
                    value: group?.id
                  }))}
                  loading={listGroupLoading}
                  defaultValue={projectDetail?.group.name}
                  disabled={Boolean(projectDetail)}
                  onChange={(e) => {
                    field.onChange(e)
                  }}
                  className="w-full min-h-9"
                  placeholder="Select a group"
                />
              )}
            />
          </div>

          <FormInputContainer<ProjectDetailFormInput>
            errors={errors}
            vertialAlign
            label={'Description'}
            name={'description'}
            control={control}
            render={({ field }) => (
              <>
                <textarea
                  placeholder="Enter Project description"
                  rows={4}
                  onChange={field.onChange}
                  defaultValue={descriptionFieldValue}
                  maxLength={250}
                  className="block w-full bg-[#F3F4F6] rounded-md border-0 py-1.5 text-mainBlack shadow-sm ring-0 focus:ring-0 placeholder:text-gray-400 sm:text-sm sm:leading-6"
                />
                <p className="text-end w-full text-gray-400 text-xs font-semibold">
                  {' '}
                  {descriptionFieldValue?.length ?? 0}/250 characters
                </p>
              </>
            )}
          />
        </div>

        <div className="flex z-20 gap-2 sticky bottom-0 justify-end w-full bg-white py-4 min-h-[40px]">
          <SecondaryButton onClick={onCancelModal} className="max-w-[250px]">
            Cancel
          </SecondaryButton>
          <PrimaryButton
            isDisabled={!isValid || !isProjectModalDirty}
            isLoading={isSubmitting}
            className="max-w-[250px]"
            type="submit"
          >
            {projectDetail ? 'Save' : 'Create'}
          </PrimaryButton>
        </div>
      </form>
      <ConfirmModal
        onConfirm={() => {
          onClose()
          setOpenConfirmCancel(false)
        }}
        text={
          'You have unsaved changes that will be lost if you leave this page. Do you want to continue?'
        }
        openState={[openConfirmCancel, setOpenConfirmCancel]}
        title={'Unsaved Changes'}
      ></ConfirmModal>
    </>
  )
}

export default ProjectDetailModal
