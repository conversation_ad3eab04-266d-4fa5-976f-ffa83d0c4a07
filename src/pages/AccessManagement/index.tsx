import { Outlet, useLocation } from 'react-router-dom'
import PrimaryButton from '../../components/PrimaryButtons'
import { ROUTE_PATH } from '../../enum/RoutePath'
import { useState } from 'react'
import CustomModal from '../../components/Modal'
import GroupDetailModal from './Groups/GroupDetailModal'
import ProjectDetailModal from './Projects/ProjectDetailModal'
import InviteUserModal from './PendingRequests/InviteUserModal'
import { ITabNavigationLinks } from '../../interfaces'
import HorizontalTabNavigation from '../../components/HorizontalTabNavigation'
import PageHeaderText from '../../components/PageHeaderText'
import { useAppSelector } from '../../stores/hooks'
import { selectCurrentUser } from '../../stores/Reducers/usersReducer'

const AccessManangementPage = () => {
  const location = useLocation()
  const userProfile = useAppSelector(selectCurrentUser)
  const [addGroupModal, setAddGroupModal] = useState<boolean>(false)
  const [addProjectModal, setAddProjectModal] = useState<boolean>(false)
  const [inviteUserModal, setInviteUserModal] = useState<boolean>(false)
  const [isProjectModalDirty, setIsProjectModalDirty] = useState<boolean>(false) // check if theres any modification in the modal
  const [isGroupModalDirty, setIsGroupModalDirty] = useState<boolean>(false) // check if theres any modification in the modal
  const tabs: (ITabNavigationLinks | undefined)[] = [
    {
      name: `Requests`,
      href: ROUTE_PATH.Access_Management,
      current: location.pathname === ROUTE_PATH.Access_Management
    },

    {
      name: 'Projects',
      href: ROUTE_PATH.Access_Projects,
      current: location.pathname === ROUTE_PATH.Access_Projects
    },
    {
      name: 'Group',
      href: ROUTE_PATH.Access_Groups,
      current: location.pathname === ROUTE_PATH.Access_Groups
    },
    userProfile?.is_super_admin
      ? {
          name: 'Users',
          href: ROUTE_PATH.Access_Users,
          current: location.pathname === ROUTE_PATH.Access_Users
        }
      : undefined
  ]
  const renderActionButton = () => {
    switch (location.pathname) {
      case ROUTE_PATH.Access_Management:
        return (
          <PrimaryButton
            onClick={() => setInviteUserModal(true)}
            className="sm:max-w-[200px]"
          >
            Invite User
          </PrimaryButton>
        )
      case ROUTE_PATH.Access_Projects:
        return (
          <PrimaryButton
            onClick={() => setAddProjectModal(true)}
            className="sm:max-w-[200px]"
          >
            Create new Project
          </PrimaryButton>
        )
      case ROUTE_PATH.Access_Groups:
        return (
          <PrimaryButton
            onClick={() => setAddGroupModal(true)}
            className="sm:max-w-[200px]"
          >
            Create new Group
          </PrimaryButton>
        )
    }
  }

  return (
    <div className="h-full w-full flex flex-col">
      <div className="mb-6 border-t-0 border-l-0 border-r-0 pb-0">
        <div className="flex flex-col sm:flex-row gap-4 sm:items-center sm:justify-between">
          <PageHeaderText>Access Management</PageHeaderText>
          {renderActionButton()}
        </div>
        <HorizontalTabNavigation tabs={tabs} />
      </div>
      <div className="flex-1 flex flex-col w-full overflow-auto">
        <Outlet />
      </div>
      <CustomModal
        openState={[addGroupModal, setAddGroupModal]}
        title={'Create new group'}
        isModalDirty={isGroupModalDirty}
      >
        <GroupDetailModal
          setIsGroupModalDirty={setIsGroupModalDirty}
          isGroupModalDirty={isGroupModalDirty}
          closeModal={() => setAddGroupModal(false)}
        />
      </CustomModal>
      <CustomModal
        isModalDirty={isProjectModalDirty}
        openState={[addProjectModal, setAddProjectModal]}
        title={'Create new project'}
      >
        <ProjectDetailModal
          isProjectModalDirty={isProjectModalDirty}
          setIsProjectModalDirty={setIsProjectModalDirty}
          onClose={() => setAddProjectModal(false)}
        />
      </CustomModal>
      <CustomModal
        openState={[inviteUserModal, setInviteUserModal]}
        title={'Invite An User'}
      >
        <InviteUserModal closeModal={() => setInviteUserModal(false)} />
      </CustomModal>
    </div>
  )
}

export default AccessManangementPage
