import SearchIcon from '../../../assets/svgs/SearchIcon'
import { ListGroup } from '../../../models/groups'
import { useEffect, useRef, useState } from 'react'
import ConfirmModal from '../../../components/ConfirmModal'
import CustomModal from '../../../components/Modal'
import GroupDetailModal from './GroupDetailModal'
import TextInputComponent from '../../../components/TextInputComponent'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  deleteGroupThunk,
  fetchListGroupThunk,
  selectGroupPage,
  selectGroupSearchText,
  selectGroupSortingOption,
  selectTotalGroup,
  setGroupPage,
  setGroupSearch
} from '../../../stores/Reducers/groupReducers'
import { useDebounce } from '../../../utils/hooks/useDebounce'
import ListGroupTable from './ListGroupTable'
import AssignGroupUserTable from './AssignGroupModalTable'
import PaginationComponent from '../../../components/Pagination'
import { DEFAULT_PAGE_SIZE } from '../../../utils'

const GroupsManagement = () => {
  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false)
  const [openManageUserModal, setOpenManageUserModal] = useState<boolean>(false)
  const [openEditGroupModal, setOpenEditGroupModal] = useState<boolean>(false)
  const totalGroup = useAppSelector(selectTotalGroup)
  const groupPage = useAppSelector(selectGroupPage)
  const groupSearchText = useAppSelector(selectGroupSearchText)
  const groupSort = useAppSelector(selectGroupSortingOption)
  const [isGroupModalDirty, setIsGroupModalDirty] = useState<boolean>(false)
  const dispatch = useAppDispatch()
  const selectedGroup = useRef<ListGroup>()

  const handleOpenDeleteModal = (group: ListGroup) => {
    selectedGroup.current = group
    setOpenDeleteModal(true)
  }

  const handleOpenEditModal = (group: ListGroup) => {
    selectedGroup.current = group
    setOpenEditGroupModal(true)
  }

  const onConfirmDelete = async () => {
    const deleteGroupAction = await dispatch(
      deleteGroupThunk(selectedGroup?.current?.id ?? '')
    )
    if (deleteGroupThunk.rejected.match(deleteGroupAction)) {
      return
    }
    setOpenDeleteModal(false)
  }

  const handleOpenUserModal = (group: ListGroup) => {
    selectedGroup.current = group
    setOpenManageUserModal(true)
  }

  const { isMounted } = useDebounce({
    func: () =>
      groupPage > 1
        ? dispatch(setGroupPage(1))
        : dispatch(fetchListGroupThunk()),
    searchText: groupSearchText
  })

  const initialFetchGroupData = async () => {
    await dispatch(fetchListGroupThunk())
    isMounted.current = true
  }

  useEffect(() => {
    initialFetchGroupData()
  }, [groupPage, groupSort])

  return (
    <div className="flex flex-1 flex-col">
      <div className="sm:w-1/2 mb-4">
        <TextInputComponent
          prefixIcon={<SearchIcon />}
          onChange={(e) => dispatch(setGroupSearch(e.target.value))}
          placeholder="Search..."
        />
      </div>
      <ListGroupTable
        handleOpenUserModal={handleOpenUserModal}
        handleOpenDeleteModal={handleOpenDeleteModal}
        handleOpenEditModal={handleOpenEditModal}
      />
      <PaginationComponent
        totalItems={totalGroup}
        currentPage={groupPage}
        onPageChange={(page) => dispatch(setGroupPage(page))}
        pageSize={DEFAULT_PAGE_SIZE}
      />

      {/* Confirm delete gorup modal */}

      <ConfirmModal
        onConfirm={onConfirmDelete}
        text={`You are deleting group “${selectedGroup?.current?.name}”`}
        openState={[openDeleteModal, setOpenDeleteModal]}
        title={'Delete this group?'}
      />

      {/* Create/Edit Group modal */}

      <CustomModal
        isModalDirty={isGroupModalDirty}
        openState={[openEditGroupModal, setOpenEditGroupModal]}
        title={'Edit group'}
      >
        <GroupDetailModal
          groupDetail={selectedGroup?.current}
          setIsGroupModalDirty={setIsGroupModalDirty}
          isGroupModalDirty={isGroupModalDirty}
          closeModal={() => setOpenEditGroupModal(false)}
        />
      </CustomModal>

      {/* Manage Group User Modal */}

      <CustomModal
        className="!min-h-[400px] md:!min-w-[60vw]"
        openState={[openManageUserModal, setOpenManageUserModal]}
        title={<p>{`Group ${selectedGroup.current?.name}'s Users`}</p>}
      >
        <AssignGroupUserTable
          closeModal={() => setOpenManageUserModal(false)}
          selectedGroup={selectedGroup.current}
        />
      </CustomModal>
    </div>
  )
}

export default GroupsManagement
