import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import CommandPaletteComponent from '../../../../components/CommandPalette'
import PrimaryButton from '../../../../components/PrimaryButtons'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  getListUsersThunk,
  selectListUsers,
  selectUserLoading,
  selectUserSearchText,
  setUserSearchText,
  triggerFetchUserLoading
} from '../../../../stores/Reducers/usersReducer'
import { useDebounce } from '../../../../utils/hooks/useDebounce'
import UserDropdownItemComponent from '../../../../components/UserDropdownItems'
import { IOption } from '../../../../interfaces'
import { IGroupUserModifyParams, ListGroup } from '../../../../models/groups'
import { toast } from 'react-hot-toast'
import { addGroupUserThunk } from '../../../../stores/Reducers/groupReducers'
import {
  capitalizeFirstLetter,
  renderUnauthorizedMessage
} from '../../../../utils'

type Props = {
  selectedGroup?: ListGroup
  closeModal: () => void
  memberRole?: string
}

const AddGroupMemberModal = ({
  selectedGroup,
  memberRole,
  closeModal
}: Props) => {
  const listUsers = useAppSelector(selectListUsers)
  const dispatch = useAppDispatch()
  const userSearchText = useAppSelector(selectUserSearchText)
  const loading = useAppSelector(selectUserLoading)
  const assignedUsers = useRef<IOption>()
  const [isAssigningUser, setIsAssigningUser] = useState<boolean>(false)
  const [errorMessage, setErrorMessage] = useState<string>('')

  const { isMounted } = useDebounce({
    func: () => userSearchText !== '' && fetchUserData(),
    searchText: userSearchText
  })

  const fetchUserData = async () => {
    await dispatch(getListUsersThunk())
    isMounted.current = true
  }

  const onAssignGroupUser = async () => {
    if (!assignedUsers.current) {
      setErrorMessage('Please select a user!')
      return
    }
    setIsAssigningUser(true)
    const data: IGroupUserModifyParams = {
      group_id: selectedGroup?.id ?? '',
      data: {
        user_id: assignedUsers.current?.value ?? '',
        role_id: memberRole
      }
    }
    const action = await dispatch(addGroupUserThunk(data))
    setIsAssigningUser(false)
    if (addGroupUserThunk.rejected.match(action)) {
      const errorMsg = renderUnauthorizedMessage(action.payload as string)
      setErrorMessage(
        capitalizeFirstLetter(
          errorMsg.includes('user already in group')
            ? 'The user is already in the group'
            : errorMsg
        )
      )
      return
    }
    toast.success(
      `User ${assignedUsers.current?.label} has been added to the group!`
    )
    closeModal()
  }

  const onSelectUser = useCallback((user: IOption) => {
    dispatch(setUserSearchText(user.additonalValue ?? ''))
    assignedUsers.current = user
  }, [])

  const onSearchUser = useCallback((text: string) => {
    if (text === '') {
      assignedUsers.current = undefined
    }
    dispatch(triggerFetchUserLoading())
    dispatch(setUserSearchText(text.trim()))
    if (errorMessage !== '') {
      setErrorMessage('')
    }
  }, [])

  const onClearSearchValue = useCallback(() => {
    assignedUsers.current = undefined
  }, [])

  const listUsersOptions = useMemo(
    () =>
      listUsers.map((user) => ({
        label: user.name,
        value: user.id,
        additonalValue: user.email
      })),
    [listUsers]
  )

  useEffect(() => {
    if (!isMounted.current) {
      fetchUserData()
    }
  }, [])

  return (
    <div className="bg-white min-h-full flex flex-col gap-4 px-4 py-5 sm:p-6">
      <div className="w-full">
        <h3 className="text-base font-semibold text-mainBlack">
          Add a new member
        </h3>
        <div className="mt-2 max-w-xl text-sm text-gray-500">
          <p>Add a new member by searching their email.</p>
        </div>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            onAssignGroupUser()
          }}
          className="mt-5 flex flex-col sm:flex-row w-full gap-4"
        >
          <div className="flex-1 flex flex-col gap-1">
            <CommandPaletteComponent
              option={listUsersOptions}
              loading={loading}
              onSearch={onSearchUser}
              onSelect={onSelectUser}
              onClearText={onClearSearchValue}
              isError={Boolean(errorMessage)}
              placeholder="Search a user by their email"
              searchText={userSearchText}
              render={(item) => <UserDropdownItemComponent item={item} />}
            />
            <p className="text-xs text-red-400">{errorMessage}</p>
          </div>
          <PrimaryButton
            isLoading={isAssigningUser}
            isDisabled={isAssigningUser}
            type="submit"
            className="sm:max-w-[25%] max-h-10"
          >
            Add
          </PrimaryButton>
        </form>
      </div>
    </div>
  )
}

export default AddGroupMemberModal
