import TextInputComponent from '../../../../components/TextInputComponent'
import SearchIcon from '../../../../assets/svgs/SearchIcon'
import { ENV, classNames } from '../../../../utils'
import { useEffect, useRef, useState } from 'react'
import { Select, Skeleton, Tooltip } from 'antd'
import {
  IGroupDetailUser,
  IGroupUserModifyParams,
  IGroupUserRoleUpdateParams,
  ListGroup
} from '../../../../models/groups'
import { getListRoles } from '../../../../api/Roles'
import { IRoles } from '../../../../models/roles'
import NoDataComponent from '../../../../components/NoDataComponent'
import { LoaderIcon } from 'react-hot-toast'
import PrimaryButton from '../../../../components/PrimaryButtons'
import { MinusCircleIcon } from '@heroicons/react/24/solid'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  getGroupInfoThunk,
  removeGroupUserThunk,
  selectGroupDetail,
  selectGroupDetailLoading,
  updateUserRoleThunk
} from '../../../../stores/Reducers/groupReducers'
import ConfirmModal from '../../../../components/ConfirmModal'
import CustomModal from '../../../../components/Modal'
import AddGroupMemberModal from '../AddMemberModal'
import { selectCurrentUser } from '../../../../stores/Reducers/usersReducer'
import ProfileIcon from '../../../../assets/svgs/ProfileIcon'

type Props = {
  selectedGroup?: ListGroup
  closeModal: () => void
}

const AssignGroupUserTable = ({ selectedGroup, closeModal }: Props) => {
  const groupDetailLoading = useAppSelector(selectGroupDetailLoading)
  const groupDetail = useAppSelector(selectGroupDetail)
  const currentUser = useAppSelector(selectCurrentUser)
  const [openAddUserModal, setOpenAddUserModal] = useState<boolean>(false)
  const [openConfirmRemoveModal, setOpenConfirmRemoveModal] =
    useState<boolean>(false)
  const [userSearch, setUserSearch] = useState<string>('')
  const [listRoles, setListRoles] = useState<IRoles[]>([])
  const [isProcessing, setIsProcessing] = useState<string[]>([])
  const [usersBeingRemoved, setUsersBeingRemoved] = useState<string[]>([])
  const removedUser = useRef<IGroupDetailUser>()
  const dispatch = useAppDispatch()

  const handleOpenAddUserModal = () => {
    setOpenAddUserModal(true)
  }
  const onSelectUserRole = async (user: IGroupDetailUser, role_id: string) => {
    const selectedRole = listRoles.find((role) => role.id === role_id)
    setIsProcessing((prev) => [...prev, user.id])
    const data: IGroupUserRoleUpdateParams = {
      group_id: selectedGroup?.id ?? '',
      role_name: selectedRole?.name ?? '',
      data: {
        role_id: role_id,
        user_id: user.id
      }
    }
    await dispatch(updateUserRoleThunk(data))
    setIsProcessing((prev) => prev.filter((user_id) => user_id !== user.id))
  }

  const triggerRemoveUserConfirm = (user: IGroupDetailUser) => {
    removedUser.current = user
    setOpenConfirmRemoveModal(true)
  }

  const handleRemoveUser = async () => {
    setUsersBeingRemoved((prev) => [...prev, removedUser.current?.id ?? ''])
    const data: IGroupUserModifyParams = {
      group_id: selectedGroup?.id ?? '',
      data: {
        user_id: removedUser.current?.id ?? ''
      }
    }
    const removeAction = await dispatch(removeGroupUserThunk(data))
    if (removeGroupUserThunk.fulfilled.match(removeAction)) {
      setOpenConfirmRemoveModal(false)
    }
    setUsersBeingRemoved((prev) =>
      prev.filter((removingUser) => removingUser !== removedUser.current?.id)
    )
  }

  const fetchListRoles = async () => {
    try {
      const res = await getListRoles()
      setListRoles(res.data)
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
    }
  }

  const isUserAdmin = (user: IGroupDetailUser) =>
    user.role.name.toLocaleLowerCase() === 'admin'

  useEffect(() => {
    fetchListRoles()
    dispatch(getGroupInfoThunk(selectedGroup?.id ?? ''))
  }, [])

  if (groupDetailLoading) {
    return <Skeleton />
  }

  return (
    <div className="flex flex-col gap-4 h-full overflow-hidden flex-1">
      <div className="w-full">
        <TextInputComponent
          prefixIcon={<SearchIcon />}
          onChange={(e) => setUserSearch(e.target.value.trim())}
          placeholder="Search users by their email..."
        />
      </div>
      <div className="flex-1 w-full overflow-auto">
        <div className="-my-2 overflow-auto w-full h-full">
          <div className="min-w-full py-2 align-middle h-full overflow-auto">
            <table className="min-w-full table w-full divide-gray-300">
              <thead className={classNames('sticky top-2 w-full bg-gray-50')}>
                <tr>
                  <th
                    scope="col"
                    className="py-3.5 px-4 w-[70px] text-center text-sm font-semibold text-mainBlack"
                  >
                    No.
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    User Details
                  </th>
                  <th
                    scope="col"
                    className="px-3 py-3.5 text-center text-sm font-semibold text-mainBlack"
                  >
                    <div className="flex w-full justify-left gap-2 items-center">
                      <p>Group</p>
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="py-3.5 text-left text-sm font-semibold text-mainBlack"
                  >
                    <div className="flex w-full gap-2 justify-center items-center">
                      <p>Role</p>
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="py-3.5 px-4 text-left text-sm font-semibold text-mainBlack"
                  >
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white w-full overflow-auto">
                {groupDetail.users &&
                groupDetail.users?.filter((filterUser) =>
                  filterUser.email
                    .toLocaleLowerCase()
                    .includes(userSearch.toLocaleLowerCase())
                ).length > 0 ? (
                  groupDetail.users
                    .filter((filterUser) =>
                      filterUser.email
                        .toLocaleLowerCase()
                        .includes(userSearch.toLocaleLowerCase())
                    )
                    .map((user, index) => (
                      <tr key={user.id} className="even:bg-gray-50">
                        <td className="whitespace-nowrap px-4 text-center py-4 text-sm font-medium text-mainBlack">
                          {index + 1}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm  text-mainBlack">
                          <div className="flex w-full items-center gap-4">
                            <ProfileIcon height={40} width={40} />
                            <div className="flex flex-col gap-2">
                              <p>{user.name}</p>
                              <p className="text-mainBlack text-xs">
                                {user.email}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-3 text-mainBlack  py-4 text-left text-sm ">
                          {selectedGroup?.name}
                        </td>

                        <td className="whitespace-nowrap text-mainBlack p-4 text-center text-sm ">
                          <Select
                            options={listRoles.map((role) => ({
                              label: role.name.replace('_', ' '),
                              value: role.id
                            }))}
                            loading={isProcessing.includes(user.id)}
                            disabled={
                              isProcessing.includes(user.id) ||
                              isUserAdmin(user) ||
                              currentUser?.email === user.email
                            }
                            className="w-[200px] max-w-[200px]"
                            value={isUserAdmin(user) ? 'Admin' : user.role.id}
                            onChange={(role_id) =>
                              onSelectUserRole(user, role_id)
                            }
                          />
                        </td>
                        <td className="whitespace-nowrap text-mainBlack p-4 text-center text-sm ">
                          {usersBeingRemoved.includes(user.id) ? (
                            <LoaderIcon />
                          ) : (
                            <Tooltip
                              title="Remove this user"
                              mouseEnterDelay={0.5}
                            >
                              <MinusCircleIcon
                                height={20}
                                onClick={() => triggerRemoveUserConfirm(user)}
                                width={20}
                                fill="red"
                                className={classNames(
                                  (isUserAdmin(user) ||
                                    currentUser?.email === user.email) &&
                                    'hidden'
                                )}
                                cursor={'pointer'}
                              />
                            </Tooltip>
                          )}
                        </td>
                      </tr>
                    ))
                ) : (
                  <tr className="pt-6 h-[150px]">
                    <td colSpan={5}>
                      <NoDataComponent />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div className="flex bg-white items-center sticky bottom-0 py-4 justify-between">
        <p
          onClick={handleOpenAddUserModal}
          className="text-sm font-medium text-primary hover:underline cursor-pointer"
        >
          Add a member
        </p>
        <PrimaryButton
          onClick={closeModal}
          className="mt-auto max-w-[200px] ml-auto"
        >
          {' '}
          Close
        </PrimaryButton>
      </div>
      <ConfirmModal
        onConfirm={handleRemoveUser}
        text={`User ${removedUser.current?.name} will not be able to view this group's resources.`}
        openState={[openConfirmRemoveModal, setOpenConfirmRemoveModal]}
        title={`Remove user ${removedUser?.current?.name}`}
      />

      {/* Add Group User Modal */}
      <CustomModal
        title=""
        className="!p-0 lg:!min-w-[40vw]"
        openState={[openAddUserModal, setOpenAddUserModal]}
      >
        <AddGroupMemberModal
          closeModal={() => setOpenAddUserModal(false)}
          selectedGroup={selectedGroup}
          memberRole={
            listRoles.find((role) => role.name.toLowerCase() === 'member')?.id
          }
        />
      </CustomModal>
    </div>
  )
}

export default AssignGroupUserTable
