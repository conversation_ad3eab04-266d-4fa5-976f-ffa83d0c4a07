import { useForm } from 'react-hook-form'
import {
  CreateGroupInput,
  EditGroupAPIParams,
  ListGroup
} from '../../../../models/groups'
import { useEffect, useState } from 'react'
import SecondaryButton from '../../../../components/SecondaryButtons'
import PrimaryButton from '../../../../components/PrimaryButtons'
import TextInputComponent from '../../../../components/TextInputComponent'
import FormInputContainer from '../../../../components/FormInputComponent'
import { useAppDispatch } from '../../../../stores/hooks'
import {
  createGroupThunk,
  editGroupThunk
} from '../../../../stores/Reducers/groupReducers'
import ConfirmModal from '../../../../components/ConfirmModal'
import { REGEX_PREVENT_ONLY_WHITESPACES } from '../../../../enum/Regex'

type Props = {
  groupDetail?: ListGroup
  closeModal: () => void
  setIsGroupModalDirty: React.Dispatch<React.SetStateAction<boolean>>
  isGroupModalDirty: boolean // check if there's any modification in the modal
}

const GroupDetailModal = ({
  groupDetail,
  closeModal,
  isGroupModalDirty,
  setIsGroupModalDirty
}: Props) => {
  const dispatch = useAppDispatch()
  const {
    formState: { errors, isSubmitting, isDirty },
    setFocus,
    control,
    handleSubmit,
    watch
  } = useForm<CreateGroupInput>({
    mode: 'onChange',
    defaultValues: groupDetail
      ? {
          name: groupDetail.name,
          description: groupDetail.description
        }
      : {
          name: '',
          description: ''
        }
  })
  const groupNameValue = watch('name')
  const groupDescriptionValue = watch('description')
  const [openConfirmCancel, setOpenConfirmCancel] = useState<boolean>(false) //list of removed users to pass in the edit project api

  const onSubmit = async (data: CreateGroupInput) =>
    groupDetail ? await handleEditGroup(data) : await handleCreateGroup(data)

  const handleCreateGroup = async (data: CreateGroupInput) => {
    const params: CreateGroupInput = {
      ...data,
      name: data.name.trim(),
      description: data.description.trim()
    }
    const createGroupAction = await dispatch(createGroupThunk(params))
    if (createGroupThunk.fulfilled.match(createGroupAction)) {
      closeModal()
    }
  }

  const handleEditGroup = async (data: CreateGroupInput) => {
    if (groupDetail) {
      const params: EditGroupAPIParams = {
        group_id: groupDetail.id,
        data: {
          name: data.name.trim(),
          description: data.description.trim()
        }
      }
      const editGroupAction = await dispatch(editGroupThunk(params))
      if (editGroupThunk.fulfilled.match(editGroupAction)) {
        closeModal()
      }
    }
  }

  const onCancelModal = () => {
    if (isDirty) {
      setOpenConfirmCancel(true)
    } else {
      closeModal()
    }
  }

  useEffect(() => {
    setFocus('name') //auto focus the Group name field when mounted
  }, [])

  useEffect(() => {
    setIsGroupModalDirty(isDirty)
  }, [isDirty])

  return (
    <>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="w-full h-full flex gap-2 flex-col"
      >
        <FormInputContainer<CreateGroupInput>
          control={control}
          errors={errors}
          vertialAlign
          label={'Group Name'}
          required
          name={'name'}
          maxCharactersText={`${groupNameValue?.length ?? 0}/50 characters`}
          rules={{
            required: 'Required',
            pattern: {
              value: REGEX_PREVENT_ONLY_WHITESPACES,
              message: 'Required'
            }
          }}
          render={({ field }) => (
            <TextInputComponent
              maxLength={50}
              placeholder="Enter group name"
              defaultValue={groupDetail?.name}
              onChange={field.onChange}
            />
          )}
        />

        <FormInputContainer<CreateGroupInput>
          errors={errors}
          vertialAlign
          maxCharactersText={`${
            groupDescriptionValue?.length ?? 0
          }/250 characters`}
          label={'Description'}
          name={'description'}
          control={control}
          render={({ field }) => (
            <textarea
              placeholder="Enter group description"
              rows={2}
              maxLength={250}
              className="block w-full bg-[#F3F4F6] rounded-md border-0 py-1.5 text-mainBlack shadow-sm ring-0 focus:ring-0 placeholder:text-gray-400 sm:text-sm sm:leading-6"
              defaultValue={groupDetail?.description}
              onChange={field.onChange}
            />
          )}
        />

        <div className="flex gap-2 sticky bottom-0 justify-end w-full bg-white py-4">
          <SecondaryButton onClick={onCancelModal} className="sm:max-w-[200px]">
            Cancel
          </SecondaryButton>
          <PrimaryButton
            isLoading={isSubmitting}
            isDisabled={isSubmitting || !isGroupModalDirty}
            type="submit"
            className="sm:max-w-[200px]"
          >
            {groupDetail ? 'Save' : 'Create'}
          </PrimaryButton>
        </div>
      </form>
      <ConfirmModal
        onConfirm={() => {
          closeModal()
          setOpenConfirmCancel(false)
        }}
        text={
          'You have unsaved changes that will be lost if you leave this page. Do you want to continue?'
        }
        openState={[openConfirmCancel, setOpenConfirmCancel]}
        title={'Unsaved Changes'}
      ></ConfirmModal>
    </>
  )
}

export default GroupDetailModal
