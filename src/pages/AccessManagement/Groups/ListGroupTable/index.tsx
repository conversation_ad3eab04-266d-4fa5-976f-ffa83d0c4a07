import { Skeleton, Tooltip } from 'antd'
import DropdownOptions from '../../../../components/DropdownOptions'
import { IOption } from '../../../../interfaces'
import {
  selectGroupSortingOption,
  selectListGroup,
  selectListGroupLoading,
  setGroupSortingOption
} from '../../../../stores/Reducers/groupReducers'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import EditIcon from '../../../../assets/svgs/EditIcon'
import TrashIcon from '../../../../assets/svgs/TrashIcon'
import NoDataComponent from '../../../../components/NoDataComponent'
import { ListGroup } from '../../../../models/groups'
import ManageUsersIcon from '../../../../assets/svgs/ManageUsersIcon'

type Props = {
  handleOpenDeleteModal: (group: ListGroup) => void
  handleOpenEditModal: (group: ListGroup) => void
  handleOpenUserModal: (group: ListGroup) => void
}
const ORDER_OPTIONS: IOption[] = [
  { value: 'name-asc', label: "Group's name (A-Z)" },
  { value: 'name-desc', label: "Group's name (Z-A)" }
]

const ListGroupTable = ({
  handleOpenDeleteModal,
  handleOpenEditModal,
  handleOpenUserModal
}: Props) => {
  const listGroup = useAppSelector(selectListGroup)
  const loading = useAppSelector(selectListGroupLoading)
  const groupSort = useAppSelector(selectGroupSortingOption)

  const dispatch = useAppDispatch()
  return (
    <div className="w-full flex-1 overflow-auto">
      <div className="-my-2 overflow-x-auto w-full h-full">
        <div className="min-w-full py-2 align-middle">
          <table className="min-w-full relative table w-full divide-gray-300">
            <thead className="sticky top-2 w-full bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="py-3.5 px-4 w-[200px] text-center text-sm font-semibold text-mainBlack"
                >
                  <div className="flex w-fit mx-auto justify-center gap-2 items-center">
                    <p>Group</p>
                    <DropdownOptions
                      label={''}
                      onlyButtonIcon
                      selected={groupSort}
                      options={ORDER_OPTIONS}
                      onSelect={(value: string) =>
                        dispatch(setGroupSortingOption(value))
                      }
                    />
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-4 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  Description
                </th>
                <th
                  scope="col"
                  className="px-3 py-3.5 text-center text-sm font-semibold text-mainBlack"
                >
                  <div className="flex w-full justify-center gap-2 items-center">
                    <p>Users</p>
                  </div>
                </th>

                <th
                  scope="col"
                  className="py-3.5 px-3 text-center text-sm font-semibold text-mainBlack"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white w-full overflow-auto">
              {loading ? (
                <>
                  <tr>
                    <td colSpan={4}>
                      <Skeleton active />
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={4}>
                      <Skeleton active />
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={4}>
                      <Skeleton active />
                    </td>
                  </tr>
                </>
              ) : listGroup?.length > 0 ? (
                listGroup?.map((group) => (
                  <tr key={group.id} className="even:bg-gray-50">
                    <td className="whitespace-nowrap px-4 pl-0 text-center py-4 text-sm font-medium text-mainBlack">
                      {group.name}
                    </td>
                    <td className="whitespace-nowrap max-w-[10vw] px-3 py-4 text-sm truncate text-mainBlack">
                      <Tooltip
                        title={group.description}
                        mouseEnterDelay={0.5}
                        className="text-mainBlack truncate w-full block text-center"
                      >
                        {group.description}
                      </Tooltip>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-center text-sm ">
                      <p
                        className="text-mainBlack cursor-pointer hover:underline"
                        onClick={() => handleOpenUserModal(group)}
                      >
                        {group?.users?.length ?? 0} users{' '}
                      </p>
                    </td>
                    <td className="whitespace-nowrap py-4 px-3 text-center text-sm font-medium ">
                      <div className="w-full flex justify-center gap-4 items-center">
                        <EditIcon
                          onClick={() => handleOpenEditModal(group)}
                          className="cursor-pointer hover:fill-primary"
                        />
                        <TrashIcon
                          cursor={'pointer'}
                          onClick={() => handleOpenDeleteModal(group)}
                          className="hover:fill-primary"
                        />
                        <ManageUsersIcon
                          width={24}
                          height={24}
                          onClick={() => handleOpenUserModal(group)}
                          cursor={'pointer'}
                          className="fill-gray-500 hover:fill-primary"
                        />
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="h-[300px]">
                    <NoDataComponent />
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default ListGroupTable
