import { useState } from 'react'
import {
  MAIN_OPERATING_SYSTEMS,
  OPERATING_SYSTEM
} from '../../../../enum/OperatingSystems'
import { ArrowDownOnSquareStackIcon } from '@heroicons/react/24/outline'
import PrimaryButton from '../../../../components/PrimaryButtons'
import SecondaryButton from '../../../../components/SecondaryButtons'
import toast from 'react-hot-toast'
import { downloadBinaryFile } from '../../../../api/Agents'
import { useAppSelector } from '../../../../stores/hooks'
import { selectAPIKey } from '../../../../stores/Reducers/agentReducers'
import { getErrorMessage } from '../../../../utils'

type Props = {
  selectedOS: MAIN_OPERATING_SYSTEMS
  setCommandLine: (commandLine: string) => void
  setCommandLineCHMOD: (chmodCMD: string) => void
}

const DownloadButtonsComponents = ({
  selectedOS,
  setCommandLine,
  setCommandLineCHMOD
}: Props) => {
  const apiKey = useAppSelector(selectAPIKey)
  const [isDownloading, setIsDownloading] = useState<boolean>(false)
  const triggerBinaryDownload = async (os: OPERATING_SYSTEM) => {
    const toastId = toast.loading('Preparing your file...')
    setIsDownloading(true)
    try {
      const res = await downloadBinaryFile(os)
      const fileName = res.data.command.split(' --token')[0]

      setCommandLine(
        (selectedOS === MAIN_OPERATING_SYSTEMS.WINDOW
          ? `${res.data.command} `
          : `./${res.data.command} `) + apiKey
      )

      setCommandLineCHMOD(`chmod +x ${fileName.replace('./', '')}`)
      const link = document.createElement('a')
      link.href = res.data.file_path
      link.setAttribute('download', '')

      // Append to html link element page
      document.body.appendChild(link)

      // Start download
      link.click()

      // Clean up and remove the link
      link.parentNode?.removeChild(link)
      toast.success('Your file is being downloaded', { id: toastId })
    } catch (error) {
      toast.error(getErrorMessage(error), { id: toastId })
    } finally {
      setIsDownloading(false)
    }
  }

  switch (selectedOS) {
    case MAIN_OPERATING_SYSTEMS.LINUX:
      return (
        <>
          <div className="flex gap-2 md:flex-row flex-col">
            <PrimaryButton
              className="md:max-w-[250px] max-h-fit md:my-4 md:mb-0 text-xs"
              onClick={() =>
                triggerBinaryDownload(OPERATING_SYSTEM.LINUX_AMD64)
              }
              isDisabled={isDownloading}
            >
              <span>
                <ArrowDownOnSquareStackIcon
                  height={20}
                  width={20}
                  stroke="white"
                />
              </span>{' '}
              Download for Linux AMD x64
            </PrimaryButton>
            <SecondaryButton
              className="md:max-w-[250px] max-h-fit md:my-4 md:mb-0 text-xs text-black"
              onClick={() =>
                triggerBinaryDownload(OPERATING_SYSTEM.LINUX_ARM64)
              }
              isDisabled={isDownloading}
            >
              <span>
                <ArrowDownOnSquareStackIcon
                  height={20}
                  width={20}
                  stroke="black"
                />
              </span>{' '}
              Download for Linux ARM x64
            </SecondaryButton>
          </div>
          <SecondaryButton
            className=" md:max-w-[250px] max-h-fit md:my-2 text-xs text-black"
            onClick={() => triggerBinaryDownload(OPERATING_SYSTEM.LINUX_ARM)}
            isDisabled={isDownloading}
          >
            <span>
              <ArrowDownOnSquareStackIcon
                height={20}
                width={20}
                stroke="black"
              />
            </span>{' '}
            Download for Linux ARM
          </SecondaryButton>
        </>
      )
    case MAIN_OPERATING_SYSTEMS.WINDOW:
      return (
        <div className="flex gap-2 sm:flex-row flex-col">
          <PrimaryButton
            className="sm:max-w-[250px] sm:my-4 text-xs"
            onClick={() => triggerBinaryDownload(OPERATING_SYSTEM.WINDOWS64)}
            isDisabled={isDownloading}
          >
            <span>
              <ArrowDownOnSquareStackIcon
                height={20}
                width={20}
                stroke="white"
              />
            </span>{' '}
            Download for Windows x64bit
          </PrimaryButton>
          <SecondaryButton
            className="sm:max-w-[250px] bg-red-400 hover:bg-red-400 sm:my-4 text-xs text-black"
            onClick={() => triggerBinaryDownload(OPERATING_SYSTEM.WINDOWS32)}
            isDisabled={isDownloading}
          >
            <span>
              <ArrowDownOnSquareStackIcon
                height={20}
                width={20}
                stroke="black"
              />
            </span>{' '}
            Download for Windows x32bit
          </SecondaryButton>
        </div>
      )
    case MAIN_OPERATING_SYSTEMS.MACOS:
      return (
        <div className="flex gap-2 sm:flex-row flex-col">
          <PrimaryButton
            className="sm:max-w-[250px] sm:my-4 text-xs"
            onClick={() => triggerBinaryDownload(OPERATING_SYSTEM.DARWIN_ARM64)}
            isDisabled={isDownloading}
          >
            <span>
              <ArrowDownOnSquareStackIcon
                height={20}
                width={20}
                stroke="white"
              />
            </span>{' '}
            Download for MacARM
          </PrimaryButton>
          <SecondaryButton
            className="sm:max-w-[250px] bg-red-400 hover:bg-red-400 sm:my-4 text-xs text-black"
            onClick={() => triggerBinaryDownload(OPERATING_SYSTEM.DARWIN_AMD64)}
            isDisabled={isDownloading}
          >
            <span>
              <ArrowDownOnSquareStackIcon
                height={20}
                width={20}
                stroke="black"
              />
            </span>{' '}
            Download for MacAMD
          </SecondaryButton>
        </div>
      )
  }
}

export default DownloadButtonsComponents
