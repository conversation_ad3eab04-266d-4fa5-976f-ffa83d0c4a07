import { classNames, handleCopy } from '../../../utils'
import { Square2StackIcon } from '@heroicons/react/24/outline'
import { MAIN_OPERATING_SYSTEMS } from '../../../enum/OperatingSystems'
import SecondaryButton from '../../../components/SecondaryButtons'
import { useState } from 'react'
import { Select } from 'antd'
import { useAppSelector } from '../../../stores/hooks'
import { selectAPIKey } from '../../../stores/Reducers/agentReducers'
import DownloadButtonsComponents from './DownloadButtons'

type Props = {
  closeModal: () => void
}

const DownloadBinaryFileModal = ({ closeModal }: Props) => {
  const DEFAULT_CHMOD_COMMAND = `chmod +x <file_name>`
  const apiKey = useAppSelector(selectAPIKey)
  const operatingSystem = Object.values(MAIN_OPERATING_SYSTEMS)
  const [selectedOS, setSelectedOS] = useState<MAIN_OPERATING_SYSTEMS>(
    MAIN_OPERATING_SYSTEMS.WINDOW
  )
  const DEFAULT_COMMAND_LINE = `./<file_name> --token ${apiKey}`
  const [commandLine, setCommandLine] = useState<string>(
    DEFAULT_COMMAND_LINE.replace(
      './',
      selectedOS === MAIN_OPERATING_SYSTEMS.WINDOW ? '' : './'
    )
  )
  const [commandLineCHMOD, setCommandLineCHMOD] = useState<string>(
    DEFAULT_CHMOD_COMMAND
  )

  return (
    <div className="flex flex-col">
      <span className="text-gray-600 text-xs font-semibold mb-4">
        Choose your operating system to download the agent's binary file
      </span>
      <div>
        <Select
          options={operatingSystem
            .filter((os) => os !== MAIN_OPERATING_SYSTEMS.MACARM)
            .map((os) => {
              return {
                label: os,
                value: os
              }
            })}
          value={selectedOS}
          className="w-full min-h-[40px] sm:hidden"
          onChange={(e) => setSelectedOS(e)}
        />
        <div className="hidden sm:block">
          <nav
            aria-label="Tabs"
            className="isolate flex p-1 gap-2 rounded-lg bg-gray-50 shadow"
          >
            {operatingSystem
              .filter((os) => os !== MAIN_OPERATING_SYSTEMS.MACARM)
              .map((tab) => (
                <div
                  key={tab}
                  onClick={() => {
                    setSelectedOS(tab)
                    setCommandLine(
                      DEFAULT_COMMAND_LINE.replace(
                        './',
                        tab === MAIN_OPERATING_SYSTEMS.WINDOW ? '' : './'
                      )
                    )
                    setCommandLineCHMOD(DEFAULT_CHMOD_COMMAND)
                  }}
                  className={classNames(
                    'group rounded-lg cursor-pointer relative min-w-0 flex-1 overflow-hidden px-4 py-2 text-center text-sm font-medium hover:bg-white focus:z-10',
                    selectedOS === tab ? 'bg-white' : 'bg-transparent'
                  )}
                >
                  <span
                    className={classNames(
                      'text-xs font-semibold group-hover:text-black',
                      selectedOS === tab ? 'text-black' : 'text-[#7b7c87]'
                    )}
                  >
                    {tab}
                  </span>
                </div>
              ))}
          </nav>
        </div>
      </div>
      <div className="w-full my-2 border border-solid flex flex-col gap-2 border-[#f2f2f2] rounded-lg p-4">
        <p className="text-xl font-bold">{selectedOS} Installation</p>
        <span className="text-gray-500 block font-semibold text-xs">
          Download the agent's binary file with these simple steps
        </span>
        <DownloadButtonsComponents
          selectedOS={selectedOS}
          setCommandLine={(commandLine) => setCommandLine(commandLine)}
          setCommandLineCHMOD={(chmod) => setCommandLineCHMOD(chmod)}
        />
        <p className="text-lg font-bold">Download instructions</p>
        <ol className="list-decimal flex flex-col gap-2 px-4 text-sm">
          <li>Download the appropriate binary file for your system</li>
          {selectedOS !== MAIN_OPERATING_SYSTEMS.WINDOW && (
            <>
              <li>
                Via the terminal, modify the permission of the binary file to
                have executable permission. You may need sudo permission.
              </li>
              <div className="relative z-0 line-clamp-2 min-w-[150px] bg-white flex-1 flex items-center rounded-md w-full">
                <div
                  className={classNames(
                    `flex flex-1 p-4 pr-12 h-[100px] w-full bg-gray-50 line-clamp-2 items-center rounded-md border-0 py-1.5 text-mainBlack ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6`
                  )}
                >
                  <div className="max-w-[97%] block font-mono line-clamp-2">
                    <p className="line-clamp-2">{`cd <path to the directory path to your binary> && ${commandLineCHMOD}`}</p>
                  </div>
                </div>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <Square2StackIcon
                    cursor={'pointer'}
                    onClick={() =>
                      handleCopy(
                        `cd <path to the directory path to your binary> && ${commandLineCHMOD}`
                      )
                    }
                    height={20}
                    width={20}
                  />
                </div>
              </div>
            </>
          )}
          <li>
            {selectedOS === MAIN_OPERATING_SYSTEMS.WINDOW
              ? 'Via the terminal, '
              : 'In the same terminal, '}{' '}
            run the following command to start the agent with its registered API
            key:
          </li>
          <div className="relative z-0 line-clamp-2 min-w-[150px] bg-white flex-1 flex items-center rounded-md w-full">
            <div
              className={classNames(
                `flex flex-1 p-4 pr-12 h-[100px] w-full bg-gray-50 line-clamp-2 items-center rounded-md border-0 py-1.5 text-mainBlack ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6`
              )}
            >
              <code className="max-w-[97%] line-clamp-2">{commandLine}</code>
            </div>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <Square2StackIcon
                cursor={'pointer'}
                onClick={() => handleCopy(commandLine)}
                height={20}
                width={20}
              />
            </div>
          </div>
        </ol>
      </div>

      <div className="w-full flex sticky bottom-0 bg-white py-4">
        <SecondaryButton onClick={closeModal} className="ml-auto max-w-[166px]">
          Close
        </SecondaryButton>
      </div>
    </div>
  )
}

export default DownloadBinaryFileModal
