import { useForm } from 'react-hook-form'
import { DEFAULT_PAGE_SIZE, renderUnauthorizedMessage } from '../../../utils'
import SecondaryButton from '../../../components/SecondaryButtons'
import PrimaryButton from '../../../components/PrimaryButtons'
import { AddAgentInputs } from '../../../models/agents'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import { createAgentThunk } from '../../../stores/Reducers/agentReducers'
import { REGEX_PREVENT_ONLY_WHITESPACES } from '../../../enum/Regex'
import TextInputComponent from '../../../components/TextInputComponent'
import FormInputContainer from '../../../components/FormInputComponent'
import { useEffect, useState } from 'react'
import {
  ListProjectThunkPayload,
  fetchListProjectThunk,
  selectListProjects,
  selectProjectPage,
  selectProjectSearchText,
  setPage,
  setProjectSearchText
} from '../../../stores/Reducers/projectsReducer'
import useLazyScroll from '../../../utils/hooks/useLazyScroll'
import LazyScrollSelectComponent from '../../../components/LazyScrollSelect'

type Props = {
  onCancel: () => void
  setApiKeyModalOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const AddAgentModal = ({ onCancel, setApiKeyModalOpen }: Props) => {
  const {
    formState: { errors, isSubmitting },
    handleSubmit,
    control,
    setError,
    watch
  } = useForm<AddAgentInputs>({ mode: 'onChange' })
  const dispatch = useAppDispatch()
  const listProjects = useAppSelector(selectListProjects)
  const projectSearchText = useAppSelector(selectProjectSearchText)
  const projectPage = useAppSelector(selectProjectPage)
  const [isFetchingMoreProject, setIsFetchingMoreProject] =
    useState<boolean>(false)
  const agentNameValue = watch('name')
  const providerValue = watch('provider')

  const {
    //custom hook for handling lazy scrolling
    fetchMoreFunc: fetchMoreProjectList,
    noMoreData,
    markAsMounted
  } = useLazyScroll({
    currentPage: projectPage,
    fetchDataFunc: () => handleFetchProjectList(),
    onPageChange: (page) => dispatch(setPage(page)),
    searchText: projectSearchText.trim()
  })

  //trigger functin to add new agent
  const handleAddNewAgent = async (data: AddAgentInputs) => {
    const addAgentAction = await dispatch(createAgentThunk(data))
    if (createAgentThunk.rejected.match(addAgentAction)) {
      if (addAgentAction.payload === 'duplicated key not allowed') {
        setError('name', {
          type: 'duplicate_name',
          message: 'An agent with this name already exists'
        })
        return
      }

      setError('name', {
        type: 'add_agent',
        message: renderUnauthorizedMessage(addAgentAction.payload as string)
      })
      setError('project_id', {
        type: 'add_agent',
        message: renderUnauthorizedMessage(addAgentAction.payload as string)
      })
      setError('provider', {
        type: 'add_agent',
        message: renderUnauthorizedMessage(addAgentAction.payload as string)
      })
      return
    }
    onCancel()
    setApiKeyModalOpen(true)
  }

  //fuction to fetch list project api
  const handleFetchProjectList = async () => {
    const action = await dispatch(fetchListProjectThunk('infinte'))
    const payload = action.payload as ListProjectThunkPayload
    markAsMounted()
    setIsFetchingMoreProject(false)
    if (
      payload.data.page_no > Math.floor(payload.data.total / DEFAULT_PAGE_SIZE)
    ) {
      noMoreData()
    }
  }

  //only for data clearing when unmount
  useEffect(() => {
    return () => {
      dispatch(setProjectSearchText(''))
    }
  }, [])

  return (
    <form
      onSubmit={handleSubmit(handleAddNewAgent)}
      className="w-full h-full flex-col flex gap-2"
    >
      <FormInputContainer<AddAgentInputs>
        label={'Name'}
        name="name"
        control={control}
        required
        maxCharactersText={`${agentNameValue?.length ?? 0}/50 characters`}
        vertialAlign
        errors={errors}
        rules={{
          required: 'Required',
          pattern: {
            value: REGEX_PREVENT_ONLY_WHITESPACES,
            message: 'Name cannot contain only spaces'
          }
        }}
        render={({ field }) => (
          <TextInputComponent
            maxLength={50}
            placeholder="Enter agent's name"
            {...field}
          />
        )}
      />
      <FormInputContainer<AddAgentInputs>
        label={'Provider'}
        name="provider"
        control={control}
        required
        maxCharactersText={`${providerValue?.length ?? 0}/50 characters`}
        vertialAlign
        errors={errors}
        rules={{
          required: 'Required',
          pattern: {
            value: REGEX_PREVENT_ONLY_WHITESPACES,
            message: 'Name cannot contain only spaces'
          }
        }}
        render={({ field }) => (
          <TextInputComponent
            maxLength={50}
            placeholder="Enter Provider's name"
            {...field}
          />
        )}
      />

      <FormInputContainer<AddAgentInputs>
        label={'Project'}
        name="project_id"
        control={control}
        required
        vertialAlign
        errors={errors}
        rules={{
          required: 'Required'
        }}
        render={({ field }) => (
          <LazyScrollSelectComponent<AddAgentInputs>
            field={field}
            options={listProjects.map((project) => ({
              label: project?.name,
              value: project?.id
            }))}
            fetchMoreData={fetchMoreProjectList}
            onSearch={(e) => {
              setIsFetchingMoreProject(true)
              dispatch(setProjectSearchText(e.target.value))
            }}
            isFetchMore={isFetchingMoreProject}
          />
        )}
      />

      <div className="flex flex-wrap sm:flex-nowrap justify-end items-center ml-auto gap-3 w-full py-4 sticky bottom-0">
        <SecondaryButton className="sm:max-w-[200px]" onClick={onCancel}>
          Cancel
        </SecondaryButton>
        <PrimaryButton
          className="sm:max-w-[200px]"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          type="submit"
        >
          Generate API Key
        </PrimaryButton>
      </div>
    </form>
  )
}

export default AddAgentModal
