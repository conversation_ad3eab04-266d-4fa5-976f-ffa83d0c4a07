import { useAppSelector } from '../../../stores/hooks'
import {
  selectAgentLoading,
  selectListAgent
} from '../../../stores/Reducers/agentReducers'
import { Skeleton } from 'antd'
import NoDataComponent from '../../../components/NoDataComponent'

import AgentItemComponent from './agent-item-component'
import { useState } from 'react'

type Props = {
  setApiKeyModalOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const ListAgentComponent = ({ setApiKeyModalOpen }: Props) => {
  const listAgent = useAppSelector(selectListAgent)
  const agentLoading = useAppSelector(selectAgentLoading)
  const [agentExtended, setAgentExtended] = useState<string>('')

  if (agentLoading) {
    return (
      <>
        <Skeleton />
        <Skeleton />
      </>
    )
  }
  if (listAgent.length === 0) {
    return <NoDataComponent />
  }
  return (
    <div className="w-full flex-1 flex flex-col gap-3 overflow-auto pr-2">
      {listAgent.map((agent) => (
        <AgentItemComponent
          setApiKeyModalOpen={setApiKeyModalOpen}
          agentExtended={agentExtended}
          setAgentExtended={setAgentExtended}
          key={agent.id}
          agent={agent}
        />
      ))}
    </div>
  )
}

export default ListAgentComponent
