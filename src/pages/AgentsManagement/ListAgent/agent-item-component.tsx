import { Fragment, useRef, useState } from 'react'
import { IAgent } from '../../../models/agents'
import ConfirmModal from '../../../components/ConfirmModal'
import CustomModal from '../../../components/Modal'
import CameraDetailComponent from '../../../components/CameraDetails'

import DropdownMenus from '../../../components/DropdownMenu'
import ListCameraTable from '../../../components/ListCameraTable'
import PaginationComponent from '../../../components/Pagination'
import { classNames, DEFAULT_PAGE_SIZE } from '../../../utils'
import {
  ChevronDownIcon,
  ChevronUpIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/20/solid'
import StatusBadgeComponent from '../../../components/StatusBadge'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  selectListCamera,
  selectTotalCamera
} from '../../../stores/Reducers/cameraReducers'
import {
  deleteAgentThunk,
  fetchListCameraByAgentThunk,
  setApiKey
} from '../../../stores/Reducers/agentReducers'
import toast from 'react-hot-toast'
import AgentItemActions from './agent-item-actions'
type Props = {
  agent: IAgent
  setApiKeyModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  agentExtended: string
  setAgentExtended: React.Dispatch<React.SetStateAction<string>>
}

const AgentItemComponent = ({
  agent,
  setApiKeyModalOpen,
  agentExtended,
  setAgentExtended
}: Props) => {
  const [confirmDeleteModalOpen, setConfirmDeleteModalOpen] =
    useState<boolean>(false)
  const dispatch = useAppDispatch()
  const [isCreateFormDirty, setIsCreateFormDirty] = useState<boolean>(false)
  const [agentCamerasLoading, setAgentCamerasLoading] = useState<boolean>(false)
  const [cameraPage, setCameraPage] = useState<number>(1)
  const totalCamera = useAppSelector(selectTotalCamera)
  const agentCameras = useAppSelector(selectListCamera)
  const [addCameraModalOpen, setAddCameraModalOpen] = useState<boolean>(false)
  const [isDeleting, setIsDeleting] = useState<boolean>(false)
  const selectedAgent = useRef<IAgent>()

  const handleExtendAgents = () =>
    agentExtended.includes(agent.id)
      ? setAgentExtended('')
      : setAgentExtended(agent.id)

  const handleRefetchCameraAfterDelete = () =>
    agentCameras.length === 1 && cameraPage > 1
      ? onCameraPageChange(cameraPage - 1)
      : fetchListCameraByAgent(cameraPage, agentExtended)

  //fetch list camera when change agent
  const handleViewCamerasAgent = () => {
    setCameraPage(1) // set Camera page to 1 to show correct page on the pagination
    handleExtendAgents() // set extened agent id to show which agent is selected
    if (!agentExtended.includes(agent.id)) {
      fetchListCameraByAgent(1, agent.id)
    }
    //wont refetch the camera list when user collapse an agent
  }

  const onCameraPageChange = (page: number) => {
    fetchListCameraByAgent(page, agentExtended)
    setCameraPage(page) // set Camera page to show correct page on the pagination
  }

  const handleDeleteAgent = async () => {
    setIsDeleting(true)
    const deleteAgentAction = await dispatch(
      deleteAgentThunk(selectedAgent?.current?.id)
    )
    setIsDeleting(false)
    if (deleteAgentThunk.fulfilled.match(deleteAgentAction)) {
      toast.success('Agent ' + selectedAgent?.current?.name + ' is deleted!', {
        position: 'top-right'
      })
      setConfirmDeleteModalOpen(false)
    }
  }

  const fetchListCameraByAgent = async (page: number, agent_ids: string) => {
    setAgentCamerasLoading(true)
    const data = {
      page: page,
      agent_ids: [agent_ids]
    }
    await dispatch(fetchListCameraByAgentThunk(data))
    setAgentCamerasLoading(false)
  }

  const agentOptions = (agent: IAgent) => [
    {
      id: 'download',
      text: 'Download binary file',
      onClick: () => {
        setApiKeyModalOpen(true)
        dispatch(setApiKey(agent.api_keys[0]?.api_key ?? ''))
      }
    },
    {
      id: 'create_camera',
      text: 'Create Camera',
      onClick: () => {
        setAddCameraModalOpen(true)
        selectedAgent.current = agent
      }
    },
    {
      id: 'delete_camera',
      text: 'Delete Camera',
      onClick: () => {
        setConfirmDeleteModalOpen(true)
        selectedAgent.current = agent
      }
    }
  ]

  return (
    <Fragment>
      <div
        className={classNames(
          'px-3 sm:px-6 flex cursor-pointer items-center gap-4 w-full rounded-xl border-[#E3E3E3] border border-solid',
          agentExtended.includes(agent.id) ? 'bg-[#F7F3FF]' : 'bg-white'
        )}
        onClick={() => handleViewCamerasAgent()}
      >
        <div className="flex flex-col gap-3 flex-1 py-3">
          <div className="flex items-center truncate gap-2 flex-1">
            {agentExtended.includes(agent.id) ? (
              <ChevronUpIcon className="min-w-5 min-h-5 w-5 h-5" />
            ) : (
              <ChevronDownIcon className="min-w-5 min-h-5 w-5 h-5" />
            )}
            <p className="font-semibold w-fit truncate">{agent.name}</p>
            <StatusBadgeComponent type={agent.status.toLocaleLowerCase()} />
          </div>
          <div className="flex flex-col sm:flex-row gap-2 pl-7 truncate">
            <p className="text-xs text-gray-500 font-semibold truncate max-w-[300px]">
              Project:{' '}
              <span
                title={agent.project.name}
                className="text-gray-900 font-normal max-w-[200px] truncate"
              >
                {agent.project.name}
              </span>
            </p>
            <div className="min-w-[1px] flex border-[0.7px] border-solid border-gray-400 my-[1px]" />
            <p className="text-xs text-gray-500 font-semibold truncate max-w-[300px]">
              Provider:{' '}
              <span
                title={agent.provider}
                className="text-gray-900 font-normal max-w-[200px] truncate"
              >
                {agent.provider}
              </span>
            </p>
          </div>
        </div>
        <AgentItemActions
          agent={agent}
          onClickDelete={(event) => {
            event.stopPropagation()
            setConfirmDeleteModalOpen(true)
            selectedAgent.current = agent
          }}
          onClickAddCamera={(event) => {
            event.stopPropagation()
            setAddCameraModalOpen(true)
            selectedAgent.current = agent
          }}
          setApiKeyModalOpen={setApiKeyModalOpen}
        />
        <div className="sm:hidden">
          <DropdownMenus
            label={
              <EllipsisVerticalIcon aria-hidden="true" className="h-5 w-5" />
            }
            disableTransition
            popupDirection="left"
            className="block sm:hidden ring-white shadow-white"
            menus={agentOptions(agent)}
          />
        </div>
      </div>
      <div
        className={`flex flex-col transition-all duration-200 transform overflow-hidden ${
          agentExtended === agent.id
            ? 'max-h-[500px] min-h-[400px] translate-y-0'
            : 'max-h-0 opacity-0'
        }`}
      >
        {agentExtended === agent.id && (
          <ListCameraTable
            listCamera={agentCameras}
            loading={agentCamerasLoading}
            page={cameraPage}
            refetchAgentCamera={() =>
              fetchListCameraByAgent(cameraPage, agentExtended)
            }
            refetchCameraListAfterDelete={handleRefetchCameraAfterDelete}
          />
        )}
        <PaginationComponent
          totalItems={totalCamera}
          onPageChange={(page) => onCameraPageChange(page)}
          currentPage={cameraPage}
          pageSize={DEFAULT_PAGE_SIZE}
        />
      </div>
      <ConfirmModal
        onConfirm={handleDeleteAgent}
        disable={isDeleting}
        title="Delete this agent?"
        openState={[confirmDeleteModalOpen, setConfirmDeleteModalOpen]}
        text={`You are deleting agent ${selectedAgent.current?.name}.`}
      />
      <CustomModal
        className="min-w-[50vw] max-w-[50vw]"
        isModalDirty={isCreateFormDirty}
        title="Add Camera"
        openState={[addCameraModalOpen, setAddCameraModalOpen]}
      >
        <CameraDetailComponent
          isFormDirty={isCreateFormDirty}
          setIsFormDirty={setIsCreateFormDirty}
          defaultAgent={selectedAgent.current}
          refetchListCamera={() => {
            fetchListCameraByAgent(1, agentExtended)
            setCameraPage(1)
          }}
          setModalOpen={setAddCameraModalOpen}
        />
      </CustomModal>
    </Fragment>
  )
}

export default AgentItemComponent
