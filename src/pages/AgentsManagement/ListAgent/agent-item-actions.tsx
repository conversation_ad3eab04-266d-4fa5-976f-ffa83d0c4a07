import { ArrowDownCircleIcon } from '@heroicons/react/24/solid'
import { Tooltip } from 'antd'
import TrashIcon from '../../../assets/svgs/TrashIcon'
import PlusIcon from '../../../assets/svgs/PlusIcon'
import { useAppDispatch } from '../../../stores/hooks'
import { setApiKey } from '../../../stores/Reducers/agentReducers'
import { IAgent } from '../../../models/agents'
type Props = {
  setApiKeyModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  agent: IAgent
  onClickDelete: (e: React.MouseEvent<SVGSVGElement, MouseEvent>) => void
  onClickAddCamera: (e: React.MouseEvent<SVGSVGElement, MouseEvent>) => void
}

const AgentItemActions = ({
  setApiKeyModalOpen,
  agent,
  onClickDelete,
  onClickAddCamera
}: Props) => {
  const dispatch = useAppDispatch()
  return (
    <div className="hidden sm:flex py-5 items-center w-fit gap-6">
      <Tooltip title="Download Binary File">
        <ArrowDownCircleIcon
          fill="gray"
          width={22}
          height={22}
          className="hover:fill-primary cursor-pointer"
          onClick={(event) => {
            event.stopPropagation()
            setApiKeyModalOpen(true)
            dispatch(setApiKey(agent.api_keys[0]?.api_key ?? ''))
          }}
        />
      </Tooltip>
      <Tooltip title="Delete">
        <TrashIcon
          width={20}
          height={20}
          className="hover:fill-primary cursor-pointer"
          onClick={onClickDelete}
        />
      </Tooltip>
      <Tooltip title="Add new camera">
        <PlusIcon
          width={20}
          height={20}
          className="hover:fill-primary cursor-pointer"
          onClick={onClickAddCamera}
        />
      </Tooltip>
    </div>
  )
}

export default AgentItemActions
