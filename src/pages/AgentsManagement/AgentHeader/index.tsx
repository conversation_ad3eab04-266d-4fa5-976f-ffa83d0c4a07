import React, { useEffect, useState } from 'react'
import PageHeaderText from '../../../components/PageHeaderText'
import DropdownOptions from '../../../components/DropdownOptions'
import TextInputComponent from '../../../components/TextInputComponent'
import SearchIcon from '../../../assets/svgs/SearchIcon'
import PrimaryButton from '../../../components/PrimaryButtons'
import { IOption } from '../../../interfaces'
import AddAgentModal from '../AddAgentModal'
import CustomModal from '../../../components/Modal'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  fetchListAgentThunk,
  selectAgentOrderBy,
  selectAgentPage,
  selectAgentProjectIds,
  selectAgentSearchText,
  setAgentOrderBy,
  setAgentPage,
  setAgentProjectIds,
  setAgentSearchText
} from '../../../stores/Reducers/agentReducers'
import { useDebounce } from '../../../utils/hooks/useDebounce'
import SecondaryButton from '../../../components/SecondaryButtons'
import AgentProjectFilter from './filter-project-modal'
import FilterIcon from '../../../assets/svgs/FilterIcon'
import ActiveFiltersComponent from '../../../components/ActiveFiltersComponent'

type Props = {
  setApiKeyModalOpen: React.Dispatch<React.SetStateAction<boolean>>
}
const options: IOption[] = [
  { value: 'created_at-desc', label: 'Newest to oldest' },
  { value: 'created_at-asc', label: 'Oldest to latest' },
  { value: 'name-asc', label: 'Agent name (A-Z)' },
  { value: 'name-desc', label: 'Agent name (Z-A)' },
  { value: 'status-asc', label: 'Status (Online first)' },
  { value: 'status-desc', label: 'Status (Offline first)' }
]
const AgentHeaderComponent = ({ setApiKeyModalOpen }: Props) => {
  const [addAgentModalOpen, setAddAgentModalOpen] = useState<boolean>(false)
  const appliedProjects = useAppSelector(selectAgentProjectIds)
  const [filterModalOpen, setFilterModalOpen] = useState<boolean>(false)
  const dispatch = useAppDispatch()
  const agentSearchText = useAppSelector(selectAgentSearchText)
  const agentPage = useAppSelector(selectAgentPage)
  const agentOrderBy = useAppSelector(selectAgentOrderBy)

  const fetchListAgents = async () => {
    await dispatch(fetchListAgentThunk())
    isMounted.current = true
  }

  //debouce when calling list agents api with search
  const { isMounted } = useDebounce({
    func: () => fetchListAgentsWithFilter(),
    searchText: agentSearchText
  })
  const fetchListAgentsWithFilter = async () => {
    if (agentPage > 1) {
      dispatch(setAgentPage(1))
      return
    }
    await dispatch(fetchListAgentThunk())
  }

  const onRemoveProjectFilters = (project: IOption) => {
    dispatch(
      setAgentProjectIds(
        appliedProjects.filter(
          (appliedProject) => appliedProject.value !== project.value
        )
      )
    )
  }

  useEffect(() => {
    fetchListAgents()
  }, [agentPage])

  useEffect(() => {
    if (!isMounted.current) return
    fetchListAgentsWithFilter()
  }, [appliedProjects, agentOrderBy])

  return (
    <>
      <div className="flex flex-col sm:flex-row flex-wrap w-full items-center justify-between mb-4 gap-4">
        <div className="flex flex-wrap w-full gap-2 justify-between">
          <PageHeaderText>Agent Management</PageHeaderText>
          <PrimaryButton
            onClick={() => setAddAgentModalOpen(true)}
            className="sm:max-w-[200px] py-[10px]"
          >
            Add new agent
          </PrimaryButton>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 w-full flex-1 items-center">
          <TextInputComponent
            prefixIcon={<SearchIcon />}
            onChange={(e) => dispatch(setAgentSearchText(e.target.value))}
            placeholder="Search..."
          />
          <DropdownOptions
            label={'Sort by'}
            type="radio"
            options={options}
            selected={agentOrderBy}
            className="sm:max-w-[200px] w-full"
            onSelect={(value) => dispatch(setAgentOrderBy(value))}
          />
          <SecondaryButton
            className={'justify-between sm:max-w-[200px]'}
            backgroundColor="#f4f4f4"
            onClick={() => setFilterModalOpen(true)}
          >
            <div className="flex items-center gap-2">
              <FilterIcon />
              <p className="font-normal">Filters</p>
            </div>
          </SecondaryButton>
        </div>
        {appliedProjects.length > 0 && (
          <ActiveFiltersComponent
            listActiveFilters={[appliedProjects].flat()}
            onRemoveFilter={onRemoveProjectFilters}
            clearAllFilter={() => dispatch(setAgentProjectIds([]))}
          />
        )}
      </div>

      <CustomModal
        title={'Add a new agent'}
        openState={[addAgentModalOpen, setAddAgentModalOpen]}
      >
        <AddAgentModal
          setApiKeyModalOpen={setApiKeyModalOpen}
          onCancel={() => setAddAgentModalOpen(false)}
        />
      </CustomModal>
      <CustomModal
        openState={[filterModalOpen, setFilterModalOpen]}
        title={'Filter by project'}
      >
        <AgentProjectFilter onCancel={() => setFilterModalOpen(false)} />
      </CustomModal>
    </>
  )
}

export default AgentHeaderComponent
