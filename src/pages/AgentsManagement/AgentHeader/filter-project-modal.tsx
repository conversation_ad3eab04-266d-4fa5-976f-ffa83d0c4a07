import { useEffect, useState } from 'react'
import { getListProjects } from '../../../api/Projects'
import { IProject, ListProjectQuerys } from '../../../models/projects'
import { DEFAULT_PAGE_SIZE, ENV } from '../../../utils'
import SecondaryButton from '../../../components/SecondaryButtons'
import PrimaryButton from '../../../components/PrimaryButtons'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  selectAgentProjectIds,
  setAgentProjectIds
} from '../../../stores/Reducers/agentReducers'
import TextInputComponent from '../../../components/TextInputComponent'
import SearchIcon from '../../../assets/svgs/SearchIcon'
import PaginationComponent from '../../../components/Pagination'
import { useDebounce } from '../../../utils/hooks/useDebounce'
import { IOption } from '../../../interfaces'

type Props = {
  onCancel: VoidFunction
}

const AgentProjectFilter = ({ onCancel }: Props) => {
  const [listProject, setListProject] = useState<IProject[]>([])
  const [projectPage, setProjectPage] = useState<number>(1)
  const [projectSearchText, setProjectSearchText] = useState<string>('')
  const [totalProject, setTotalProject] = useState<number>(0)
  const appliedProjects = useAppSelector(selectAgentProjectIds)
  const [selectedProjects, setSelectedProjects] =
    useState<IOption[]>(appliedProjects)
  const dispatch = useAppDispatch()

  const fetchListVA = async () => {
    const data: ListProjectQuerys = {
      'page-no': projectPage,
      'page-size': DEFAULT_PAGE_SIZE,
      name: projectSearchText
    }
    try {
      const res = await getListProjects(data)
      setListProject(res.data)
      setTotalProject(res.total)
    } catch (e) {
      if (ENV === 'DEV') {
        console.log(e)
      }
      setListProject([])
    } finally {
      isMounted.current = true
    }
  }

  const onSelectProject = (project: IProject) => {
    if (
      selectedProjects.some(
        (selectedProject) => selectedProject.value === project.id
      )
    ) {
      setSelectedProjects(
        selectedProjects.filter(
          (selectedProject) => selectedProject.value !== project.id
        )
      )
    } else {
      setSelectedProjects([
        ...selectedProjects,
        {
          label: project.name,
          value: project.id
        }
      ])
    }
  }

  const onApplyProject = () => {
    dispatch(setAgentProjectIds(selectedProjects))
    onCancel()
  }

  const { isMounted } = useDebounce({
    func: () => (projectPage > 1 ? setProjectPage(1) : fetchListVA()),
    searchText: projectSearchText
  })

  useEffect(() => {
    fetchListVA()
  }, [projectPage])

  return (
    <div className="w-full flex flex-col h-full">
      <TextInputComponent
        prefixIcon={<SearchIcon />}
        onChange={(e) => setProjectSearchText(e.target.value)}
        placeholder="Search by project's name"
      />
      <div className="mt-4 min-h-[150px] flex-1 grid grid-cols-1 md:grid-cols-2 divide-y max-h-[300px] w-full overflow-auto divide-gray-200 border-b border-t border-gray-200">
        {listProject.map((project) => (
          <div
            key={project.id}
            className="relative gap-4 flex flex-row-reverse items-start py-1"
          >
            <div className="min-w-0 flex-1 text-sm leading-6">
              <label
                htmlFor={`project-${project.id}`}
                className="select-none leading-[22px] text-mainBlack"
              >
                {project.name}
              </label>
            </div>
            <div className="ml-3 flex h-6 items-center">
              <input
                id={`project-${project.id}`}
                name={`project-${project.id}`}
                defaultChecked={selectedProjects.some(
                  (selectedProject) => selectedProject.value === project.id
                )}
                onChange={() => onSelectProject(project)}
                type="checkbox"
                className="h-4 w-4 rounded border-solid border-gray-300 text-primary focus:ring-primary"
              />
            </div>
          </div>
        ))}
      </div>
      <PaginationComponent
        totalItems={totalProject}
        currentPage={projectPage}
        onPageChange={setProjectPage}
        pageSize={DEFAULT_PAGE_SIZE}
      />
      <div className="flex items-center gap-4 py-4 sticky bottom-0 w-full justify-end">
        <SecondaryButton className="sm:max-w-[250px]" onClick={onCancel}>
          Cancel
        </SecondaryButton>
        <PrimaryButton className="sm:max-w-[250px]" onClick={onApplyProject}>
          Apply
        </PrimaryButton>
      </div>
    </div>
  )
}

export default AgentProjectFilter
