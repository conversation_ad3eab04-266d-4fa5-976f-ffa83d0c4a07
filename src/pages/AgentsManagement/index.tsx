import { useState } from 'react'
import { DEFAULT_PAGE_SIZE } from '../../utils'
import CustomModal from '../../components/Modal'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import {
  selectAgentPage,
  selectTotalAgent,
  setAgentPage
} from '../../stores/Reducers/agentReducers'

import DownloadBinaryFileModal from './DownloadBinaryModal'
import PaginationComponent from '../../components/Pagination'
import AgentHeaderComponent from './AgentHeader'
import ListAgentComponent from './ListAgent'

const AgentManagementPage = () => {
  const agentPage = useAppSelector(selectAgentPage)
  const [apiKeyModalOpen, setApiKeyModalOpen] = useState<boolean>(false)
  const dispatch = useAppDispatch()
  const totalAgent = useAppSelector(selectTotalAgent)

  return (
    <div className="h-full w-full flex flex-col overflow-auto">
      <AgentHeaderComponent setApiKeyModalOpen={setApiKeyModalOpen} />

      <ListAgentComponent setApiKeyModalOpen={setApiKeyModalOpen} />

      <PaginationComponent
        totalItems={totalAgent}
        onPageChange={(page) => dispatch(setAgentPage(page))}
        currentPage={agentPage}
        pageSize={DEFAULT_PAGE_SIZE}
      />

      <CustomModal
        title={'Installation Guide'}
        openState={[apiKeyModalOpen, setApiKeyModalOpen]}
      >
        <DownloadBinaryFileModal closeModal={() => setApiKeyModalOpen(false)} />
      </CustomModal>
    </div>
  )
}

export default AgentManagementPage
