import TextInputComponent from '../../components/TextInputComponent'
import { useAppDispatch, useAppSelector } from '../../stores/hooks'
import {
  selectCurrentUser,
  updateUserEmailSetting
} from '../../stores/Reducers/usersReducer'
import PrimaryButton from '../../components/PrimaryButtons'
import { Switch } from 'antd'
import { updateNotificationSettings } from '../../api/Notifications'
import { toast } from 'react-hot-toast'
import { useEffect, useState } from 'react'
import { getErrorMessage } from '../../utils'
import {
  getNotificationSettingsThunk,
  selectNotificationSettings
} from '../../stores/Reducers/notificationsReducer'
import { Link } from 'react-router-dom'
import { ROUTE_PATH } from '../../enum/RoutePath'

const UserProfile = () => {
  const userProfile = useAppSelector(selectCurrentUser)
  const [loading, setLoading] = useState<boolean>(false)
  const notificationSettings = useAppSelector(selectNotificationSettings)
  const dispatch = useAppDispatch()
  const handleToggleEmailNotification = async (checked: boolean) => {
    setLoading(true)
    try {
      await updateNotificationSettings({
        ...notificationSettings,
        camera_email_notification: checked
      })
      toast.success(
        `Notification about your camera feed will ${
          checked ? 'now' : 'not'
        } be sent to your email`
      )
      dispatch(updateUserEmailSetting(checked))
    } catch (error) {
      toast.error(getErrorMessage(error))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    dispatch(getNotificationSettingsThunk())
  }, [])

  return (
    <div className="w-full h-full flex-1 flex flex-col gap-4">
      <p className="font-bold text-3xl leading-[38px]">Profile</p>

      <div className="flex flex-col gap-2">
        <label htmlFor="name" className="text-base font-medium">
          Name
        </label>
        <TextInputComponent
          id="name"
          disabled
          defaultValue={userProfile?.name}
        />
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="email" className="text-base font-medium">
          Email
        </label>
        <TextInputComponent
          id="email"
          disabled
          defaultValue={userProfile?.email}
        />
      </div>
      <div className="flex flex-col gap-2">
        <label htmlFor="group" className="text-base font-medium">
          Group
        </label>
        <TextInputComponent
          id="group"
          disabled
          defaultValue={userProfile?.groups
            .map((group) => group.name)
            .join(', ')}
        />
      </div>
      <div className="flex flex-col gap-2">
        <label className="text-base font-medium">Password</label>
        <Link to={ROUTE_PATH.Change_Password}>
          <PrimaryButton className="max-w-[200px]">
            Change password
          </PrimaryButton>
        </Link>
      </div>
      <div className="flex flex-col gap-2">
        <label htmlFor="email_notification" className="text-base font-medium">
          Email Notification
        </label>
        <p className="text-sm">
          Notify me when a camera feed on my dashboard goes live{' '}
          <span>
            <Switch
              checked={
                userProfile?.setting?.Settings?.camera_email_notification
              }
              disabled={loading}
              loading={loading}
              onChange={(e) => handleToggleEmailNotification(e)}
            />
          </span>
        </p>
      </div>
    </div>
  )
}

export default UserProfile
