import { Skeleton, Switch } from 'antd'
import PrimaryButton from '../../../../components/PrimaryButtons'
import SecondaryButton from '../../../../components/SecondaryButtons'
import { IOption } from '../../../../interfaces'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  DEFAULT_NOTIFICATION_SETTINGS,
  NOTIFICATION_EVENTS,
  getNotificationSettingsThunk,
  selectNotificationSettingLoading,
  selectNotificationSettings,
  updateNotiSettings,
  updateNotificationSettingsThunk
  // selectNotificationSettings
} from '../../../../stores/Reducers/notificationsReducer'
import { useCallback, useEffect, useState } from 'react'
import { INotificationSettingsDetail } from '../../../../models/notifications'

type Props = {
  closeModal: () => void
}

const NotificationSettingModal = ({ closeModal }: Props) => {
  const notificationSettingLoading = useAppSelector(
    selectNotificationSettingLoading
  )
  const notificationSettings = useAppSelector(selectNotificationSettings)
  const dispatch = useAppDispatch()
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false)

  const getNotiSettingGroup = (
    obj: INotificationSettingsDetail | undefined,
    condition: (key: string, value: string) => boolean
  ): object => {
    if (!obj) return {}
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([key, value]) => condition(key, value) && !key.includes('camera_email')
        // not include camera email setting because it has its separated setting in profile page
      )
    )
  }

  const checkSettingGroupStatus = useCallback(
    (event_type: IOption): boolean | undefined => {
      if (notificationSettings === undefined) return

      const defaultSettingGroup = getNotiSettingGroup(
        DEFAULT_NOTIFICATION_SETTINGS,
        (key) => key.includes(event_type.value)
      )
      const settingGroup = getNotiSettingGroup(notificationSettings, (key) =>
        key.includes(event_type.value)
      )
      if (
        Object.keys(defaultSettingGroup).length !==
        Object.keys(settingGroup).length
      ) {
        return false
      }

      return Object.values(settingGroup).every((item) => item === true)
    },
    [notificationSettings]
  )

  const handleChangeSettingGroup = (event_type: IOption, status: boolean) => {
    // Initialize newSettings with the correct type
    const newSettings: INotificationSettingsDetail = {
      ...DEFAULT_NOTIFICATION_SETTINGS,
      ...notificationSettings
    }

    // Iterate through the keys in DEFAULT_NOTIFICATION_SETTINGS
    if (!newSettings) {
      return
    }

    for (const setting in DEFAULT_NOTIFICATION_SETTINGS) {
      if (
        setting.includes(event_type.value) &&
        !setting.includes('camera_email_notification')
        // not include camera email setting because it has its separated setting in profile page
      ) {
        newSettings[setting as keyof INotificationSettingsDetail] = status
      }
    }

    // Dispatch the updated settings
    dispatch(updateNotiSettings(newSettings))
  }

  const onSaveNotificationSettings = async () => {
    setIsSubmiting(true)
    const saveSettingAction = await dispatch(
      updateNotificationSettingsThunk(notificationSettings)
    )
    setIsSubmiting(false)
    if (updateNotificationSettingsThunk.rejected.match(saveSettingAction)) {
      return
    }
    closeModal()
  }

  useEffect(() => {
    dispatch(getNotificationSettingsThunk())
  }, [])

  if (notificationSettingLoading) {
    return (
      <>
        <Skeleton />
        <Skeleton />
      </>
    )
  }

  return (
    <div className="w-full h-full flex flex-col gap-6">
      <div className="flex flex-col gap-5">
        {NOTIFICATION_EVENTS.map((event) => (
          <div
            key={event.value}
            className="flex items-center gap-4 justify-between"
          >
            <label className="text-sm font-medium" htmlFor={event.value}>
              {event.label}
            </label>
            <Switch
              onChange={(e) => handleChangeSettingGroup(event, e)}
              checked={checkSettingGroupStatus(event)}
            />
          </div>
        ))}
      </div>
      <div className="flex ml-auto gap-4 justify-end sticky bottom-0 pb-4 bg-white w-full">
        <SecondaryButton className="sm:max-w-[200px]" onClick={closeModal}>
          Cancel
        </SecondaryButton>
        <PrimaryButton
          className="sm:max-w-[200px]"
          isLoading={isSubmiting}
          isDisabled={isSubmiting}
          onClick={onSaveNotificationSettings}
        >
          Save
        </PrimaryButton>
      </div>
    </div>
  )
}

export default NotificationSettingModal
