import { Cog6ToothIcon } from '@heroicons/react/24/outline'
import { memo, useCallback, useState } from 'react'
import CustomModal from '../../../components/Modal'
import NotificationSettingModal from './NotificationSettingModal'
import PageHeaderText from '../../../components/PageHeaderText'

const NotificationHeader = memo(() => {
  const [openSettingModal, setOpenSettingModal] = useState<boolean>(false)
  const closeModal = useCallback(() => setOpenSettingModal(false), [])

  return (
    <>
      <div className="w-full gap-2 flex items-center">
        <PageHeaderText>Notifications</PageHeaderText>
        <Cog6ToothIcon
          onClick={() => setOpenSettingModal(true)}
          cursor={'pointer'}
          height={20}
          width={20}
        />
      </div>

      <CustomModal
        openState={[openSettingModal, setOpenSettingModal]}
        title={'Notification settings'}
        className="!max-w-[500px] sm:!min-w-[500px] !w-[500px]"
      >
        <NotificationSettingModal closeModal={closeModal} />
      </CustomModal>
    </>
  )
})

export default NotificationHeader
