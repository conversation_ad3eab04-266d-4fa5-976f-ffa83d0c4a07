import { useEffect, useRef } from 'react'
import NotificationItemComponent from './NotificationItem'
import { DEFAULT_PAGE_SIZE, classNames } from '../../../utils'
import { LoaderIcon } from 'react-hot-toast'
import { useInfiniteScroll } from '../../../utils/hooks/useInfiniteScroll'
import { useAppDispatch, useAppSelector } from '../../../stores/hooks'
import {
  fetchListNotificationThunk,
  increaseNotificationPage,
  resetNotificationPage,
  selectListNotification,
  selectNotificationLoading,
  selectNotificationPage,
  selectSelectedNotifications,
  selectTotalNotification,
  setDeletedAllInPage,
  setSelectedNotifications
} from '../../../stores/Reducers/notificationsReducer'
import NoDataComponent from '../../../components/NoDataComponent'
import { Skeleton } from 'antd'
import NotificationActionsDropdown from './NotificationActionsDropdown'
const ListNotificationComponent = () => {
  const totalNotification = useAppSelector(selectTotalNotification)
  const notificationLoading = useAppSelector(selectNotificationLoading)
  const notificationPage = useAppSelector(selectNotificationPage)
  const listNotification = useAppSelector(selectListNotification)
  const dispatch = useAppDispatch()
  const selectedNotifications = useAppSelector(selectSelectedNotifications)
  const rootDeleteCheckbox = useRef<HTMLInputElement>(null)
  const { loaderRef } = useInfiniteScroll({
    hasMore:
      notificationPage < Math.ceil(totalNotification / DEFAULT_PAGE_SIZE),
    onPageChange: () => {
      dispatch(increaseNotificationPage())
    },
    isFetchingData: notificationLoading
  })

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      dispatch(setDeletedAllInPage())
    } else {
      dispatch(setSelectedNotifications([]))
    }
  }

  useEffect(() => {
    const fetchAction = dispatch(fetchListNotificationThunk(undefined))
    return () => {
      fetchAction.abort()
    }
  }, [notificationPage])

  useEffect(() => {
    return () => {
      dispatch(resetNotificationPage())
    }
  }, [])

  useEffect(() => {
    const isPartialSelected =
      selectedNotifications.length > 0 &&
      selectedNotifications.length !== listNotification.length

    if (rootDeleteCheckbox.current) {
      rootDeleteCheckbox.current.indeterminate = isPartialSelected
    }
  }, [selectedNotifications, listNotification])

  if (listNotification?.length === 0 && !notificationLoading) {
    return <NoDataComponent />
  }

  if (notificationLoading && listNotification?.length === 0) {
    return (
      <>
        <Skeleton />
        <Skeleton />
        <Skeleton />
        <Skeleton />
      </>
    )
  }
  return (
    <>
      <div className="flex items-center gap-3 p-4 border-solid border rounded-lg border-gray-100 bg-gray-50">
        <input
          type="checkbox"
          id="select-all"
          className="w-3 h-3 border border-solid rounded-sm"
          checked={
            selectedNotifications.length > 0 &&
            selectedNotifications.length === listNotification.length
          }
          onChange={handleSelectAll}
          ref={rootDeleteCheckbox}
        />
        <label
          htmlFor="select-all"
          className="text-sm w-fit hover:cursor-pointer font-semibold text-gray-700"
        >
          Select all
        </label>
        <NotificationActionsDropdown />
      </div>
      <div className="flex rounded-2xl flex-1 overflow-auto flex-col divide-solid divide-y divide-x-0 divide-[#E3E3E3] w-full">
        {listNotification?.map((notification) => (
          <NotificationItemComponent
            key={notification.id}
            notification={notification}
          />
        ))}

        <div
          className={classNames(
            'flex justify-center w-full items-center',
            notificationLoading && 'min-h-[40px]'
          )}
          ref={loaderRef}
        >
          {notificationLoading && <LoaderIcon className="min-h-4 min-w-4" />}
        </div>
      </div>
    </>
  )
}

export default ListNotificationComponent
