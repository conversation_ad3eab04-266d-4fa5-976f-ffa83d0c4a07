import {
  <PERSON>u,
  <PERSON>uButton,
  Menu<PERSON>tem,
  MenuItems,
  MenuSeparator
} from '@headlessui/react'
import {
  CheckIcon,
  ChevronDownIcon,
  TrashIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { Menu as MenuType } from '../../../../interfaces'
import { useAppSelector } from '../../../../stores/hooks'
import {
  seenAllNotificationThunk,
  setSelectedNotifications,
  selectSelectedNotifications,
  deleteAllNotificationThunk,
  deleteMultipleNotificationThunk,
  seenMultipleNotificationsThunk
} from '../../../../stores/Reducers/notificationsReducer'
import DoubleCheckIcon from '../../../../assets/svgs/DoubleCheckIcon'
import { useAppDispatch } from '../../../../stores/hooks'
import { classNames } from '../../../../utils'
import { useState } from 'react'
import ConfirmModal from '../../../../components/ConfirmModal'

const NotificationActionsDropdown = () => {
  const dispatch = useAppDispatch()
  const selectedNotifications = useAppSelector(selectSelectedNotifications)
  const [confirmDelete, setConfirmDelete] = useState(false)
  const [confirmDeleteAll, setConfirmDeleteAll] = useState(false)
  const menus: MenuType[] = [
    {
      text: (
        <span className="flex items-center gap-2">
          <CheckIcon height={16} width={16} />
          Mark as read {selectedNotifications.length} items
        </span>
      ),
      onClick: () =>
        dispatch(seenMultipleNotificationsThunk(selectedNotifications)),
      id: 'mark-as-read',
      disabled: selectedNotifications.length === 0
    },
    {
      text: (
        <span className="flex items-center gap-2">
          <DoubleCheckIcon height={16} width={16} />
          Mark all as read
        </span>
      ),
      onClick: () => dispatch(seenAllNotificationThunk()),
      id: 'mark-as-read-all'
    },
    {
      text: (
        <MenuSeparator
          className={'bg-gray-300 h-[1px] pointer-events-none -mb-2'}
        />
      ),
      disabled: true,
      onClick: () => {},
      id: 'separator'
    },
    {
      text: (
        <span className="flex items-center gap-2">
          <XCircleIcon height={16} width={16} />
          Unselect all
        </span>
      ),
      onClick: () => dispatch(setSelectedNotifications([])),
      id: 'unselect-all',
      disabled: selectedNotifications.length === 0
    },
    {
      text: (
        <MenuSeparator
          className={'bg-gray-300 h-[1px] pointer-events-none -my-1'}
        />
      ),
      disabled: true,
      onClick: () => {},
      id: 'separator1'
    },
    {
      text: (
        <span className="flex items-center text-red-400 font-medium gap-2">
          <TrashIcon
            height={16}
            width={16}
            strokeWidth={2}
            className="stroke-red-400"
          />
          Delete {selectedNotifications.length} items
        </span>
      ),
      onClick: () => setConfirmDelete(true),
      id: 'delete',
      disabled: selectedNotifications.length === 0
    },
    {
      text: (
        <span className="flex text-red-400 font-medium items-center gap-2">
          <TrashIcon
            height={16}
            width={16}
            strokeWidth={2}
            className="stroke-red-400"
          />
          Delete all
        </span>
      ),
      onClick: () => setConfirmDeleteAll(true),
      id: 'delete-all'
    }
  ]
  const onConfirmDeleteNotifications = async () => {
    const deleteNotificationsAction = await dispatch(
      deleteMultipleNotificationThunk(selectedNotifications)
    )
    if (
      deleteMultipleNotificationThunk.fulfilled.match(deleteNotificationsAction)
    ) {
      setConfirmDelete(false)
    }
  }
  const onConfirmDeleteAllNotifications = async () => {
    const deleteAllNotificationsAction = await dispatch(
      deleteAllNotificationThunk()
    )
    if (
      deleteAllNotificationThunk.fulfilled.match(deleteAllNotificationsAction)
    ) {
      setConfirmDeleteAll(false)
    }
  }
  return (
    <>
      <Menu
        as="div"
        className="relative flex flex-col ml-auto text-left justify-center items-center"
      >
        <MenuButton
          className={
            'shadow-sm ring-1 inline-flex w-full items-center justify-center gap-x-1.5 rounded-2xl cursor-pointer bg-transparent lg:bg-white px-3 py-2 text-sm text-mainBlackring-inset ring-gray-300 lg:hover:bg-gray-50'
          }
        >
          <span className="text-sm items-center gap-1 text-gray-700 flex font-semibold">
            Actions
            <ChevronDownIcon strokeWidth={2} height={16} width={16} />
          </span>
        </MenuButton>

        <MenuItems
          transition
          anchor="bottom end"
          className="absolute mt-2 z-10 bottom-auto w-56 max-h-[200px] overflow-auto origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
        >
          <div className="py-1 h-full overflow-auto">
            {menus.map((menu) => (
              <MenuItem disabled={menu.disabled} key={menu.id}>
                <a
                  onClick={menu.onClick}
                  className={classNames(
                    'block px-4 py-2 text-sm text-black data-[focus]:bg-gray-100 data-[focus]:font-semibold data-[focus]:text-mainBlack hover:cursor-pointer',
                    menu.disabled && 'opacity-50 cursor-not-allowed'
                  )}
                >
                  {menu.text}
                </a>
              </MenuItem>
            ))}
          </div>
        </MenuItems>
      </Menu>
      <ConfirmModal
        onConfirm={onConfirmDeleteNotifications}
        text={`Are you sure you want to delete these ${selectedNotifications.length} notitifications?`}
        openState={[confirmDelete, setConfirmDelete]}
        title={`Delete ${selectedNotifications.length} notifications`}
      />
      <ConfirmModal
        onConfirm={onConfirmDeleteAllNotifications}
        text="Are you sure you want to delete all notifications?"
        openState={[confirmDeleteAll, setConfirmDeleteAll]}
        title="Delete all notifications"
      />
    </>
  )
}

export default NotificationActionsDropdown
