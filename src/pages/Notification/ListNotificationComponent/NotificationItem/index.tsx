import { useMemo } from 'react'
import BadgeComponent from '../../../../components/BadgeComponent'
import PrimaryButton from '../../../../components/PrimaryButtons'
import { INotification } from '../../../../models/notifications'
import { classNames } from '../../../../utils'
import moment from 'moment'
import Markdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { NOTIFICATION_TYPE } from '../../../../enum/Notification'
import { ROUTE_PATH } from '../../../../enum/RoutePath'
import { useAppDispatch, useAppSelector } from '../../../../stores/hooks'
import {
  seenMultipleNotificationsThunk,
  selectSelectedNotifications,
  setSelectedNotifications
} from '../../../../stores/Reducers/notificationsReducer'
import TextInputComponent from '../../../../components/TextInputComponent'
type Props = {
  notification?: INotification
  isPopup?: boolean // if this component is used in the popup notification or not
}

const NotificationItemComponent = ({
  notification,
  isPopup = false
}: Props) => {
  const dispatch = useAppDispatch()
  const deletedNotifications = useAppSelector(selectSelectedNotifications)
  const renderNotificationType: string = useMemo(() => {
    switch (true) {
      case notification?.type.toLocaleLowerCase().includes('user'):
        return 'Users'
      case notification?.type.toLocaleLowerCase().includes('camera'):
        return 'Camera'
      case notification?.type.toLocaleLowerCase().includes('agent'):
        return 'Agent'
      case notification?.type.toLocaleLowerCase().includes('group'):
        return 'Group'
      case notification?.type.toLocaleLowerCase().includes('project'):
        return 'Project'
      case notification?.type.toLocaleLowerCase().includes('va'):
        return 'Video analytics'
      case notification?.type.toLocaleLowerCase().includes('recording'):
        return 'Recording'
      default:
        return ''
    }
  }, [notification?.type])

  const handleClickNotification = (notification_id: string) => {
    dispatch(seenMultipleNotificationsThunk([notification_id]))
    switch (notification?.type) {
      case NOTIFICATION_TYPE.CameraAddedRemoved:
      case NOTIFICATION_TYPE.CameraStatus:
        window.location.href = ROUTE_PATH.Camera
        break

      case NOTIFICATION_TYPE.AgentStatus:
      case NOTIFICATION_TYPE.AgentAddedRemoved:
        window.location.href = ROUTE_PATH.Agents
        break

      case NOTIFICATION_TYPE.VAAdded:
      case NOTIFICATION_TYPE.VAStreamProcess:
        window.location.href = `${ROUTE_PATH.VideoAnalytics}/${notification.reference.reference_id}`
        break

      case NOTIFICATION_TYPE.VARemoved:
        window.location.href = ROUTE_PATH.VideoAnalytics
        break

      case NOTIFICATION_TYPE.UserAcceptance:
      case NOTIFICATION_TYPE.UserAccess:
      case NOTIFICATION_TYPE.UserRequest:
        window.location.href = ROUTE_PATH.Access_Management
        break

      case NOTIFICATION_TYPE.GroupMember:
      case NOTIFICATION_TYPE.GroupMemberRole:
      case NOTIFICATION_TYPE.GroupRemoved:
      case NOTIFICATION_TYPE.GroupAdded:
        window.location.href = ROUTE_PATH.Access_Groups
        break

      case NOTIFICATION_TYPE.ProjectMember:
      case NOTIFICATION_TYPE.ProjectMemberRole:
      case NOTIFICATION_TYPE.ProjectRemoved:
      case NOTIFICATION_TYPE.ProjectAdded:
        window.location.href = ROUTE_PATH.Access_Projects
        break

      case NOTIFICATION_TYPE.RecordingSynchronization:
        window.location.href = `${ROUTE_PATH.Recording}/${notification.reference.reference_id}/recovered`
        break

      case NOTIFICATION_TYPE.RecordingUpload:
        window.location.href = `${ROUTE_PATH.Recording}/null/${notification.reference.reference_id}/uploaded`
        break
      default:
        break
    }
  }

  const buttonText: string = useMemo(() => {
    switch (notification?.type) {
      case NOTIFICATION_TYPE.CameraAddedRemoved:
      case NOTIFICATION_TYPE.CameraStatus:
        return 'Go to Cameras'

      case NOTIFICATION_TYPE.AgentStatus:
      case NOTIFICATION_TYPE.AgentAddedRemoved:
        return 'Go to Agent'

      case NOTIFICATION_TYPE.VAAdded:
      case NOTIFICATION_TYPE.VAStreamProcess:
        return 'View'

      case NOTIFICATION_TYPE.VARemoved:
        return 'Go to Video Analytics'

      case NOTIFICATION_TYPE.UserAcceptance:
      case NOTIFICATION_TYPE.UserAccess:
      case NOTIFICATION_TYPE.UserRequest:
        return 'View all requests'

      case NOTIFICATION_TYPE.GroupMember:
      case NOTIFICATION_TYPE.GroupMemberRole:
      case NOTIFICATION_TYPE.GroupRemoved:
      case NOTIFICATION_TYPE.GroupAdded:
        return 'View all groups'

      case NOTIFICATION_TYPE.ProjectMember:
      case NOTIFICATION_TYPE.ProjectMemberRole:
      case NOTIFICATION_TYPE.ProjectAdded:
      case NOTIFICATION_TYPE.ProjectRemoved:
        return 'View all projects'

      case NOTIFICATION_TYPE.RecordingSynchronization:
      case NOTIFICATION_TYPE.RecordingUpload:
        return 'View recording'
      default:
        return ''
    }
  }, [notification?.type])

  const notificationNameRecordingType = useMemo(() => {
    const recordingNameSplited = notification?.content.split('**')
    const recordingNameInNotification = recordingNameSplited?.[1] ?? ''
    return `${recordingNameSplited?.[0]}**${
      recordingNameInNotification.split('/')[0]
    } - ${moment(notification?.created_at).format('MMMM Do YYYY, HH:mm:ss')}**${
      recordingNameSplited?.[2]
    }`
  }, [notification?.content])

  const onSelectNotification = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      dispatch(
        setSelectedNotifications([
          ...deletedNotifications,
          notification?.id ?? ''
        ])
      )
      return
    }
    dispatch(
      setSelectedNotifications(
        deletedNotifications.filter(
          (deletedNotification) => deletedNotification !== notification?.id
        )
      )
    )
  }

  return (
    <div
      className={classNames(
        'w-full flex gap-4 p-4',
        !notification?.is_seen && 'bg-gray-50',
        isPopup
          ? 'rounded-lg bg-white hover:bg-gray-100 cursor-pointer flex'
          : 'items-center'
      )}
      onClick={() => isPopup && handleClickNotification(notification?.id ?? '')}
    >
      {!isPopup && (
        <TextInputComponent
          type="checkbox"
          className="w-3 h-3 min-w-3 min-h-3 border border-gray-400 border-solid rounded-sm m-auto"
          checked={deletedNotifications.includes(notification?.id ?? '')}
          onChange={onSelectNotification}
        />
      )}
      <div
        className={classNames(
          'w-3 h-3 min-w-3 min-h-3 bg-red-500 rounded-full mt-1',
          notification?.is_seen && 'invisible'
        )}
      ></div>
      <div className="flex flex-col gap-2 w-full">
        <Markdown remarkPlugins={[remarkGfm]} className={'text-sm'}>
          {notification?.type.includes('Recording')
            ? notificationNameRecordingType
            : notification?.content}
        </Markdown>
        <div
          className={classNames(
            'flex gap-2',
            isPopup ? 'flex-col' : 'flex-row  items-center'
          )}
        >
          <p
            title={moment(notification?.created_at).format('MM/DD/YYYY HH:mm')}
            className="text-gray-400 text-xs flex items-center gap-3"
          >
            {moment(notification?.created_at).fromNow()}
          </p>
          {!isPopup && (
            <div className="w-[5px] h-[5px] flex rounded-full bg-gray-300" />
          )}
          <BadgeComponent
            className="!bg-[#F7F3FF]"
            textStyle="text-primary"
            text={renderNotificationType}
          />
        </div>
      </div>
      {!isPopup && (
        <PrimaryButton
          onClick={() => handleClickNotification(notification?.id ?? '')}
          className="max-h-9 sm:max-w-fit ml-auto"
        >
          {buttonText}
        </PrimaryButton>
      )}
    </div>
  )
}

export default NotificationItemComponent
