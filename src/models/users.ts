import { IPagination } from '../interfaces'

export interface IUser {
  id: string
  name: string
  email: string
}

export interface IUserQuery extends IPagination {
  email: string
}

//---------------------- Current Logged In User------------------

export interface ICurrentUser {
  id: string
  name: string
  email: string
  is_super_admin: boolean
  role: null
  setting?: Setting
  groups: ICurrentUserGroup[]
  projects: null
}

interface ICurrentUserGroup {
  id: string
  name: string
  description: string
  users: unknown[]
}

interface Setting {
  UserID: string
  Settings?: Settings
}

interface Settings {
  user_request: boolean
  user_acceptance: boolean
  user_access: boolean
  camera_status: boolean
  camera_onboard_off_board: boolean
  agent_status: boolean
  agent_onboard_off_board: boolean
  group_onboard: boolean
  group_off_board: boolean
  group_member: boolean
  group_member_role: boolean
  project_onboard: boolean
  project_off_board: boolean
  project_member: boolean
  project_member_role: boolean
  va_onboard: boolean
  va_off_board: boolean
  va_stream_process: boolean
  recording_synchronization: boolean
  recording_upload: boolean
  camera_email_notification: boolean
}
