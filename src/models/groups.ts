import { IPagination } from '../interfaces'

// use for getting list group with authorized token
export interface ListGroup {
  id: string
  name: string
  description: string
  users?: IGroupDetailUser[]
}

// Use for react-hook-form for creating group
export interface CreateGroupInput {
  name: string
  description: string
}

//data pass in Edit group API
export interface EditGroupAPIParams {
  group_id: string
  data: CreateGroupInput
}

//use for query list group
export interface IGroupQuery extends IPagination {
  name: string
  'order-by'?: string
  'order-direction'?: string
}

//only use in pages without authorized: Login, sign up,...
export interface UnauthorizedListGroup {
  id: string
  name: string
}

// ----------------interfaces for getting List of group's users -------------

export interface IGroupDetailUser {
  email: string
  id: string
  name: string
  role: IGroupDetailUserRole
  assigned_by: string
  membership_type: string
}

interface IGroupDetailUserRole {
  id: string
  name: string
  statement?: Statement[]
}

interface Statement {
  resource: string
  permissions: string[]
}

// --------------------- interface for updating Group's Users' role ----------------

export interface IGroupUserRoleUpdateParams {
  group_id: string
  role_name: string
  data: {
    role_id: string
    user_id: string
  }
}

// ------------------ interface for remove/add/edit user from a group
export interface IGroupUserModifyParams {
  group_id: string
  data: {
    user_id: string
    role_id?: string
  }
}
