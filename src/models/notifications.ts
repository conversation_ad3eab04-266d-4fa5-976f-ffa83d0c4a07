import { NOTIFICATION_TYPE } from '../enum/Notification'

export interface INotificationSettingsDetail {
  agent_onboard_off_board: boolean
  agent_status: boolean
  camera_onboard_off_board: boolean
  camera_status: boolean
  group_member: boolean
  group_member_role: boolean
  group_off_board: boolean
  group_onboard: boolean
  project_member: boolean
  project_member_role: boolean
  project_off_board: boolean
  project_onboard: boolean
  recording_synchronization: boolean
  recording_upload: boolean
  user_acceptance: boolean
  user_access: boolean
  user_request: boolean
  va_off_board: boolean
  va_onboard: boolean
  va_stream_process: boolean
  camera_email_notification?: boolean
}

export interface INotificationSettingRes {
  UserID: string
  Settings: INotificationSettingsDetail
}

export interface INotificationRes {
  notifications: INotification[]
  unseen_count: number
}

export interface INotification {
  id: string
  log_id: string
  content: string
  reference: INotificationReference
  type: NOTIFICATION_TYPE
  created_at: string
  is_seen: boolean
}

interface INotificationReference {
  reference_id: string
}
