import { SYSTEM_LOG_TYPES } from '../enum/SystemLogTypes'
import { IPagination } from '../interfaces'

export interface ISystemLog {
  id: string
  content: string
  type: SYSTEM_LOG_TYPES
  created_at: string
}

export interface ISystemLogQuery extends IPagination {
  content: string
  'order-by': string
  'order-direction': string
  'start-time'?: string
  'end-time'?: string
  type: SYSTEM_LOG_TYPES[]
}

export type IExportLogQuery = Pick<
  ISystemLogQuery,
  'start-time' | 'end-time' | 'content' | 'type'
>
