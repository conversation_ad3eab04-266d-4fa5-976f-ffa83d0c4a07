import { USER_PENDING_STATUS } from '../enum/UserPendingStatus'
import { IPagination } from '../interfaces'

export interface UserCredential {
  access_token: string
  token_type: string
  expires_in: number
  expires_at: number
  refresh_token: string
  user: UserCredentialDetail
}

export interface UserCredentialDetail {
  id: string
  aud: string
  role: string
  email: string
  email_confirmed_at: string
  phone: string
  confirmation_sent_at: string
  confirmed_at: string
  recovery_sent_at: string
  last_sign_in_at: string
  app_metadata: Appmetadata
  user_metadata: Usermetadata
  identities: Identity[]
  created_at: string
  updated_at: string
  is_anonymous: boolean
}

interface Identity {
  identity_id: string
  id: string
  user_id: string
  identity_data: Usermetadata
  provider: string
  last_sign_in_at: string
  created_at: string
  updated_at: string
  email: string
}

interface Usermetadata {
  email: string
  email_verified: boolean
  phone_verified: boolean
  sub: string
}

interface Appmetadata {
  provider: string
  providers: string[]
}
export interface AuthVerifyInput {
  type: string
  token: string
  email: string
}

export interface ResetPasswordInput {
  newPassword: string
  confirmNewPassword: string
}

export interface ResendOTPParams {
  email: string
  create_user: boolean
}

export interface RegisterParams {
  email: string
  password: string
  confirmPassword: string
  group: string
  name: string
}

export interface InvitationSignup {
  id: string
  name: string
  password: string
}

export interface RequestAccessParams {
  email: string
  group_id: string
  name: string
  password: string
  role?: string
}

export interface SignUpResponse {
  id: string
  aud: string
  role: string
  email: string
  phone: string
  confirmation_sent_at: string
  app_metadata: Appmetadata
  user_metadata?: Usermetadata
  identities: Identity[]
  created_at: string
  updated_at: string
  is_anonymous: boolean
}

interface Identity {
  identity_id: string
  id: string
  user_id: string
  identity_data: Usermetadata
  provider: string
  last_sign_in_at: string
  created_at: string
  updated_at: string
  email: string
}

interface Usermetadata {
  email: string
  email_verified: boolean
  phone_verified: boolean
  sub: string
}

interface Appmetadata {
  provider: string
  providers: string[]
}

export interface IPendingUsers {
  id: string
  name: string
  email: string
  method: string
  status: USER_PENDING_STATUS
  group_id: string
  role: PendingUsersRole
  group: PendingUsersGroup
  updated_at: string
}

interface PendingUsersGroup {
  id: string
  name: string
}

interface PendingUsersRole {
  id: string
  name: string
  statement: PendingUsersStatement[]
}

interface PendingUsersStatement {
  resource: string
  permissions: string[]
}

export interface InviteUserParams {
  email: string
  group_id: string
  name: string
  role_id: string
}

export interface IPendingRequestsQuery extends IPagination {
  statuses: USER_PENDING_STATUS[]
}
