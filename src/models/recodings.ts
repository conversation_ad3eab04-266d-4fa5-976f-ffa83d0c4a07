import { Segment } from 'm3u8-parser'
import { FILTER_TYPES } from '../enum/FilterTypes'
import { IOption } from '../interfaces'
import { Api_Keys } from './analytics'

export type RecordingType = 'recorded' | 'recovered' | 'uploaded' | ''

export interface GetPlaybackInput {
  camera_id: string
  selected_date: string
  recording_id: string
  type: RecordingType
}

export type DownloadRecordingQuery = Pick<
  GetPlaybackInput,
  'camera_id' | 'type'
> & {
  start: number
  end: number
  record_id: string
  date: string
}

export interface IRecordings {
  id: string
  camera_id: string
  duration: number
  record_name: string
  file_name: string
  size: number
  is_end: boolean
  start_index_segment: number
  va_plugins?: AssignedVA[]
  created_at: string
  updated_at: string
  record_type: RecordingType
  thumbnail: string
}

export interface AssignedVA {
  id: string
  name: string
  api_keys?: Api_Keys[]
  created_at: string
  updated_at: string
}

export interface ListRecordingFilterParams {
  'page-no': number
  'page-size': number
  'order-by'?: string
  'order-direction'?: string
  search?: string
  'project-ids'?: string[]
  'tag-ids'?: string[]
  'agent-ids'?: string[]
  'va-plugin-ids'?: string[]
  month?: number
  year?: number
  'only-uploaded'?: boolean
  'only-recorded'?: boolean
  file_name?: string
}
export interface RecordingsFilterPayload {
  type: FILTER_TYPES
  data: IOption
}

export interface UploadingRecordChunk {
  url: string
  etag: string
  progress: number
  isError?: boolean
  errorMsg: string
}

export interface UploadRecordingParams {
  files: File[]
  analytic_ids: IOption[]
  project_id: string
}

export interface UploadChunkParams {
  index: number
  data: UploadRecordingParams
  url?: string
}

export interface GetAzureUrlParams {
  filename: string
  total_chunks: number
}

export interface ICommitRecordBlocks {
  record_name: string
  block_list: string[]
  va_plugin_ids: string[]
  project_id: string
  duration: number
  size: number
  resolution: string
  file_name: string
}

export interface FileMetadata {
  duration: number
  resolution: string
  size: number
}

export interface IRecordingSegment {
  type: string
  data: Segment[]
}

export interface DeletedRecordings {
  page: number
  record_ids: string[]
}
