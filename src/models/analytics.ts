import { ANALYTIC_STREAM_STATUS } from '../enum/AnalyticStreamStatus'
import { IPagination } from '../interfaces'
import { IProject } from './projects'
import { AssignedVA } from './recodings'

export interface Analytics_Streams {
  id: string
  name: string
  type: 'CAMERA' | 'RECORDING'
  status: ANALYTIC_STREAM_STATUS
  source: string
  sink: string
}
export interface IAnalytics {
  id: string
  name: string
  provider: string
  description: string
  supported_classes: string
  created_at: string
  updated_at: string
  api_keys: Api_Keys[]
  api_key?: string
  project?: IProject
}

export interface AddVAInputs {
  name: string
  provider: string
  class: string
}

export interface Api_Keys {
  id: string
  name: string
  api_key: string
  revoked: boolean
  analytic_id: string
}

export interface AssignVARecording {
  recording_id: string
  va_plugin_ids: AssignedVA[]
}

// ---------------- interfaces for analytic results ----------------

export interface IAnalyticResultsQuerys extends IPagination {
  'order-by': string
  'order-direction': string
  start_time?: string
  end_time?: string
  label: string[]
  event_code: string[]
}

export interface IAnalyticsResults {
  id: string
  event_code: number
  timestamp: string
  image_blob: string
  objects: IAnalyticDetectionObject[]
  camera_id: string
}

interface IAnalyticDetectionObject {
  label: string
  bounding_box: number[]
  confidence: number
  tracking_id: number
}
// ------------------------------------------------------------------
export type QueryEvents = {
  value: string
  index: number
}
