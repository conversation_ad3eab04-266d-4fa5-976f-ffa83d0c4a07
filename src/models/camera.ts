import { FILTER_TYPES } from '../enum/FilterTypes'
import { IOption } from '../interfaces'
import { IAgent } from './agents'

export interface ListCameraFilterParams {
  'page-no': number
  'page-size': number
  'order-by'?: string
  'order-direction'?: string
  search?: string
  'project-ids'?: string[]
  'tag-ids'?: string[]
  'agent-ids'?: string[]
  'va-plugin-ids'?: string[]
  status?: string
}

export interface ICameraDetail {
  name: string
  notes: string
  projects: IOption
  rtsp: string
  tags: IOption[]
  agent: string
  recording: 'On' | 'Off'
  va_plugins: IOption[]
}

export interface UpdateCameraInput {
  agent_id: string
  name: string
  notes: string
  remove_tag_ids?: string[]
  remove_va_plugin_ids?: string[]
  rtsp: string
  tags?: string[]
  project_id: string
  recording: 'On' | 'Off'
  va_plugin_ids?: string[]
}

export interface CreateCameraInput {
  agent_id: string
  name: string
  notes: string
  project_id: string
  rtsp: string
  tags: string[]
  va_plugin_ids: string[]
}
export interface CameraTags {
  id?: string
  name?: string
}

export interface ICameras {
  agent: IAgent
  created_at: string
  id: string
  name: string
  notes: string
  project: Project
  retention: string
  rtsp: string
  status: 'Online' | 'Offline'
  tags: CameraTags[]
  updated_at: string
  recording: 'On' | 'Off'
  va_plugins: CameraVA[]
  position: number
}

export interface CameraVA {
  api_key: string
  created_at: string
  description: string
  hook_url: string
  id: string
  name: string
  provider: string
  supported_classes: string
  updated_at: string
}

interface Project {
  description: string
  id: string
  name: string
  users: User[]
}

interface User {
  email: string
  id: string
  name: string
}

export interface CameraFilterPayload {
  type: FILTER_TYPES
  data: IOption
}
export interface CamereHeartBeat {
  id: string
  api_Key: string
  status: 'Online' | 'Offline'
}
