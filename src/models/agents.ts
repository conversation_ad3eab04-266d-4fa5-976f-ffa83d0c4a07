import { OPERATING_SYSTEM } from '../enum/OperatingSystems'
import { IProject } from './projects'

export interface ListAgentParams {
  'page-no': number
  'page-size': number
  name?: string
  'project-ids'?: string[]
  'order-by'?: string
  'order-direction'?: string
}

export interface IAgent {
  id: string
  name: string
  provider: string
  api_keys: AgentAPIKey[]
  class: string
  cameras: unknown[]
  project: IProject
  created_at: string
  updated_at: string
  status: 'Offline' | 'Online'
}

export interface AddAgentInputs {
  name: string
  provider: string
  project_id: string
}
export interface CreateAgentRes {
  id: string
  name: string
  provider: string
  class: string
  api_key: string
  cameras: unknown[]
  created_at: string
  updated_at: string
}
interface AgentAPIKey {
  id: string
  name: string
  api_key: string
  revoked: boolean
  agent_id: string
  created_at: string
  updated_at: string
}

export interface AgentBinaryFileResponse {
  id: string
  file_path: string
  operating_system: string
  command: string
  created_at: string
  updated_at: string
}

export interface UploadBinaryFileParams {
  operating_system: OPERATING_SYSTEM
  file: File
}
export interface AgentHeartbeatData {
  id: string
  api_Key: string
  status: 'Online' | 'Offline'
  timestamp: string
  heartbeat_type: string
}
