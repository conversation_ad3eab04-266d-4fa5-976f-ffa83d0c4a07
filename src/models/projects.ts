import { IPagination } from '../interfaces'

export interface ListProjectQuerys extends IPagination {
  name: string
  'order-by'?: string
  'order-direction'?: string
}

export interface ProjectDetailFormInput {
  group_id: string
  description: string
  name: string
}

export interface EditProjectParams {
  id: string
  data: ProjectDetailFormInput
}

// ------------------ interfaces for List projects--------------
export interface IProject {
  id: string
  name: string
  description: string
  created_at: string
  users: IProjectUsers[]
  group: IProjectGroup
}

interface IProjectGroup {
  id: string
  name: string
  description: string
}

export interface IProjectUsers {
  id: string
  name: string
  email: string
  roles?: IProjectUserRole[]
}

export interface IProjectUserRole {
  id: string
  name: string
  statement: Statement[]
  assigned_by: string
  membership_type: string
}

interface Statement {
  resource: string
  permissions: string[]
}
// -------------------------------------------------------

export interface ModifyProjectUsers {
  role_id?: string
  user_id: string
}

export interface IModifyUserParams {
  project_id: string
  data: ModifyProjectUsers
}
