import { Middleware, configureStore } from '@reduxjs/toolkit'
import logger from 'redux-logger'
import {
  FLUSH,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
  REHYDRATE
} from 'redux-persist'
import cameraReducers from './Reducers/cameraReducers'
import agentReducers from './Reducers/agentReducers'
import projectsReducer from './Reducers/projectsReducer'
import usersReducer from './Reducers/usersReducer'
import analyticsReducer from './Reducers/analyticsReducer'
import recordingReducer from './Reducers/recordingReducer'
import requestsReducer from './Reducers/requestsReducer'
import groupReducers from './Reducers/groupReducers'
import dashboardReducer from './Reducers/dashboardReducer'
import notificationsReducer from './Reducers/notificationsReducer'
import logsReducer from './Reducers/logsReducer'

const middleware: Middleware[] = []
if (process.env.NODE_ENV !== 'production') {
  middleware.push(logger)
}

const store = configureStore({
  //   reducer: PersistedReducer,
  reducer: {
    //reducers here
    dashboard: dashboardReducer,
    cameras: cameraReducers,
    agents: agentReducers,
    projects: projectsReducer,
    users: usersReducer,
    analytics: analyticsReducer,
    recording: recordingReducer,
    requests: requestsReducer,
    groups: groupReducers,
    notifications: notificationsReducer,
    logs: logsReducer
  },

  middleware: (
    getDefaultMiddleware // config middleware for async thunk to work
  ) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER]
      } //check serialiable
    }).concat(middleware)
})

export type RootState = ReturnType<typeof store.getState>

export type AppDispatch = typeof store.dispatch

// export const persistor = persistStore(store);

export default store
