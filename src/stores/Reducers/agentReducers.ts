import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { AddAgentInputs, IAgent, ListAgentParams } from '../../models/agents'
import { AGENTS_ENDPOINTS } from '../../api/Endpoints'
import { IResponseDataWithPage } from '../../models/apiResponse'
import { createAgent, deleteAgent, getListAgents } from '../../api/Agents'
import { RootState } from '../store'
import { IOption, ReduxStatePayload } from '../../interfaces'
import { ListCameraFilterParams } from '../../models/camera'
import {
  DEFAULT_PAGE_SIZE,
  getErrorMessage,
  renderUnauthorizedMessage
} from '../../utils'
import { fetchListCameraThunk, toggleRefresh } from './cameraReducers'
import { toast } from 'react-hot-toast'

type InitialState = {
  listAgent: IAgent[]
  loading: boolean
  total: number
  'page-no': number
  'page-size': number
  apiKey: string
  searchText: string
  refresh: boolean // a state triggered when list camera/list agent changes to reconnect SSE
  'project-ids': IOption[]
  'order-by': string
}

const initialState: InitialState = {
  listAgent: [],
  loading: true,
  total: 0,
  refresh: false,
  'page-no': 1,
  apiKey: '',
  'page-size': DEFAULT_PAGE_SIZE,
  searchText: '',
  'project-ids': [],
  'order-by': 'created_at-desc'
}

export const fetchListAgentThunk = createAsyncThunk<
  IResponseDataWithPage<IAgent[]>,
  undefined
>(AGENTS_ENDPOINTS.LIST_AGENTS, async (_, { dispatch, getState }) => {
  const { agents: agenstState } = getState() as RootState
  const data: ListAgentParams = {
    'page-no': agenstState['page-no'],
    'page-size': agenstState['page-size'],
    'order-by': agenstState['order-by'].split('-')[0],
    'order-direction': agenstState['order-by'].split('-')[1],
    name: agenstState.searchText.trim(),
    'project-ids': agenstState['project-ids'].map((project) => project.value)
  }
  const res = await getListAgents(data)
  dispatch(toggleRefresh())
  return res
})

export const fetchListCameraByAgentThunk = createAsyncThunk(
  AGENTS_ENDPOINTS.AGENTS_CAMERA,
  async (data: { page: number; agent_ids: string[] }, { dispatch }) => {
    const params: ListCameraFilterParams = {
      'page-no': data.page,
      'page-size': DEFAULT_PAGE_SIZE,
      'agent-ids': data.agent_ids,
      'order-by': 'created_at',
      'order-direction': 'desc' //new to old
    }

    return await dispatch(fetchListCameraThunk(params))
  }
)

export const createAgentThunk = createAsyncThunk<unknown, AddAgentInputs>(
  `${AGENTS_ENDPOINTS.LIST_AGENTS}/create`,
  async (data: AddAgentInputs, { dispatch, getState, rejectWithValue }) => {
    const { agents: agenstState } = getState() as RootState
    const trimmedData: AddAgentInputs = {
      name: data.name.trim(),
      provider: data.provider.trim(), // remove whitespaces from both ends of the string
      project_id: data.project_id
    }
    try {
      const res = await createAgent(trimmedData)
      if (agenstState['page-no'] === 1) {
        dispatch(fetchListAgentThunk())
      } else {
        dispatch(setAgentPage(1))
      }
      dispatch(setApiKey(res.data.api_key))
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const deleteAgentThunk = createAsyncThunk<unknown, string | undefined>(
  `${AGENTS_ENDPOINTS.LIST_AGENTS}/delete`,
  async (agent_id: string = '', { getState, dispatch, rejectWithValue }) => {
    const { agents: agenstState } = getState() as RootState
    const isLastAgentInPage =
      agenstState.listAgent.length === 1 && agenstState['page-no'] > 1
    try {
      await deleteAgent(agent_id)
      if (isLastAgentInPage) {
        dispatch(setAgentPage(agenstState['page-no'] - 1))
      } else {
        dispatch(fetchListAgentThunk())
      }
    } catch (error) {
      const errMsg = renderUnauthorizedMessage(getErrorMessage(error))
      toast.error(errMsg)
      return rejectWithValue(errMsg)
    }
  }
)

const agentSlice = createSlice({
  name: 'agents',
  initialState,
  reducers: {
    setNextPageAgents: (state, action: ReduxStatePayload<IAgent[]>) => {
      state.listAgent = state.listAgent.concat(action.payload)
    },
    updateAgentHeartbeat: (state, action: ReduxStatePayload<IAgent>) => {
      const editedIndex = state.listAgent.findIndex(
        (agent) => agent.id === action.payload.id
      )
      if (editedIndex !== -1) {
        state.listAgent[editedIndex].status = action.payload.status
      }
    },
    setAgentPage: (state, action: ReduxStatePayload<number>) => {
      state['page-no'] = action.payload
    },
    setApiKey: (state, action: ReduxStatePayload<string>) => {
      state.apiKey = action.payload
    },
    setAgentSearchText: (state, action: ReduxStatePayload<string>) => {
      state.searchText = action.payload
    },
    setAgentProjectIds: (state, action: ReduxStatePayload<IOption[]>) => {
      state['project-ids'] = action.payload
    },
    setAgentOrderBy: (state, action: ReduxStatePayload<string>) => {
      state['order-by'] = action.payload
    }
  },
  extraReducers(builder) {
    builder.addCase(fetchListAgentThunk.pending, (state) => {
      state.loading = true
    })
    builder.addCase(fetchListAgentThunk.fulfilled, (state, action) => {
      state.listAgent = action.payload.data
      state.total = action.payload.total
      state.loading = false
      state.refresh = !state.refresh
    })
    builder.addCase(fetchListAgentThunk.rejected, (state) => {
      state.loading = false
    })
  }
})
export const {
  setNextPageAgents,
  updateAgentHeartbeat,
  setAgentPage,
  setApiKey,
  setAgentSearchText,
  setAgentProjectIds,
  setAgentOrderBy
} = agentSlice.actions
export const selectListAgent = (state: RootState) => state.agents.listAgent
export const selectTotalAgent = (state: RootState) => state.agents.total
export const selectAgentLoading = (state: RootState) => state.agents.loading
export const selectAgentRefreshingState = (state: RootState) =>
  state.agents.refresh
export const selectAgentPage = (state: RootState) => state.agents['page-no']
export const selectAPIKey = (state: RootState) => state.agents.apiKey
export const selectAgentSearchText = (state: RootState) =>
  state.agents.searchText
export const selectAgentProjectIds = (state: RootState) =>
  state.agents['project-ids']
export const selectAgentOrderBy = (state: RootState) => state.agents['order-by']

export default agentSlice.reducer
