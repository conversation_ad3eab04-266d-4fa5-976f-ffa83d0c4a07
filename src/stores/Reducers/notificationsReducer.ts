import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { RootState } from '../store'
import { NOTIFICATIONS_ENDPOINTS } from '../../api/Endpoints'
import { DEFAULT_PAGE_SIZE, getErrorMessage } from '../../utils'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import { IOption, IPagination, ReduxStatePayload } from '../../interfaces'
import {
  deleteNotifications,
  deleteNotificationAll,
  getNotificationList,
  getNotificationSettings,
  seenAllNotification,
  updateNotificationSettings,
  updateSeenNotifications
} from '../../api/Notifications'
import {
  INotification,
  INotificationRes,
  INotificationSettingRes,
  INotificationSettingsDetail
} from '../../models/notifications'
import { toast } from 'react-hot-toast'
export const NOTIFICATION_EVENTS: IOption[] = [
  {
    label: 'Camera Events',
    value: 'camera'
  },
  {
    label: 'Recording Events',
    value: 'recording'
  },
  {
    label: 'User Events',
    value: 'user'
  },
  {
    label: 'Agent Events',
    value: 'agent'
  },
  {
    label: 'Project Events',
    value: 'project'
  },
  {
    label: 'Group Events',
    value: 'group'
  },
  {
    label: 'Video Analytics Events',
    value: 'va_'
  }
]

export const DEFAULT_NOTIFICATION_SETTINGS: INotificationSettingsDetail = {
  agent_onboard_off_board: false,
  agent_status: false,
  camera_onboard_off_board: false,
  camera_status: false,
  group_member: false,
  group_member_role: false,
  group_off_board: false,
  group_onboard: false,
  project_member: false,
  project_member_role: false,
  project_off_board: false,
  project_onboard: false,
  recording_synchronization: false,
  recording_upload: false,
  user_acceptance: false,
  user_access: false,
  user_request: false,
  va_off_board: false,
  va_onboard: false,
  va_stream_process: false,
  camera_email_notification: false
}

interface InitialState {
  totalNotification: number
  unreadNotification: number
  listNotifications: INotification[]
  'page-no': number
  'page-size': number
  loading: boolean
  notificationSettings: INotificationSettingsDetail | undefined
  settingLoading: boolean
  selectedNotifications: string[]
}
const initialState: InitialState = {
  totalNotification: 0,
  unreadNotification: 0,
  listNotifications: [],
  'page-no': 1,
  'page-size': DEFAULT_PAGE_SIZE,
  loading: true,
  notificationSettings: undefined,
  settingLoading: true,
  selectedNotifications: []
}

export const fetchListNotificationThunk = createAsyncThunk<
  IResponseDataWithPage<INotificationRes>,
  string | undefined
>(
  NOTIFICATIONS_ENDPOINTS.LIST_NOTIFICATIONS,
  async (_, { getState, signal }) => {
    const { notifications: notificationState } = getState() as RootState
    const data: IPagination = {
      'page-no': notificationState['page-no'],
      'page-size': notificationState['page-size']
    }
    return await getNotificationList(data, signal)
  }
)

export const seenMultipleNotificationsThunk = createAsyncThunk<
  unknown,
  string[]
>(
  `${NOTIFICATIONS_ENDPOINTS.LIST_NOTIFICATIONS}/seen`,
  async (notification_ids: string[], { rejectWithValue }) => {
    try {
      await updateSeenNotifications(notification_ids)
      return
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const seenAllNotificationThunk = createAsyncThunk<unknown, undefined>(
  `${(NOTIFICATIONS_ENDPOINTS.ACTION_NOTIFICATION_ALL, 'seen-all')}`,
  async (_, { rejectWithValue }) => {
    try {
      await seenAllNotification()
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const deleteMultipleNotificationThunk = createAsyncThunk<
  unknown,
  string[]
>(
  `${NOTIFICATIONS_ENDPOINTS.ACTION_NOTIFICATION_ALL}/delete`,
  async (ids, { rejectWithValue, dispatch, getState }) => {
    try {
      const { notifications: notificationState } = getState() as RootState
      await deleteNotifications(ids)
      if (
        ids.length === notificationState.listNotifications.length &&
        notificationState['page-no'] === 1
      ) {
        dispatch(fetchListNotificationThunk())
      }
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const deleteAllNotificationThunk = createAsyncThunk<unknown, undefined>(
  `${NOTIFICATIONS_ENDPOINTS.ACTION_NOTIFICATION_ALL}/delete-all`,
  async (_, { rejectWithValue }) => {
    try {
      await deleteNotificationAll()
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const getNotificationSettingsThunk = createAsyncThunk<
  IResponseData<INotificationSettingRes>,
  undefined
>(NOTIFICATIONS_ENDPOINTS.NOTIFICATION_SETTINGS, async () => {
  return await getNotificationSettings()
})

export const updateNotificationSettingsThunk = createAsyncThunk<
  unknown,
  INotificationSettingsDetail | undefined
>(
  `${NOTIFICATIONS_ENDPOINTS.NOTIFICATION_SETTINGS}/update`,
  async (
    params: INotificationSettingsDetail | undefined,
    { rejectWithValue }
  ) => {
    if (!params) return
    try {
      await updateNotificationSettings(params)
      return params
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    increaseUnreadNotification: (state) => {
      state.unreadNotification++
    },
    increaseNotificationPage: (state) => {
      state['page-no']++
    },
    resetNotificationPage: (state) => {
      state['page-no'] = 1
    },
    addNewNotification: (state, action: ReduxStatePayload<INotification>) => {
      state.listNotifications.unshift(action.payload)
    },
    updateNotiSettings: (
      state,
      action: ReduxStatePayload<INotificationSettingsDetail>
    ) => {
      state.notificationSettings = action.payload
    },
    setSelectedNotifications: (state, action: ReduxStatePayload<string[]>) => {
      state.selectedNotifications = action.payload
    },
    setDeletedAllInPage: (state) => {
      state.selectedNotifications = state.listNotifications.map(
        (notification) => notification.id
      )
    }
  },
  extraReducers(builder) {
    //list notification
    builder.addCase(fetchListNotificationThunk.pending, (state) => {
      state.loading = true
    })
    builder.addCase(fetchListNotificationThunk.fulfilled, (state, action) => {
      state.loading = false
      state.totalNotification = action.payload.total
      state.unreadNotification = action.payload.data.unseen_count
      state.listNotifications =
        state['page-no'] === 1
          ? action.payload.data.notifications
          : [
              ...state.listNotifications,
              action.payload.data.notifications
            ].flat()
    })
    builder.addCase(fetchListNotificationThunk.rejected, (state) => {
      state.loading = false
    })

    //seen Notification
    builder.addCase(
      seenMultipleNotificationsThunk.fulfilled,
      (state, action) => {
        const updatedIDs = new Set(action.meta.arg)
        state.selectedNotifications = []
        state.listNotifications = state.listNotifications.map((notification) =>
          updatedIDs.has(notification.id)
            ? { ...notification, is_seen: true }
            : notification
        )
      }
    )
    builder.addCase(seenMultipleNotificationsThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })

    //seen all notification
    builder.addCase(seenAllNotificationThunk.fulfilled, (state) => {
      state.listNotifications = state.listNotifications.map((notification) => ({
        ...notification,
        is_seen: true
      }))
      state.unreadNotification = 0
      state.selectedNotifications = []
    })

    builder.addCase(seenAllNotificationThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })

    //get notification settings
    builder.addCase(getNotificationSettingsThunk.pending, (state) => {
      state.settingLoading = true
    })

    builder.addCase(getNotificationSettingsThunk.fulfilled, (state, action) => {
      state.settingLoading = false
      state.notificationSettings = action.payload.data.Settings
    })

    builder.addCase(getNotificationSettingsThunk.rejected, (state) => {
      state.settingLoading = false
    })

    //update notifcation settings
    builder.addCase(updateNotificationSettingsThunk.fulfilled, () => {
      toast.success('Your changes are saved!')
    })

    builder.addCase(updateNotificationSettingsThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })

    //delete multiple notification
    builder.addCase(
      deleteMultipleNotificationThunk.fulfilled,
      (state, action) => {
        const deletedNotificationIDs = action.meta.arg
        state.selectedNotifications = []
        toast.success(
          `${deletedNotificationIDs.length} notification(s) have been deleted!`
        )

        // if all notifications are selected deleted and page is not 1, set page to 1 to fetch new data
        if (
          deletedNotificationIDs.length === state.listNotifications.length &&
          state['page-no'] > 1
        ) {
          state['page-no'] = 1
          return
        }

        deletedNotificationIDs.forEach((id) => {
          const index = state.listNotifications.findIndex(
            (notification) => notification.id === id
          )
          if (index === -1) {
            return
          }
          state.listNotifications.splice(index, 1)
        })
      }
    )

    builder.addCase(deleteMultipleNotificationThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })

    //delete all notification
    builder.addCase(deleteAllNotificationThunk.fulfilled, (state) => {
      toast.success('All notifications have been deleted')
      state.listNotifications = []
      state.totalNotification = 0
      state.unreadNotification = 0
      state.selectedNotifications = []
    })

    builder.addCase(deleteAllNotificationThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })
  }
})

export const {
  increaseUnreadNotification,
  increaseNotificationPage,
  addNewNotification,
  resetNotificationPage,
  updateNotiSettings,
  setSelectedNotifications,
  setDeletedAllInPage
} = notificationSlice.actions
export const selectUnreadNotification = (state: RootState) =>
  state.notifications.unreadNotification
export const selectTotalNotification = (state: RootState) =>
  state.notifications.totalNotification
export const selectListNotification = (state: RootState) =>
  state.notifications.listNotifications
export const selectNotificationLoading = (state: RootState) =>
  state.notifications.loading
export const selectNotificationPage = (state: RootState) =>
  state.notifications['page-no']
export const selectNotificationSettingLoading = (state: RootState) =>
  state.notifications.settingLoading
export const selectNotificationSettings = (state: RootState) =>
  state.notifications.notificationSettings
export const selectSelectedNotifications = (state: RootState) =>
  state.notifications.selectedNotifications

export default notificationSlice.reducer
