import { PayloadAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
  AddVAInputs,
  IAnalyticResultsQuerys,
  IAnalytics
} from '../../models/analytics'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import { IOption, IPagination, ReduxStatePayload } from '../../interfaces'
import { ANALYTICS_ENDPOINT } from '../../api/Endpoints'
import { createVA, deleteVA, getListAnalytics } from '../../api/Analytics'
import { RootState } from '../store'
import { DEFAULT_PAGE_SIZE, getErrorMessage } from '../../utils'
import { toast } from 'react-hot-toast'
import { DisableVAParams } from '../../models/apiKeys'
import { revokeAPIKey } from '../../api/ApiKey'
import dayjs from 'dayjs'
import { TOAST_LIST_UPDATED_ALERT } from '../../enum/AnalyticEvents'

const QUERY_SORT_OPTIONS: IOption[] = [
  { value: 'created_at-desc', label: 'Newest to oldest' },
  { value: 'created_at-asc', label: 'Oldest to latest' }
]
export const DEFAULT_QUERY_PARAMS: IAnalyticResultsQuerys = {
  'order-by': QUERY_SORT_OPTIONS[0].value.split('-')[0],
  'order-direction': QUERY_SORT_OPTIONS[0].value.split('-')[1],
  'page-no': 1,
  'page-size': DEFAULT_PAGE_SIZE,
  label: [],
  event_code: []
}
interface InitialState {
  listAnalytics: IAnalytics[]
  total: number
  'page-no': number
  'page-size': number
  selectedVA: IAnalytics
  loading: boolean
  analyticResultQuery: IAnalyticResultsQuerys
}
const DEFAULT_VA_PAGE_SIZE = 12
const initialState: InitialState = {
  listAnalytics: [],
  total: 0,
  'page-no': 1,
  'page-size': DEFAULT_VA_PAGE_SIZE,
  loading: true,
  selectedVA: {
    id: '',
    name: '',
    provider: '',
    description: '',
    supported_classes: '',
    created_at: '',
    updated_at: '',
    api_keys: []
  },
  analyticResultQuery: {
    ...DEFAULT_QUERY_PARAMS,
    start_time: dayjs(Date.now())
      .subtract(30, 'day')
      .second(0)
      .millisecond(0)
      .toISOString(),
    end_time: dayjs(Date.now()).second(59).millisecond(0).toISOString()
  }
}

export const fetchListVAThunk = createAsyncThunk<
  IResponseDataWithPage<IAnalytics[]>
>(ANALYTICS_ENDPOINT.LIST_ANALYTICS, async (_, { getState }) => {
  const { analytics: analyticsState } = getState() as RootState
  const data: IPagination = {
    'page-no': analyticsState['page-no'],
    'page-size': analyticsState['page-size']
  }
  return await getListAnalytics(data)
})

export const createVAThunk = createAsyncThunk<
  IResponseData<IAnalytics>,
  AddVAInputs
>(
  ANALYTICS_ENDPOINT.LIST_ANALYTICS + '/create',
  async (data: AddVAInputs, { rejectWithValue, getState, dispatch }) => {
    const { analytics: analyticState } = getState() as RootState
    try {
      const res = await createVA(data)
      if (analyticState['page-no'] === 1) {
        dispatch(fetchListVAThunk())
      }
      return res
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const deleteVAThunk = createAsyncThunk<string, string>(
  ANALYTICS_ENDPOINT.LIST_ANALYTICS + '/delete',
  async (analytic_id: string, { rejectWithValue, getState, dispatch }) => {
    const { analytics: analyticsState } = getState() as RootState
    const currentPage = analyticsState['page-no']
    const listAnalytics = analyticsState.listAnalytics

    try {
      await deleteVA(analytic_id)
      if (currentPage === 1 || listAnalytics.length > 1) {
        dispatch(fetchListVAThunk())
      }
      return analytic_id
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const revokeVAThunk = createAsyncThunk<unknown, DisableVAParams>(
  ANALYTICS_ENDPOINT.LIST_ANALYTICS + '/revoke_apikeys',
  async (data: DisableVAParams, { rejectWithValue }) => {
    try {
      await revokeAPIKey(data.data)
      return data.analytic_id
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

const AnalyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {
    setAnalyticsPage: (state, action: ReduxStatePayload<number>) => {
      state['page-no'] = action.payload
    },
    setQueryParams: (state, action: PayloadAction<IAnalyticResultsQuerys>) => {
      state['page-no'] = state['page-no'] > 1 ? 1 : state['page-no']
      state.analyticResultQuery.label = action.payload.label
      state.analyticResultQuery.event_code = action.payload.event_code
      state.analyticResultQuery.start_time = action.payload.start_time
      state.analyticResultQuery.end_time = action.payload.end_time
      state.analyticResultQuery['order-by'] = action.payload['order-by']
      state.analyticResultQuery['order-direction'] =
        action.payload['order-direction']
    },
    setAnalyticQueryPage: (state, action: PayloadAction<number>) => {
      state.analyticResultQuery['page-no'] = action.payload
    },
    setAnalyticStartDateQuery: (state, action: PayloadAction<string>) => {
      state.analyticResultQuery.start_time = action.payload
    },
    setAnalyticEndDateQuery: (state, action: PayloadAction<string>) => {
      state.analyticResultQuery.end_time = action.payload
    },
    removeQueryObjectLabel: (state, action: PayloadAction<string>) => {
      state.analyticResultQuery.label = state.analyticResultQuery.label.filter(
        (label) => label !== action.payload
      )
    },
    removeQueryEventCode: (state, action: PayloadAction<string>) => {
      state.analyticResultQuery.event_code =
        state.analyticResultQuery.event_code.filter(
          (event_code) => event_code !== action.payload
        )
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchListVAThunk.pending, (state) => {
      state.loading = true
    })
    builder.addCase(fetchListVAThunk.fulfilled, (state, action) => {
      state.listAnalytics = action.payload.data
      state.total = action.payload.total
      state.loading = false
    })
    builder.addCase(fetchListVAThunk.rejected, (state) => {
      state.loading = false
      state.total = 0
    })

    //create VA Thunk
    builder.addCase(createVAThunk.fulfilled, (state) => {
      toast.remove(TOAST_LIST_UPDATED_ALERT)
      if (state['page-no'] > 1) {
        state['page-no'] = 1
      }
    })
    builder.addCase(createVAThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })

    //delete VA thunk
    builder.addCase(deleteVAThunk.fulfilled, (state) => {
      toast.remove(TOAST_LIST_UPDATED_ALERT)
      if (state.listAnalytics.length === 1 && state['page-no'] > 1) {
        state['page-no']--
        state.total--
      }
    })

    builder.addCase(deleteVAThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })
    //revoke VA
    builder.addCase(revokeVAThunk.fulfilled, (state, action) => {
      toast.remove(TOAST_LIST_UPDATED_ALERT)
      state.listAnalytics = state.listAnalytics.map((analytic) => {
        if (analytic.id === action.payload) {
          return {
            ...analytic,
            api_keys: [{ ...analytic.api_keys[0], revoked: true }]
          }
        } else {
          return analytic
        }
      })
    })
    builder.addCase(revokeVAThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })
  }
})
export const {
  setAnalyticsPage,
  setQueryParams,
  setAnalyticQueryPage,
  setAnalyticEndDateQuery,
  setAnalyticStartDateQuery,
  removeQueryEventCode,
  removeQueryObjectLabel
} = AnalyticsSlice.actions
export const selectListVA = (state: RootState) => state.analytics.listAnalytics
export const selectTotalVA = (state: RootState) => state.analytics.total
export const selectVALoading = (state: RootState) => state.analytics.loading
export const selectCurretPageSize = (state: RootState) =>
  state.analytics['page-size']
export const selectCurrentPage = (state: RootState) =>
  state.analytics['page-no']
export const selectAnalyticResultQuery = (
  state: RootState
): IAnalyticResultsQuerys => state.analytics.analyticResultQuery

export default AnalyticsSlice.reducer
