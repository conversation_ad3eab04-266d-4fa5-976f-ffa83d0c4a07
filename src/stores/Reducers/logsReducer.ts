import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IOption, ReduxStatePayload } from '../../interfaces'
import { RootState } from '../store'
import { DEFAULT_PAGE_SIZE, getErrorMessage } from '../../utils'
import { ISystemLog, ISystemLogQuery } from '../../models/systemLogs'
import { ROUTE_PATH } from '../../enum/RoutePath'
import { IResponseDataWithPage } from '../../models/apiResponse'
import { getSystemLogsList } from '../../api/SystemLogs'
import { SYSTEM_LOG_TYPES } from '../../enum/SystemLogTypes'

export const RETENTION_OPTIONS: IOption[] = [
  {
    label: 'Never',
    value: '-1'
  },
  {
    label: '3 months',
    value: '3'
  },
  {
    label: '6 months',
    value: '6'
  },
  {
    label: '12 months',
    value: '12'
  }
]

export const DATE_ORDER_OPTIONS: IOption[] = [
  { value: 'created_at-desc', label: 'Latest to oldest' },
  { value: 'created_at-asc', label: 'Oldest to latest' }
]

export const SYSTEM_LOG_TYPES_OPTIONS: IOption[] = Object.values(
  SYSTEM_LOG_TYPES
).map((status) => ({
  label: status,
  value: status
}))

interface InitialState {
  systemLogQuery: ISystemLogQuery
  listLogs: ISystemLog[]
  errorMsg: string
  loading: boolean
  total: number
}

const initialState: InitialState = {
  systemLogQuery: {
    'start-time': undefined,
    'end-time': undefined,
    content: '',
    'order-by': DATE_ORDER_OPTIONS[0].value,
    'order-direction': '',
    'page-no': 1,
    'page-size': DEFAULT_PAGE_SIZE,
    type: SYSTEM_LOG_TYPES_OPTIONS.map(
      (option) => option.value as SYSTEM_LOG_TYPES
    )
  },
  listLogs: [],
  errorMsg: '',
  loading: false,
  total: 0
}

export const fetchListSystemLogThunk = createAsyncThunk<
  IResponseDataWithPage<ISystemLog[]>,
  undefined
>(
  `${ROUTE_PATH.System_Logs}/list`,
  async (_, { getState, rejectWithValue }) => {
    const { logs: systemLogState } = getState() as RootState
    const data: ISystemLogQuery = {
      content: systemLogState.systemLogQuery.content.trim(),
      'order-direction':
        systemLogState.systemLogQuery['order-by'].split('-')[1],
      'order-by': systemLogState.systemLogQuery['order-by'].split('-')[0],
      'page-no': systemLogState.systemLogQuery['page-no'],
      'page-size': systemLogState.systemLogQuery['page-size'],
      type: systemLogState.systemLogQuery.type
    }

    if (systemLogState.systemLogQuery['start-time']) {
      data['start-time'] = systemLogState.systemLogQuery['start-time']
    }

    if (systemLogState.systemLogQuery['end-time']) {
      data['end-time'] = systemLogState.systemLogQuery['end-time']
    }

    try {
      return await getSystemLogsList(data)
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

const SystemLogsSlice = createSlice({
  name: 'logs',
  initialState,
  reducers: {
    changeDateRange: (
      state,
      action: ReduxStatePayload<null | (string | null)[]>
    ) => {
      state.systemLogQuery['start-time'] = action.payload?.[0] ?? ''
      state.systemLogQuery['end-time'] = action.payload?.[1] ?? ''
    },
    changeLogContentSearch: (state, action: ReduxStatePayload<string>) => {
      state.systemLogQuery.content = action.payload
    },
    changeSystemLogPage: (state, action: ReduxStatePayload<number>) => {
      state.systemLogQuery['page-no'] = action.payload
    },
    changeSystemLogSortOption: (state, action: ReduxStatePayload<string>) => {
      state.systemLogQuery['order-by'] = action.payload
    },
    clearEventTypeFilter: (state) => {
      state.systemLogQuery.type = []
    },
    removeEventTypeFilter: (
      state,
      action: ReduxStatePayload<SYSTEM_LOG_TYPES>
    ) => {
      const removedItemIndex = state.systemLogQuery.type.findIndex(
        (type) => type === action.payload
      )
      state.systemLogQuery.type.splice(removedItemIndex, 1)
    },
    addEventTypeFilter: (
      state,
      action: ReduxStatePayload<SYSTEM_LOG_TYPES>
    ) => {
      state.systemLogQuery.type.push(action.payload)
    },
    addAllEventTypeFilter: (state) => {
      state.systemLogQuery.type = SYSTEM_LOG_TYPES_OPTIONS.map(
        (option) => option.value as SYSTEM_LOG_TYPES
      )
    }
  },
  extraReducers(builder) {
    builder.addCase(fetchListSystemLogThunk.pending, (state) => {
      state.loading = true
      state.errorMsg = ''
    })
    builder.addCase(fetchListSystemLogThunk.fulfilled, (state, action) => {
      state.total = action.payload.total
      state.listLogs = action.payload.data
      state.loading = false
    })
    builder.addCase(fetchListSystemLogThunk.rejected, (state, action) => {
      state.loading = false
      state.errorMsg = action.payload as string
    })
  }
})

export const {
  changeDateRange,
  changeLogContentSearch,
  changeSystemLogPage,
  changeSystemLogSortOption,
  clearEventTypeFilter,
  removeEventTypeFilter,
  addEventTypeFilter,
  addAllEventTypeFilter
} = SystemLogsSlice.actions
export const selectLogFromDate = (state: RootState) =>
  state.logs.systemLogQuery['start-time']
export const selectLogToDate = (state: RootState) =>
  state.logs.systemLogQuery['end-time']
export const selectLogContentSearch = (state: RootState) =>
  state.logs.systemLogQuery.content
export const selectTotalSystemLogs = (state: RootState) => state.logs.total
export const selectListSystemLogs = (state: RootState) => state.logs.listLogs
export const selectSystemLogsPage = (state: RootState) =>
  state.logs.systemLogQuery['page-no']
export const selectSystemLogsPageSize = (state: RootState) =>
  state.logs.systemLogQuery['page-size']
export const selectSystemLogLoading = (state: RootState) => state.logs.loading
export const selectSystemLogSortOption = (state: RootState) =>
  state.logs.systemLogQuery['order-by']
export const selectSystemLogTypeOptions = (state: RootState) =>
  state.logs.systemLogQuery.type

export default SystemLogsSlice.reducer
