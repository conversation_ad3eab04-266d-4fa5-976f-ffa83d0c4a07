import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IResponseDataWithPage } from '../../models/apiResponse'
import { CAMERA_ENDPOINTS } from '../../api/Endpoints'
import { getListCamera } from '../../api/Camera'
import { RootState } from '../store'
import {
  CameraFilterPayload,
  ICameras,
  ListCameraFilterParams
} from '../../models/camera'
import { IOption, ReduxStatePayload } from '../../interfaces'
import { FILTER_TYPES } from '../../enum/FilterTypes'

type InitialState = {
  listCamera: ICameras[]
  loading: boolean
  total: number
  'order-by': string
  'agent-ids': IOption[]
  'project-ids': IOption[]
  'tag-ids': IOption[]
  'va-plugin-ids': IOption[]
  refresh: boolean // a state triggered when list camera/list agent changes to reconnect SSE
}
const initialState: InitialState = {
  listCamera: [],
  loading: true,
  total: 0,
  'order-by': 'created_at-desc',
  'agent-ids': [],
  'project-ids': [],
  'tag-ids': [],
  'va-plugin-ids': [],
  refresh: false
}
export const fetchListCameraThunk = createAsyncThunk<
  IResponseDataWithPage<ICameras[]>,
  ListCameraFilterParams
>(CAMERA_ENDPOINTS.LIST_CAMERA, async (data: ListCameraFilterParams) => {
  const res = await getListCamera(data)
  return res
}) // fetch list firmwares

const cameraSlice = createSlice({
  name: 'cameras',
  initialState,
  reducers: {
    toggleRefresh: (state) => {
      state.refresh = !state.refresh
    },
    setCameraList: (state, action) => {
      state.listCamera = action.payload
    },
    clearAllFilter: (state) => {
      state['agent-ids'] = []
      state['project-ids'] = []
      state['tag-ids'] = []
      state['va-plugin-ids'] = []
    },

    toggleOrder: (state, action) => {
      state['order-by'] = action.payload
    },
    addCameraFilters: (
      state,
      action: ReduxStatePayload<CameraFilterPayload>
    ) => {
      switch (action.payload.type) {
        case FILTER_TYPES.AGENTS:
          state['agent-ids'].unshift(action.payload.data)
          break
        case FILTER_TYPES.PROJECTS:
          state['project-ids'].unshift(action.payload.data)
          break
        case FILTER_TYPES.TAGS:
          state['tag-ids'].unshift(action.payload.data)
          break
        case FILTER_TYPES.VA_PLUGINS:
          state['va-plugin-ids'].unshift(action.payload.data)
          break
        default:
          break
      }
    },
    removeCameraFilters: (
      state,
      action: ReduxStatePayload<CameraFilterPayload>
    ) => {
      switch (action.payload.type) {
        case FILTER_TYPES.AGENTS: {
          const agentIndex = state['agent-ids'].findIndex(
            (item) => item.value === action.payload.data.value
          )
          state['agent-ids'].splice(agentIndex, 1)
          break
        }
        case FILTER_TYPES.PROJECTS: {
          const projectIndex = state['project-ids'].findIndex(
            (item) => item.value === action.payload.data.value
          )
          state['project-ids'].splice(projectIndex, 1)
          break
        }
        case FILTER_TYPES.TAGS: {
          const tagIndex = state['tag-ids'].findIndex(
            (item) => item.value === action.payload.data.value
          )
          state['tag-ids'].splice(tagIndex, 1)
          break
        }
        case FILTER_TYPES.VA_PLUGINS: {
          const vaIndex = state['va-plugin-ids'].findIndex(
            (item) => item.value === action.payload.data.value
          )
          state['va-plugin-ids'].splice(vaIndex, 1)
          break
        }
        default:
          break
      }
    },
    updateHeartBeat: (state, action: ReduxStatePayload<ICameras>) => {
      const index = state.listCamera.findIndex(
        (camera) => camera.id === action.payload.id
      )
      if (index !== -1) {
        state.listCamera[index].status = action.payload.status
      }
    }
  },
  extraReducers(builder) {
    builder.addCase(fetchListCameraThunk.pending, (state) => {
      state.loading = true
    })
    builder.addCase(fetchListCameraThunk.fulfilled, (state, action) => {
      state.listCamera = action.payload.data
      state.total = action.payload.total
      state.refresh = !state.refresh
      state.loading = false
    })
    builder.addCase(fetchListCameraThunk.rejected, (state) => {
      state.loading = false
    })
  }
})
export const {
  clearAllFilter,
  toggleOrder,
  addCameraFilters,
  removeCameraFilters,
  setCameraList,
  updateHeartBeat,
  toggleRefresh
} = cameraSlice.actions
export const selectCameraOrderBy = (state: RootState) =>
  state.cameras['order-by']
export const selectListCamera = (state: RootState) => state.cameras.listCamera
export const selectFilterAgents = (state: RootState) =>
  state.cameras['agent-ids']
export const selectFilterProjects = (state: RootState) =>
  state.cameras['project-ids']
export const selectFilterTags = (state: RootState) => state.cameras['tag-ids']
export const selectFilterVA = (state: RootState) =>
  state.cameras['va-plugin-ids']
export const selectTotalCamera = (state: RootState) => state.cameras.total
export const selectListCameraLoading = (state: RootState) =>
  state.cameras.loading
export const selectRefreshingState = (state: RootState) => state.cameras.refresh

export default cameraSlice.reducer
