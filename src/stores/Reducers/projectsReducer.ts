import {
  ThunkDispatch,
  UnknownAction,
  createAsyncThunk,
  createSlice
} from '@reduxjs/toolkit'
import {
  EditProjectParams,
  IModifyUserParams,
  IProject,
  IProjectUsers,
  ListProjectQuerys,
  ProjectDetailFormInput
} from '../../models/projects'
import { RootState } from '../store'
import { PROJECTS_ENDPOINT } from '../../api/Endpoints'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  addProjectUser,
  createProject,
  deleteProject,
  editProject,
  editProjectUserRole,
  getListProjects,
  removeProjectUser
} from '../../api/Projects'
import { ReduxStatePayload } from '../../interfaces'
import {
  DEFAULT_PAGE_SIZE,
  getErrorMessage,
  renderUnauthorizedMessage
} from '../../utils'
import { setGroupPage } from './groupReducers'
import toast from 'react-hot-toast'

type InitialState = {
  listProjects: IProject[]
  loading: boolean
  total: number
  isError: boolean
  searchText: string
  'order-by': string
  'page-no': number
  projectUsers: IProjectUsers[]
}
const initialState: InitialState = {
  listProjects: [],
  'order-by': 'created_at-desc',
  loading: true,
  total: 0,
  isError: false,
  searchText: '',
  'page-no': 1,
  projectUsers: []
}

export type ListProjectThunkPayload = {
  pagination_type?: 'infinte' | 'seperated'
  data: IResponseDataWithPage<IProject[]>
}

export const fetchListProjectThunk = createAsyncThunk<
  ListProjectThunkPayload,
  'infinte' | 'seperated' | undefined
>(
  PROJECTS_ENDPOINT.LIST_PROJECTS,
  async (
    pagination_type: 'infinte' | 'seperated' = 'seperated',
    { getState }
  ) => {
    const { projects: projectState } = getState() as RootState

    const data: ListProjectQuerys = {
      name: projectState.searchText.trim(),
      'page-no': projectState['page-no'],
      'page-size': DEFAULT_PAGE_SIZE,
      'order-by': projectState['order-by'].split('-')[0],
      'order-direction': projectState['order-by'].split('-')[1]
    }
    const res = await getListProjects(data)
    return { pagination_type: pagination_type, data: res }
  }
)

export const editProjectThunk = createAsyncThunk<undefined, EditProjectParams>(
  PROJECTS_ENDPOINT.LIST_PROJECTS + '/edit',
  async (data: EditProjectParams, thunkAPI) => {
    try {
      await editProject(data)
      thunkAPI.dispatch(fetchListProjectThunk())
    } catch (error) {
      const errMsg = getErrorMessage(error)
      toast.error(renderUnauthorizedMessage(errMsg))
      return thunkAPI.rejectWithValue(errMsg)
    }
  }
)

export const deleteProjectThunk = createAsyncThunk<string, string>(
  PROJECTS_ENDPOINT.LIST_PROJECTS + '/delete',
  async (project_id: string, thunkAPI) => {
    const { projects: projectState } = thunkAPI.getState() as RootState
    try {
      await deleteProject(project_id)
      if (
        projectState.listProjects.length > 1 ||
        (projectState.listProjects.length === 1 &&
          projectState['page-no'] === 1)
      ) {
        thunkAPI.dispatch(fetchListProjectThunk())
      }
      return project_id
    } catch (error) {
      const errMsg = getErrorMessage(error)
      toast.error(renderUnauthorizedMessage(errMsg))
      return thunkAPI.rejectWithValue(errMsg)
    }
  }
)

export const createProjectThunk = createAsyncThunk<
  IResponseData<IProject>,
  ProjectDetailFormInput
>(
  PROJECTS_ENDPOINT.LIST_PROJECTS + '/create',
  async (
    data: ProjectDetailFormInput,
    { rejectWithValue, getState, dispatch }
  ) => {
    const { projects: projectState } = getState() as RootState
    try {
      const res = await createProject(data)
      if (projectState['page-no'] > 1) {
        dispatch(setGroupPage(1))
      } else {
        dispatch(fetchListProjectThunk())
      }
      return res
    } catch (error) {
      const errMsg = getErrorMessage(error)
      return rejectWithValue(errMsg)
    }
  }
)

export const editProjectUserRoleThunk = createAsyncThunk<
  unknown,
  IModifyUserParams
>(
  `${PROJECTS_ENDPOINT.LIST_PROJECTS}/edit_user`,
  async (data: IModifyUserParams, { rejectWithValue, dispatch }) => {
    try {
      await editProjectUserRole(data)
      toast.success('Your changes have been saved!')
      refetchListAfterAddUser(data, dispatch)
    } catch (error) {
      const errMsg = getErrorMessage(error)
      toast.error(renderUnauthorizedMessage(errMsg))
      return rejectWithValue(errMsg)
    }
  }
)

export const addProjectUserThunk = createAsyncThunk<unknown, IModifyUserParams>(
  `${PROJECTS_ENDPOINT.LIST_PROJECTS}/add_user`,
  async (data: IModifyUserParams, { rejectWithValue, dispatch }) => {
    try {
      await addProjectUser(data)
      refetchListAfterAddUser(data, dispatch)
    } catch (error) {
      const errMsg = getErrorMessage(error)
      return rejectWithValue(errMsg)
    }
  }
)

const refetchListAfterAddUser = async (
  data: IModifyUserParams,
  dispatch: ThunkDispatch<unknown, unknown, UnknownAction>
) => {
  const refetchAction = await dispatch(fetchListProjectThunk())
  if (fetchListProjectThunk.fulfilled.match(refetchAction)) {
    const chosenProject = refetchAction.payload.data.data.find(
      (project) => project.id === data.project_id
    )
    dispatch(setProjectUsers(chosenProject?.users ?? []))
  }
}

export const removeProjectUserThunk = createAsyncThunk<
  unknown,
  IModifyUserParams
>(
  `${PROJECTS_ENDPOINT.LIST_PROJECTS}/remove_user`,
  async (
    params: IModifyUserParams,
    { rejectWithValue, getState, dispatch }
  ) => {
    const { projects: projectState } = getState() as RootState
    try {
      await removeProjectUser(params)
      onAfterRemoveUser(params, projectState.projectUsers, dispatch)
    } catch (error) {
      const errMsg = getErrorMessage(error)
      toast.error(renderUnauthorizedMessage(errMsg))
      return rejectWithValue(errMsg)
    }
  }
)

const onAfterRemoveUser = (
  params: IModifyUserParams,
  projectUsers: IProjectUsers[],
  dispatch: ThunkDispatch<unknown, unknown, UnknownAction>
) => {
  dispatch(fetchListProjectThunk())
  const removedUser = projectUsers.find(
    (user) => user.id === params.data.user_id
  )
  if (removedUser) {
    const newUsersList = projectUsers.filter(
      (user) => user.id !== removedUser.id
    )
    dispatch(setProjectUsers(newUsersList))
  }
  toast.success('User is removed successfully!')
}

const projectSlice = createSlice({
  name: 'projects',
  initialState,
  reducers: {
    setProjectSearchText: (state, action: ReduxStatePayload<string>) => {
      state.searchText = action.payload
    },
    setPage: (state, action: ReduxStatePayload<number>) => {
      state['page-no'] = action.payload
    },
    toggleProjectOrder: (state, action: ReduxStatePayload<string>) => {
      state['order-by'] = action.payload
    },
    setProjectUsers: (state, action: ReduxStatePayload<IProjectUsers[]>) => {
      state.projectUsers = action.payload
    }
  },
  extraReducers(builder) {
    builder.addCase(fetchListProjectThunk.pending, (state) => {
      state.isError = false
      state.loading = true
    })
    builder.addCase(fetchListProjectThunk.fulfilled, (state, action) => {
      state.loading = false
      state.total = action.payload.data.total
      if (
        action.payload.pagination_type === 'infinte' &&
        state['page-no'] > 1
      ) {
        state.listProjects = state.listProjects.concat(action.payload.data.data)
      } else {
        state.listProjects = action.payload.data.data
      }
    })
    builder.addCase(fetchListProjectThunk.rejected, (state) => {
      state.isError = true
      state.loading = false
    })

    builder.addCase(createProjectThunk.fulfilled, (state, action) => {
      if (state['page-no'] > 1) {
        state['page-no'] = 1
      } else {
        state.listProjects?.unshift(action.payload.data)
      }
    })
  }
})

export const {
  setProjectSearchText,
  setPage,
  toggleProjectOrder,
  setProjectUsers
} = projectSlice.actions
export const selectListProjects = (state: RootState) =>
  state.projects.listProjects
export const selectProjectsLoading = (state: RootState) =>
  state.projects.loading
export const selectProjectSearchText = (state: RootState) =>
  state.projects.searchText
export const selectProjectPage = (state: RootState) => state.projects['page-no']
export const selectProjectsSortOrder = (state: RootState) =>
  state.projects['order-by']
export const selectTotalProject = (state: RootState) => state.projects.total
export const selectProjectUsers = (state: RootState) =>
  state.projects.projectUsers

export default projectSlice.reducer
