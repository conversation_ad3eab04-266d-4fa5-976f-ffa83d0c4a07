import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { ICameras, ListCameraFilterParams } from '../../models/camera'
import { ReduxStatePayload } from '../../interfaces'
import { RootState } from '../store'
import {
  addCameraToDashboard,
  getListDashboardCameras,
  removeCameraDashboard,
  swapDashboardCamera
} from '../../api/Dashboard'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  AddCameraDashboard,
  IDashboardCamera,
  SwapCameraDashboard
} from '../../models/dashboard'
import { toast } from 'react-hot-toast'
import { DEFAULT_PAGE_SIZE, getErrorMessage } from '../../utils'
import { DASHBOARD_ENDPOINT } from '../../api/Endpoints'
import { getListCamera } from '../../api/Camera'

interface InititalState {
  listCameraView: IDashboardCamera[]
  listCamera: ICameras[]
  loading: boolean
  'page-no': number
  'page-size': number
  search: string
  totalCameras: number
}
const initialState: InititalState = {
  listCameraView: [],
  listCamera: [],
  loading: false,
  'page-no': 1,
  'page-size': DEFAULT_PAGE_SIZE,
  search: '',
  totalCameras: 0
}

// get list camera to show on the Manage Dashboard pop up
export const getDashboardManageCamerasThunk = createAsyncThunk<
  IResponseDataWithPage<ICameras[]>
>(
  DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS + '/manage',
  async (_, { getState, signal }) => {
    const { dashboard: dashboardState } = getState() as RootState
    const params: ListCameraFilterParams = {
      'page-no': dashboardState['page-no'],
      'page-size': dashboardState['page-size'],
      search: dashboardState.search.trim()
    }
    return await getListCamera(params, signal)
  }
)

//get list cameras appeared on the drag and drop grid
export const getDashboardCamerasThunk = createAsyncThunk<
  IResponseData<IDashboardCamera[]>,
  undefined
>(DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS, async () => {
  const res = await getListDashboardCameras()
  return res
})

//add a camera to the drag and drop grid
export const addCameraToDashboardThunk = createAsyncThunk<
  AddCameraDashboard,
  AddCameraDashboard
>(
  DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS + '/add',
  async (params: AddCameraDashboard, { rejectWithValue, dispatch }) => {
    const data: AddCameraDashboard = {
      position: params.position,
      camera_id: params.camera_id
    }
    try {
      await addCameraToDashboard(data)
      dispatch(getDashboardCamerasThunk())
      return params
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

// swap cameras in the drag and drop grid
export const swapDashboardCamerasThunk = createAsyncThunk<
  SwapCameraDashboard,
  SwapCameraDashboard
>(
  DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS + '/swap',
  async (params: SwapCameraDashboard, { rejectWithValue, dispatch }) => {
    try {
      dispatch(arrangeLiveView(params))
      await swapDashboardCamera(params)
      dispatch(getDashboardCamerasThunk())
      return params
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

//remove a camera from the drag and drop grid
export const removeDashboardCameraThunk = createAsyncThunk<string, string>(
  DASHBOARD_ENDPOINT.DASHBOARD_CAMERAS + '/remove',
  async (grid_id: string, { rejectWithValue, dispatch }) => {
    try {
      await removeCameraDashboard(grid_id)
      await dispatch(getDashboardCamerasThunk())
      return grid_id
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const DashboardSlice = createSlice({
  name: 'Dashboard',
  initialState,
  reducers: {
    addToLiveView: (state, action: ReduxStatePayload<AddCameraDashboard>) => {
      const droppedIndex = action.payload.position - 1
      state.listCameraView[droppedIndex] = {
        ...state.listCameraView[droppedIndex],
        camera: action.payload.data
      }
    },
    arrangeLiveView: (
      state,
      action: ReduxStatePayload<SwapCameraDashboard>
    ) => {
      const draggedItemIndex = action.payload.source_position - 1
      const droppedItemIndex = action.payload.target_position - 1

      const droppedCamera = state.listCameraView[droppedItemIndex]
      const draggedCamera = state.listCameraView[draggedItemIndex]
      state.listCameraView[droppedItemIndex] = draggedCamera
      state.listCameraView[draggedItemIndex] = droppedCamera
    },
    setManageCameraSearchText: (state, action: ReduxStatePayload<string>) => {
      state.search = action.payload
    },
    setManageCameraPage: (state, action: ReduxStatePayload<number>) => {
      state['page-no'] = action.payload
    }
  },
  extraReducers(builder) {
    builder.addCase(
      getDashboardManageCamerasThunk.fulfilled,
      (state, action) => {
        state.listCamera = action.payload.data
        state.totalCameras = action.payload.total
      }
    )

    builder.addCase(getDashboardCamerasThunk.fulfilled, (state, action) => {
      const numberOfGrids = Math.max(
        ...action.payload.data.map((grid_item) => grid_item.position)
      ) // find the highest position number to set the list length

      state.listCameraView = Array.from(
        // fill the list with default value with the numberOfGrids as length => show placeholder for grid item that doesnt have camera
        { length: numberOfGrids },
        (_, index) => ({
          id: (index + 1).toString(),
          position: index + 1,
          camera: undefined
        })
      )
      // map through the response to set the cameras in the response in their respective position
      action.payload.data.map((grid_item) => {
        state.listCameraView[grid_item.position - 1] = grid_item
      })
    })

    builder.addCase(addCameraToDashboardThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })

    builder.addCase(swapDashboardCamerasThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })

    builder.addCase(removeDashboardCameraThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })
  }
})
export const {
  arrangeLiveView,
  addToLiveView,
  setManageCameraSearchText,
  setManageCameraPage
} = DashboardSlice.actions
export const selectLiveViewData = (state: RootState) =>
  state.dashboard.listCameraView
export const selectListDashboardCamera = (state: RootState) =>
  state.dashboard.listCamera
export const selectDashboardLoading = (state: RootState) =>
  state.dashboard.loading
export const selectManageDashboardCameraSearchText = (state: RootState) =>
  state.dashboard.search
export const selectManageDashboardCameraPage = (state: RootState) =>
  state.dashboard['page-no']
export const selectManageDashboardTotalCamera = (state: RootState) =>
  state.dashboard.totalCameras
export default DashboardSlice.reducer
