import { PayloadAction, createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { ICurrentUser, IUser, IUserQuery } from '../../models/users'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import { USERS_ENDPOINT } from '../../api/Endpoints'
import { getUserById, getUsersList } from '../../api/Users'
import { RootState } from '../store'
import { DEFAULT_PAGE_SIZE } from '../../utils'
import { ReduxStatePayload } from '../../interfaces'

type InitialState = {
  listUsers: IUser[]
  loading: boolean
  total: number
  'page-no': number
  'page-size': number
  searchText: string
  currentUser: ICurrentUser | undefined
}
const initialState: InitialState = {
  listUsers: [],
  loading: true,
  total: 0,
  'page-no': 1,
  'page-size': DEFAULT_PAGE_SIZE,
  searchText: '',
  currentUser: undefined
}

export const getListUsersThunk = createAsyncThunk<
  IResponseDataWithPage<IUser[]>,
  undefined
>(USERS_ENDPOINT.LIST_USERS, async (_, { getState }) => {
  const { users: usersState } = getState() as RootState
  const data: IUserQuery = {
    email: usersState.searchText.trim(),
    'page-no': usersState['page-no'],
    'page-size': usersState['page-size']
  }
  return await getUsersList(data)
})

export const getUserByIdThunk = createAsyncThunk<
  IResponseData<ICurrentUser>,
  string
>(USERS_ENDPOINT.LIST_USERS + '/detail', async (user_id: string = '') => {
  return await getUserById(user_id)
})

const UsersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    setUserSearchText: (state, action: ReduxStatePayload<string>) => {
      state.searchText = action.payload
    },
    triggerFetchUserLoading: (state) => {
      state.loading = true
    },
    updateUserEmailSetting: (state, action: PayloadAction<boolean>) => {
      if (
        state.currentUser &&
        state.currentUser.setting &&
        state.currentUser.setting.Settings
      ) {
        state.currentUser.setting.Settings = {
          ...state.currentUser?.setting.Settings,
          camera_email_notification: action.payload
        }
      }
    }
  },
  extraReducers(builder) {
    builder.addCase(getListUsersThunk.pending, (state) => {
      state.loading = true
    })
    builder.addCase(getListUsersThunk.fulfilled, (state, action) => {
      state.listUsers = action.payload.data
      state.total = action.payload.total
      state.loading = false
    })
    builder.addCase(getListUsersThunk.rejected, (state) => {
      state.loading = false
    })

    builder.addCase(getUserByIdThunk.fulfilled, (state, action) => {
      state.currentUser = action.payload.data
    })
  }
})
export const {
  setUserSearchText,
  triggerFetchUserLoading,
  updateUserEmailSetting
} = UsersSlice.actions
export const selectListUsers = (state: RootState) => state.users.listUsers
export const selectUserLoading = (state: RootState) => state.users.loading
export const selectTotalUser = (state: RootState) => state.users.total
export const selectUserSearchText = (state: RootState) => state.users.searchText
export const selectCurrentUser = (state: RootState) => state.users.currentUser

export default UsersSlice.reducer
