import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
  DEFAULT_PAGE_SIZE,
  getErrorMessage,
  renderUnauthorizedMessage
} from '../../utils'
import { AUTH_ENDPOINTS } from '../../api/Endpoints'
import { IResponseDataWithPage } from '../../models/apiResponse'
import { RootState } from '../store'
import { ReduxStatePayload } from '../../interfaces'
import {
  acceptPendingRequest,
  getListPendingRequests,
  inviteUser,
  rejectPendingRequest
} from '../../api/Auth'
import { USER_PENDING_STATUS } from '../../enum/UserPendingStatus'
import {
  IPendingRequestsQuery,
  IPendingUsers,
  InviteUserParams
} from '../../models/auth'
import { toast } from 'react-hot-toast'

type ActionReturnMessage = {
  message: string
  id: string
}

interface InitialState {
  listRequest: IPendingUsers[]
  loading: boolean
  'page-no': number
  'page-size': number
  total: number
  search: string
  actionPending: string[]
  status: USER_PENDING_STATUS[]
}

const initialState: InitialState = {
  listRequest: [],
  loading: true,
  'page-no': 1,
  'page-size': DEFAULT_PAGE_SIZE,
  total: 0,
  search: '',
  actionPending: [],
  status: [USER_PENDING_STATUS.INVITED, USER_PENDING_STATUS.PENDING]
}

export const fetchListRequestThunk = createAsyncThunk<
  IResponseDataWithPage<IPendingUsers[]>,
  undefined
>(AUTH_ENDPOINTS.PENDING_REQUESTS, async (_, { getState }) => {
  const { requests: requestState } = getState() as RootState
  const data: IPendingRequestsQuery = {
    'page-no': requestState['page-no'],
    'page-size': requestState['page-size'],
    statuses: requestState.status
  }
  return await getListPendingRequests(data)
})

export const rejectRequestThunk = createAsyncThunk<ActionReturnMessage, string>(
  AUTH_ENDPOINTS.REJECT_REQUEST,
  async (id: string, { rejectWithValue, dispatch }) => {
    dispatch(setActionPending(id))
    try {
      await rejectPendingRequest(id)
      return {
        message: 'success',
        id: id
      }
    } catch (error) {
      return rejectWithValue({ message: getErrorMessage(error), id: id })
    }
  }
)

export const approveRequestThunk = createAsyncThunk<
  ActionReturnMessage,
  string
>(
  AUTH_ENDPOINTS.ACCEPT_REQUEST,
  async (id: string, { rejectWithValue, dispatch }) => {
    dispatch(setActionPending(id))
    try {
      await acceptPendingRequest(id)
      return {
        message: 'success',
        id: id
      }
    } catch (error) {
      return rejectWithValue({ message: getErrorMessage(error), id: id })
    }
  }
)

export const inviteUserThunk = createAsyncThunk<unknown, InviteUserParams>(
  AUTH_ENDPOINTS.INVITE_USER,
  async (data: InviteUserParams, { dispatch, getState, rejectWithValue }) => {
    const { requests: requestState } = getState() as RootState
    try {
      await inviteUser(data)
      if (requestState['page-no'] > 1) {
        dispatch(setRequestPage(1))
      } else {
        dispatch(fetchListRequestThunk())
      }
    } catch (error) {
      const errorMsg = getErrorMessage(error)

      return rejectWithValue(renderUnauthorizedMessage(errorMsg))
    }
  }
)

export const RequestSlice = createSlice({
  name: 'request',
  initialState,
  reducers: {
    // save user input
    setRequestSearch: (state, action: ReduxStatePayload<string>) => {
      state.search = action.payload
    },

    //change pagination page
    setRequestPage: (state, action: ReduxStatePayload<number>) => {
      state['page-no'] = action.payload
    },

    //array to tell which item has an action in progress e.g: is approving/rejecting
    setActionPending: (state, action: ReduxStatePayload<string>) => {
      state.actionPending.push(action.payload)
    },

    //remove item has an action finished
    removeActionPending: (state, action: ReduxStatePayload<string>) => {
      state.actionPending = state.actionPending.filter(
        (item) => item !== action.payload
      )
    },

    //modify the role for request that has INVITED role
    changeInvitationUserRole: (
      state,
      action: ReduxStatePayload<{ request_id: string; role_id: string }>
    ) => {
      const modifiedItemIndex = state.listRequest.findIndex(
        (item) => item.id === action.payload.request_id
      )
      state.listRequest[modifiedItemIndex] = {
        ...state.listRequest[modifiedItemIndex],
        role: {
          ...state.listRequest[modifiedItemIndex].role,
          id: action.payload.role_id
        }
      }
    },

    //add status filter
    addFilterStatus: (
      state,
      action: ReduxStatePayload<USER_PENDING_STATUS | ''>
    ) => {
      if (action.payload === '') {
        state.status = Object.values(USER_PENDING_STATUS)
      } else {
        state.status.push(action.payload)
      }
    },

    //remove status filter
    removeFilterStatus: (
      state,
      action: ReduxStatePayload<USER_PENDING_STATUS | ''>
    ) => {
      state.status = state.status.filter((status) => status !== action.payload)
    },

    //clear all filters
    clearAllStatusFilter: (state) => {
      state.status = []
    }
  },
  extraReducers(builder) {
    builder.addCase(fetchListRequestThunk.pending, (state) => {
      state.loading = true
    })
    builder.addCase(fetchListRequestThunk.fulfilled, (state, action) => {
      state.listRequest = action.payload.data
      state.loading = false
      state.total = action.payload.total
    })
    builder.addCase(fetchListRequestThunk.rejected, (state) => {
      state.loading = false
    })

    //CASE FOR REJECTION
    builder.addCase(rejectRequestThunk.fulfilled, (state, action) => {
      const rejectedItemIndex = state.listRequest.findIndex(
        (req) => req.id === action.payload.id
      )
      state.actionPending = state.actionPending.filter(
        (pendingReq) => pendingReq !== action.payload.id
      )
      state.listRequest[rejectedItemIndex] = {
        ...state.listRequest[rejectedItemIndex],
        status: USER_PENDING_STATUS.REJECTED
      }
      toast.success(
        `Request from ${state.listRequest[rejectedItemIndex].name} is rejected!`
      )
    })

    builder.addCase(rejectRequestThunk.rejected, (state, action) => {
      const error = action.payload as ActionReturnMessage
      state.actionPending = state.actionPending.filter(
        (pendingReq) => pendingReq !== error.id
      )
      toast.error(error.message as string)
    })

    //cases for APRROVAL
    builder.addCase(approveRequestThunk.fulfilled, (state, action) => {
      const approvedItemIndex = state.listRequest.findIndex(
        (req) => req.id === action.payload.id
      )
      state.actionPending = state.actionPending.filter(
        (pendingReq) => pendingReq !== action.payload.id
      )
      state.listRequest[approvedItemIndex] = {
        ...state.listRequest[approvedItemIndex],
        status: USER_PENDING_STATUS.APPROVED
      }
      toast.success(
        `Request from ${state.listRequest[approvedItemIndex].name} is approved!`
      )
    })
    builder.addCase(approveRequestThunk.rejected, (state, action) => {
      const error = action.payload as ActionReturnMessage
      state.actionPending = state.actionPending.filter(
        (pendingReq) => pendingReq !== error.id
      )
      toast.error(action.payload as string)
    })
  }
})
export const {
  setRequestSearch,
  setRequestPage,
  setActionPending,
  removeActionPending,
  changeInvitationUserRole,
  removeFilterStatus,
  addFilterStatus,
  clearAllStatusFilter
} = RequestSlice.actions
export const selectListRequests = (state: RootState) =>
  state.requests.listRequest
export const selectRequestLoading = (state: RootState) => state.requests.loading
export const selectRequestTotal = (state: RootState) => state.requests.total
export const selectRequestPage = (state: RootState) => state.requests['page-no']
export const selectRequestPageSize = (state: RootState) =>
  state.requests['page-size']
export const selectRequestSearchText = (state: RootState) =>
  state.requests.search
export const selectActionPending = (state: RootState) =>
  state.requests.actionPending
export const selectFilterRequestStatus = (state: RootState) =>
  state.requests.status

export default RequestSlice.reducer
