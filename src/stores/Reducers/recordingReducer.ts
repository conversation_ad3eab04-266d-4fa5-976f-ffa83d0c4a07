import {
  PayloadAction,
  ThunkDispatch,
  UnknownAction,
  createAsyncThunk,
  createSlice
} from '@reduxjs/toolkit'
import {
  ICommitRecordBlocks,
  IRecordings,
  ListRecordingFilterParams,
  RecordingsFilterPayload,
  UploadChunkParams,
  UploadingRecordChunk
} from '../../models/recodings'
import { IOption, ReduxStatePayload } from '../../interfaces'
import { RECORDING_ENDPOINT } from '../../api/Endpoints'
import {
  assignVAToRecording,
  commitRecordingBlocks,
  deleteRecording,
  getListRecordings,
  uploadToAzure
} from '../../api/Recordings'
import { RootState } from '../store'
import dayjs from 'dayjs'
import { IResponseDataWithPage } from '../../models/apiResponse'
import { AssignVARecording } from '../../models/analytics'
import toast from 'react-hot-toast'
import {
  abortAllControllers,
  createAbortController,
  getErrorMessage
} from '../../utils'
import { FILTER_TYPES } from '../../enum/FilterTypes'

export const UPLOAD_TOAST_ID = 'UPLOAD_TOAST_ID'

export const controllers: AbortController[] = []

export const downloadedFileData: { id: string; controller: AbortController }[] =
  []

export const RECORDING_PAGESIZE = 12

export const DOWNLOAD_FILE_IDS: string[] = []

export const PART_SIZE = 100 * 1024 * 1024 //100MB

export const RECORD_SORT_OPTIONS: IOption[] = [
  { value: 'created_at-desc', label: 'Newest to oldest' },
  { value: 'created_at-asc', label: 'Oldest to latest' },
  { value: 'name-asc', label: 'Camera name (A-Z)' },
  { value: 'name-desc', label: 'Camera name (Z-A)' }
]

export const RECORD_TYPE_OPTIONS = [
  { label: 'All', value: 'all' },
  {
    label: 'Recorded',
    value: 'recorded'
  },
  {
    label: 'Uploaded',
    value: 'uploaded'
  }
]

export const SUPPORTED_RECORDINGS_FORMAT: IOption[] = [
  { label: 'AVI', value: 'video/avi' },
  { label: 'MOV', value: 'video/quicktime' },
  { label: 'MP4', value: 'video/mp4' }
]

export const DEFAULT_RECORDING: IRecordings = {
  id: '',
  camera_id: '',
  duration: 0,
  record_name: '',
  file_name: '',
  size: 0,
  is_end: false,
  start_index_segment: 0,
  created_at: '',
  updated_at: '',
  record_type: 'recorded',
  thumbnail: ''
}

interface InitialState {
  listRecording: IRecordings[] // list of recordings
  loading: boolean //loading flag when fetching list recordings
  filteredAgents: IOption[] // selected agent filter options
  filteredProjects: IOption[] //selected project  filter options
  filteredTags: IOption[] //selected tag filter options
  filteredVA: IOption[] //selected VA filter options
  orderBy: string // selected order ooption
  selectedMonth: number //month selected
  selectedYear: number //year selected,
  selectedRecording: IRecordings | undefined
  chunkProgress: UploadingRecordChunk[] // list of uploading chunks progress,
  'only-uploaded': boolean
  'only-recorded': boolean
  totalRecording: number
  'page-no': number
  'page-size': number
  isUploading: boolean
  isDownloading: boolean
  downloadedFiles: string[]
  deletedRecordings: IRecordings[]
}

const initialState: InitialState = {
  listRecording: [],
  loading: false,
  filteredAgents: [],
  filteredProjects: [],
  filteredTags: [],
  filteredVA: [],
  orderBy: 'created_at-desc', //latest first
  selectedMonth: dayjs().month(),
  selectedYear: dayjs().year(),
  selectedRecording: undefined,
  chunkProgress: [],
  'only-uploaded': false,
  'only-recorded': false,
  totalRecording: 0,
  'page-no': 1,
  'page-size': RECORDING_PAGESIZE,
  isUploading: false,
  isDownloading: false,
  downloadedFiles: [],
  deletedRecordings: []
}

//thunk to call api to fetch List recording, and handle states in the extraReducer below
export const fetchListRecordingThunk = createAsyncThunk<
  IResponseDataWithPage<IRecordings[]> //type of the data returned from the thunk
>(RECORDING_ENDPOINT.LIST_RECORDING, async (_, { getState, signal }) => {
  const { recording } = getState() as RootState // get the global recording state
  // Get the search string from the current URL
  const queryString = window.location.search

  // Create a URLSearchParams object
  const params = new URLSearchParams(queryString)

  // Retrieve specific parameters
  const recordingSearchState = params.get('search') ?? ''
  const data: ListRecordingFilterParams = {
    //data used for the api
    'page-no': recording['page-no'],
    'page-size': recording['page-size'],
    'order-by': recording.orderBy.split('-')[0],
    'order-direction': recording.orderBy.split('-')[1],
    search: recordingSearchState.trim(),
    'only-uploaded': recording['only-uploaded'],
    'only-recorded': recording['only-recorded']
  }
  if (!recordingSearchState) {
    data.year = recording.selectedYear
    data.month = recording.selectedMonth + 1
  }
  if (recording.filteredProjects.length > 0) {
    //only assign this field when the list is not empty
    data['project-ids'] = recording.filteredProjects.map(
      (project) => project.value
    )
  }
  if (recording.filteredTags.length > 0) {
    //only assign this field when the list is not empty
    data['tag-ids'] = recording.filteredTags.map((tag) => tag.value)
  }
  if (recording.filteredAgents.length > 0) {
    //only assign this field when the list is not empty
    data['agent-ids'] = recording.filteredAgents.map((agent) => agent.value)
  }
  if (recording.filteredVA.length > 0) {
    //only assign this field when the list is not empty
    data['va-plugin-ids'] = recording.filteredVA.map((va) => va.value)
  }
  return await getListRecordings(data, signal)
})

//thunk to assign VA to a recording, state changes handled in extraReducer
export const assignVAPluginsThunk = createAsyncThunk<
  IRecordings, //type of date returned from the thunk
  AssignVARecording //type of input params
>(
  RECORDING_ENDPOINT.LIST_RECORDING + '/VA',
  async (data: AssignVARecording, thunkAPI) => {
    try {
      const res = await assignVAToRecording(data) // api call
      return {
        ...res.data,
        va_plugins: data.va_plugin_ids
      } // this data will be returned in action.payload in the extraReducer below
    } catch (error) {
      return thunkAPI.rejectWithValue(getErrorMessage(error)) // return error message from the thonk to notify the user
    }
  }
)

const onUploadChunkProgress = (
  dispatch: ThunkDispatch<unknown, unknown, UnknownAction>,
  uploadUrl: string,
  loaded: number
) => {
  dispatch(
    updateChunkProgress({
      url: uploadUrl,
      progress: loaded // update the uploaded bytes
    })
  )
}
// ----------------------------- spliting the file into chunks-----------------------------
const splitFileToChunks = (
  params: UploadChunkParams,
  recording: InitialState
): Blob => {
  const start =
    (params.url
      ? recording.chunkProgress.findIndex((chunk) => chunk.url === params.url)
      : params.index) * // if reupload, the index will be based on the url passed in
    PART_SIZE
  const end = Math.min(start + PART_SIZE, params.data.files?.[0].size ?? 0)
  return params.data?.files?.[0].slice(start, end)
}
// -----------------------------------------------------

export const uploadRecordByChunkThunk = createAsyncThunk<
  { url: string; etag: string },
  UploadChunkParams
>(
  RECORDING_ENDPOINT.LIST_RECORDING + '/upload',
  async (
    params: UploadChunkParams,
    { dispatch, getState, rejectWithValue }
  ) => {
    const { recording } = getState() as RootState // get the global recording state
    const abortController = createAbortController(controllers) //terminate the api call on condition
    const uploadUrl = params.url ?? recording.chunkProgress[params.index]?.url // if reupload, the url will be based on the url passed in
    // ------------------------------ Begin Upload ---------------------------------
    try {
      await uploadToAzure(
        uploadUrl,
        (loaded) => onUploadChunkProgress(dispatch, uploadUrl, loaded), //update progress to show on Toast
        abortController,
        splitFileToChunks(params, recording)
      )
      return {
        url: uploadUrl,
        etag: new URL(uploadUrl).searchParams.get('blockid') ?? ''
      } // update the etag later to sync the server
    } catch (error) {
      abortAllControllers(controllers) // when a chunk is error, all other chunks is canceled
      return rejectWithValue({
        url: uploadUrl,
        error: getErrorMessage(error)
      }) // update the error state
    }
  }
)

export const commitAzureBlockThunk = createAsyncThunk<
  IRecordings,
  ICommitRecordBlocks
>(
  RECORDING_ENDPOINT.COMMIT_BLOCK_BLOBS,
  async (params: ICommitRecordBlocks, { rejectWithValue, dispatch }) => {
    try {
      const res = await commitRecordingBlocks(params)
      toast.success(`Your file has been uploaded successfully`, {
        id: UPLOAD_TOAST_ID,
        duration: 5000
      })
      return res
    } catch (error) {
      toast.error(`Failed to sync the file into the system!`, {
        id: UPLOAD_TOAST_ID,
        duration: 5000
      })
      return rejectWithValue(getErrorMessage(error))
    } finally {
      dispatch(refreshChunkProgress())
    }
  }
)

export const deleteRecordingsThunk = createAsyncThunk<unknown, undefined>(
  `${RECORDING_ENDPOINT.LIST_RECORDING}/delete`,
  async (_, { rejectWithValue, dispatch, getState }) => {
    const { recording } = getState() as RootState // get the global recording state
    const deletedRecordingsIds = new Set(
      recording.deletedRecordings.map((item) => item.id)
    )
    const currentListRecordingsIds = recording.listRecording.map(
      (item) => item.id
    )
    const isDeletingCurrentPage = currentListRecordingsIds.every((id) =>
      deletedRecordingsIds.has(id)
    )
    try {
      await deleteRecording(recording.deletedRecordings.map((item) => item.id))

      if (isDeletingCurrentPage && recording['page-no'] > 1) {
        dispatch(setCurrentRecordingPage(recording['page-no'] - 1))
        return
      }
      dispatch(fetchListRecordingThunk())
    } catch (error) {
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

const recordingSlice = createSlice({
  name: 'recording',
  initialState,
  reducers: {
    playRecording: (state, action: ReduxStatePayload<IRecordings>) => {
      state.selectedRecording = action.payload
    },
    setOrderBy: (state, action) => {
      //select order option
      state.orderBy = action.payload
    },

    startUploading: (state) => {
      state.isUploading = true
    },

    updateChunkProgress: (
      state,
      action: ReduxStatePayload<{
        url: string
        progress: number
      }>
    ) => {
      const updatedChunkIndex = state.chunkProgress.findIndex(
        (chunk) => chunk.url === action.payload.url
      )
      if (updatedChunkIndex !== undefined) {
        state.chunkProgress[updatedChunkIndex] = {
          ...state.chunkProgress[updatedChunkIndex],
          progress: action.payload.progress,
          isError: false
        }
      }
    },

    updateChunkUrl: (state, action: ReduxStatePayload<string[]>) => {
      const chunkUrls: UploadingRecordChunk[] = action.payload.map((url) => ({
        url: url,
        errorMsg: '',
        etag: '',
        progress: 0
      }))
      state.chunkProgress = chunkUrls
    },

    refreshChunkProgress: (state) => {
      state.chunkProgress = state.chunkProgress.map((chunk) => ({
        ...chunk,
        progress: 0,
        etag: '',
        isError: false
      }))
      state.isUploading = false
    },

    addRecordingFilters: (
      //add filters by type
      state,
      action: ReduxStatePayload<RecordingsFilterPayload>
    ) => {
      switch (action.payload.type) {
        case FILTER_TYPES.AGENTS:
          state.filteredAgents.unshift(action.payload.data)
          break
        case FILTER_TYPES.PROJECTS:
          state.filteredProjects.unshift(action.payload.data)
          break
        case FILTER_TYPES.TAGS:
          state.filteredTags.unshift(action.payload.data)
          break
        case FILTER_TYPES.VA_PLUGINS:
          state.filteredVA.unshift(action.payload.data)
          break
        default:
          break
      }
    },
    removeRecordingFilters: (
      //remove filters by type
      state,
      action: ReduxStatePayload<RecordingsFilterPayload>
    ) => {
      switch (action.payload.type) {
        case FILTER_TYPES.AGENTS: {
          const agentIndex = state.filteredAgents.findIndex(
            (item) => item.value === action.payload.data.value
          )
          state.filteredAgents.splice(agentIndex, 1)
          break
        }
        case FILTER_TYPES.PROJECTS: {
          const projectIndex = state.filteredProjects.findIndex(
            (item) => item.value === action.payload.data.value
          )
          state.filteredProjects.splice(projectIndex, 1)
          break
        }
        case FILTER_TYPES.TAGS: {
          const tagIndex = state.filteredTags.findIndex(
            (item) => item.value === action.payload.data.value
          )
          state.filteredTags.splice(tagIndex, 1)
          break
        }
        case FILTER_TYPES.VA_PLUGINS: {
          const vaIndex = state.filteredVA.findIndex(
            (item) => item.value === action.payload.data.value
          )
          state.filteredVA.splice(vaIndex, 1)
          break
        }
        default:
          break
      }
    },
    toggleUploadedRecordType: (state) => {
      state['only-recorded'] = false
      state['only-uploaded'] = true
    },
    toggleRecordedRecordType: (state) => {
      state['only-uploaded'] = false
      state['only-recorded'] = true
    },
    showAllRecordTypes: (state) => {
      state['only-recorded'] = false
      state['only-uploaded'] = false
    },
    setSelectRecordingMonth: (state, action: ReduxStatePayload<number>) => {
      //set the month selected
      state.selectedMonth = action.payload
    },
    setSelectRecordingYear: (state, action: ReduxStatePayload<number>) => {
      //set the year selected
      state.selectedYear = action.payload
    },
    clearAllFilter: (state) => {
      //clear all selected filter options
      state.filteredAgents = []
      state.filteredProjects = []
      state.filteredTags = []
      state.filteredVA = []
    },
    setCurrentRecordingPage: (state, action: ReduxStatePayload<number>) => {
      state['page-no'] = action.payload
    },
    addDownloadedFile: (state, action: PayloadAction<string>) => {
      state.downloadedFiles.push(action.payload)
    },
    removeDownloadedFile: (state, action: PayloadAction<string>) => {
      state.downloadedFiles.splice(
        state.downloadedFiles.indexOf(action.payload),
        1
      )
    },
    setSingulaDeletedRecording: (state, action: PayloadAction<IRecordings>) => {
      state.deletedRecordings = [...state.deletedRecordings, action.payload]
    },
    unSelectSingularDeletedRecording: (
      state,
      action: PayloadAction<string>
    ) => {
      state.deletedRecordings = state.deletedRecordings.filter(
        (item) => item.id !== action.payload
      )
    },
    selectAllRecordingInPage: (state) => {
      state.listRecording.forEach((recording) => {
        if (
          state.deletedRecordings.some(
            (deletedRecording) => deletedRecording.id === recording.id
          )
        ) {
          return
        }
        state.deletedRecordings.push(recording)
      })
    },
    unSelectAllRecordingInPage: (state) => {
      state.listRecording.forEach((recording) => {
        if (
          !state.deletedRecordings.some(
            (deletedRecording) => deletedRecording.id === recording.id
          )
        ) {
          return
        }
        state.deletedRecordings.splice(
          state.deletedRecordings.findIndex(
            (deletedRecording) => deletedRecording.id === recording.id
          ),
          1
        )
      })
    },
    unSelectDeletedRecordingsAllPages: (state) => {
      state.deletedRecordings = []
    }
  },
  //handle all the aftermath from createAsyncThunk
  extraReducers(builder) {
    // cases for fetchListRecordThunk
    builder.addCase(fetchListRecordingThunk.pending, (state) => {
      //when the request initiated
      state.loading = true
    })
    builder.addCase(fetchListRecordingThunk.fulfilled, (state, action) => {
      //the request succeeds
      state.listRecording = action.payload.data
      state.loading = false
      state.totalRecording = action.payload.total
    })
    builder.addCase(fetchListRecordingThunk.rejected, (state) => {
      //the request fails
      state.loading = false
    })

    //cases for assigning VA to a recording Thunk
    builder.addCase(assignVAPluginsThunk.fulfilled, (state, action) => {
      //the request succeeds
      state.listRecording = state.listRecording.map((recording) => {
        // apply the new VA list to the assigned recording
        if (recording.id === action.payload.id) {
          return {
            ...recording,
            va_plugins: action.payload.va_plugins
          }
        } else return recording
      })
      toast.success('Plugins assigned to the recording file!')
    })
    builder.addCase(assignVAPluginsThunk.rejected, (_, action) => {
      toast.error(action.payload as string) //notify the error message returned from the thunk
    })

    //cases for uploading recording chunks
    builder.addCase(uploadRecordByChunkThunk.fulfilled, (state, action) => {
      const updatedChunkIndex = state.chunkProgress.findIndex(
        (chunk) => chunk.url === action.payload.url
      )
      if (updatedChunkIndex !== undefined) {
        state.chunkProgress[updatedChunkIndex] = {
          ...state.chunkProgress[updatedChunkIndex],
          etag: action.payload.etag,
          isError: false
        }
      }
    })

    builder.addCase(uploadRecordByChunkThunk.rejected, (state, action) => {
      const errorValue = action.payload as { url: string; error: string }
      const updatedChunkIndex = state.chunkProgress.findIndex(
        (chunk) => chunk.url === errorValue.url
      )
      if (updatedChunkIndex !== undefined) {
        state.chunkProgress[updatedChunkIndex] = {
          ...state.chunkProgress[updatedChunkIndex],
          errorMsg: errorValue.error,
          isError: true
        }
      }
    })

    //cases for commiting blob file
    builder.addCase(commitAzureBlockThunk.fulfilled, (state, action) => {
      if (!state['only-uploaded']) {
        state['only-uploaded'] = true
        state['only-recorded'] = false
        return
      }
      if (state['page-no'] > 1) {
        state['page-no'] = 1
        return
      }
      state.listRecording.unshift(action.payload)
    })
    // ------------delete recordings---------------
    builder.addCase(deleteRecordingsThunk.fulfilled, (state) => {
      toast.success('Recordings have been deleted successfully')
      state.deletedRecordings = []
    })
    builder.addCase(deleteRecordingsThunk.rejected, (_, action) => {
      toast.error(action.payload as string)
    })
  }
})

export const {
  setOrderBy,
  removeRecordingFilters,
  addRecordingFilters,
  setSelectRecordingMonth,
  clearAllFilter,
  setSelectRecordingYear,
  playRecording,
  updateChunkProgress,
  refreshChunkProgress,
  updateChunkUrl,
  toggleUploadedRecordType,
  setCurrentRecordingPage,
  startUploading,
  toggleRecordedRecordType,
  showAllRecordTypes,
  addDownloadedFile,
  removeDownloadedFile,
  setSingulaDeletedRecording,
  selectAllRecordingInPage,
  unSelectAllRecordingInPage,
  unSelectSingularDeletedRecording,
  unSelectDeletedRecordingsAllPages
} = recordingSlice.actions
export const selectRecordingOrder = (state: RootState) =>
  state.recording.orderBy
export const selectFilteredAgents = (state: RootState) =>
  state.recording.filteredAgents
export const selectFilteredTags = (state: RootState) =>
  state.recording.filteredTags
export const selectFilteredVA = (state: RootState) => state.recording.filteredVA
export const selectFilteredProjects = (state: RootState) =>
  state.recording.filteredProjects
export const selectListRecording = (state: RootState) =>
  state.recording.listRecording
export const selectRecordingLoading = (state: RootState) =>
  state.recording.loading
export const selectRecordingMonth = (state: RootState) =>
  state.recording.selectedMonth
export const selectRecordingYear = (state: RootState) =>
  state.recording.selectedYear
export const selectSelectedRecording = (state: RootState) =>
  state.recording.selectedRecording
export const selectChunkProgress = (state: RootState) =>
  state.recording.chunkProgress
export const selectUploadRecordType = (state: RootState) =>
  state.recording['only-uploaded']
export const selectRecordedType = (state: RootState) =>
  state.recording['only-recorded']
export const selectTotalRecording = (state: RootState) =>
  state.recording.totalRecording
export const selectCurrentRecordingPage = (state: RootState) =>
  state.recording['page-no']
export const selectIsUploading = (state: RootState) =>
  state.recording.isUploading
export const selectDownloadedFiles = (state: RootState) =>
  state.recording.downloadedFiles
export const selectDeletedRecordings = (state: RootState) =>
  state.recording.deletedRecordings

export default recordingSlice.reducer
