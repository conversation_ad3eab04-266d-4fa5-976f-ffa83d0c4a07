import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
  CreateGroupInput,
  EditGroupAPIParams,
  IGroupQuery,
  IGroupUserModifyParams,
  IGroupUserRoleUpdateParams,
  ListGroup
} from '../../models/groups'
import {
  DEFAULT_PAGE_SIZE,
  ENV,
  getErrorMessage,
  renderUnauthorizedMessage
} from '../../utils'
import { GROUP_ENDPOINT } from '../../api/Endpoints'
import { IResponseData, IResponseDataWithPage } from '../../models/apiResponse'
import {
  addGroupUser,
  createNewGroup,
  deleteGroup,
  editGroup,
  getAuthorizedGroupList,
  getGroupDetail,
  removeGroupUser,
  updateGroupUsers
} from '../../api/Groups'
import { RootState } from '../store'
import { ReduxStatePayload } from '../../interfaces'
import { toast } from 'react-hot-toast'
export const DEFAULT_GROUP = {
  id: '',
  name: '',
  description: '',
  users: []
}

interface InitialState {
  listGroup: ListGroup[]
  loading: boolean
  'page-no': number
  'page-size': number
  search: string
  total: number
  'order-by': string
  groupDetail: ListGroup
  groupDetailLoading: boolean
}

const initialState: InitialState = {
  listGroup: [],
  loading: false,
  'page-no': 1,
  'page-size': DEFAULT_PAGE_SIZE,
  search: '',
  total: 0,
  'order-by': 'name-asc',
  groupDetail: DEFAULT_GROUP,
  groupDetailLoading: true
}

export const fetchListGroupThunk = createAsyncThunk<
  IResponseDataWithPage<ListGroup[]>,
  undefined
>(GROUP_ENDPOINT.LIST_GROUP, async (_, { getState }) => {
  const { groups: groupState } = getState() as RootState
  const params: IGroupQuery = {
    name: groupState.search.trim(),
    'page-no': groupState['page-no'],
    'page-size': groupState['page-size'],
    'order-by': groupState['order-by'].split('-')[0],
    'order-direction': groupState['order-by'].split('-')[1]
  }
  return await getAuthorizedGroupList(params)
})

export const getGroupInfoThunk = createAsyncThunk<
  IResponseData<ListGroup>,
  string
>(GROUP_ENDPOINT.LIST_GROUP + '/infos', async (group_id: string) => {
  return await getGroupDetail(group_id)
})

export const createGroupThunk = createAsyncThunk<
  Promise<unknown>,
  CreateGroupInput
>(
  GROUP_ENDPOINT.LIST_GROUP + '/create',
  async (params: CreateGroupInput, { getState, dispatch, rejectWithValue }) => {
    const { groups: groupState } = getState() as RootState
    try {
      await createNewGroup(params)
      if (groupState['page-no'] > 1) {
        dispatch(setGroupPage(1))
      } else {
        dispatch(fetchListGroupThunk())
      }

      toast.success('Successfully created a new group!')
    } catch (error) {
      const errMsg = getErrorMessage(error)

      if (errMsg.includes('group_name_unique_idx')) {
        toast.error("Duplicated Group's name")
      } else {
        toast.error(renderUnauthorizedMessage(errMsg))
      }
      return rejectWithValue(errMsg)
    }
  }
)

export const editGroupThunk = createAsyncThunk<unknown, EditGroupAPIParams>(
  GROUP_ENDPOINT.LIST_GROUP + '/edit',
  async (params: EditGroupAPIParams, { dispatch, rejectWithValue }) => {
    try {
      await editGroup(params)
      dispatch(fetchListGroupThunk())
      toast.success('Your changes have been saved!')
    } catch (error) {
      const errMsg = getErrorMessage(error)
      toast.error(renderUnauthorizedMessage(errMsg))
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const deleteGroupThunk = createAsyncThunk<unknown, string>(
  GROUP_ENDPOINT.LIST_GROUP + '/delete',
  async (group_id: string, { getState, dispatch, rejectWithValue }) => {
    const { groups: groupState } = getState() as RootState
    const isLastGroupInPage =
      groupState.listGroup.length > 1 && groupState['page-no'] > 1
    try {
      await deleteGroup(group_id)
      toast.success('Successfully deleted a group!')
      if (isLastGroupInPage) {
        dispatch(setGroupPage(groupState['page-no'] - 1))
      } else {
        dispatch(fetchListGroupThunk())
      }
    } catch (error) {
      if (ENV === 'DEV') {
        console.log(error)
      }
      toast.error(
        'Can only delete a group without any users or projects assigned to it'
      )
      return rejectWithValue(getErrorMessage(error))
    }
  }
)

export const removeGroupUserThunk = createAsyncThunk<
  IGroupUserModifyParams,
  IGroupUserModifyParams
>(
  GROUP_ENDPOINT.GROUP_USER + '/remove',
  async (params: IGroupUserModifyParams, { rejectWithValue }) => {
    try {
      await removeGroupUser(params)
      return params
    } catch (error) {
      const errMsg = getErrorMessage(error)
      toast.error(renderUnauthorizedMessage(errMsg))
      return rejectWithValue(errMsg)
    }
  }
)

export const addGroupUserThunk = createAsyncThunk<
  unknown,
  IGroupUserModifyParams
>(
  GROUP_ENDPOINT.GROUP_USER + '/assign',
  async (data: IGroupUserModifyParams, { dispatch, rejectWithValue }) => {
    try {
      await addGroupUser(data)
      dispatch(fetchListGroupThunk())
      dispatch(getGroupInfoThunk(data.group_id))
    } catch (error) {
      const errMsg = getErrorMessage(error)
      return rejectWithValue(errMsg)
    }
  }
)

export const updateUserRoleThunk = createAsyncThunk<
  IGroupUserRoleUpdateParams,
  IGroupUserRoleUpdateParams
>(
  GROUP_ENDPOINT.LIST_GROUP + '/roles',
  async (params: IGroupUserRoleUpdateParams, { rejectWithValue }) => {
    try {
      await updateGroupUsers(params)
      return params
    } catch (error) {
      const errMsg = getErrorMessage(error)
      return rejectWithValue(errMsg)
    }
  }
)

export const GroupSlice = createSlice({
  name: 'groups',
  initialState,
  reducers: {
    setGroupPage: (state, action: ReduxStatePayload<number>) => {
      state['page-no'] = action.payload
    },
    setGroupSearch: (state, action: ReduxStatePayload<string>) => {
      state.search = action.payload
    },
    setGroupSortingOption: (state, action: ReduxStatePayload<string>) => {
      state['order-by'] = action.payload
    },
    setGroupDetail: (state, action) => {
      state.groupDetail = action.payload
    }
  },

  extraReducers(builder) {
    // cases for fetching list group
    builder.addCase(fetchListGroupThunk.pending, (state) => {
      state.loading = true
    })
    builder.addCase(fetchListGroupThunk.rejected, (state) => {
      state.loading = false
    })
    builder.addCase(fetchListGroupThunk.fulfilled, (state, action) => {
      state.loading = false
      state.listGroup = action.payload.data
      state.total = action.payload.total
    })

    //cases for getting a single group's detail
    builder.addCase(getGroupInfoThunk.pending, (state) => {
      state.groupDetailLoading = true
    })
    builder.addCase(getGroupInfoThunk.rejected, (state) => {
      state.groupDetailLoading = false
    })
    builder.addCase(getGroupInfoThunk.fulfilled, (state, action) => {
      state.groupDetail = action.payload.data
      state.groupDetailLoading = false
    })

    //cases for remove a group's user
    builder.addCase(removeGroupUserThunk.fulfilled, (state, action) => {
      const selectedGroup = state.listGroup.findIndex(
        (group) => group.id === action.payload.group_id
      )
      const newUsersList = state.listGroup[selectedGroup].users?.filter(
        (user) => user.id !== action.payload.data.user_id
      )

      state.listGroup[selectedGroup] = {
        ...state.listGroup[selectedGroup],
        users: newUsersList
      }
      state.groupDetail = { ...state.groupDetail, users: newUsersList }
      toast.success('User removed successfully!')
    })

    //case for editing a user's role
    builder.addCase(updateUserRoleThunk.fulfilled, (state, action) => {
      if (state.groupDetail.users) {
        const selectedUserIndex = state.groupDetail.users?.findIndex(
          (user) => user.id === action.payload.data.user_id
        )
        state.groupDetail.users[selectedUserIndex] = {
          ...state.groupDetail.users[selectedUserIndex],
          role: {
            ...state.groupDetail.users[selectedUserIndex].role,
            id: action.payload.data.role_id,
            name: action.payload.role_name
          }
        }
        toast.success("You have successfully updated a user's role")
      }
    })
    builder.addCase(updateUserRoleThunk.rejected, (_, action) => {
      toast.error(renderUnauthorizedMessage(action.payload as string))
    })
  }
})

export const { setGroupPage, setGroupSearch, setGroupSortingOption } =
  GroupSlice.actions
export const selectListGroup = (state: RootState) => state.groups.listGroup
export const selectListGroupLoading = (state: RootState) => state.groups.loading
export const selectTotalGroup = (state: RootState) => state.groups.total
export const selectGroupSearchText = (state: RootState) => state.groups.search
export const selectGroupPage = (state: RootState) => state.groups['page-no']
export const selectGroupSortingOption = (state: RootState) =>
  state.groups['order-by']
export const selectGroupDetail = (state: RootState) => state.groups.groupDetail
export const selectGroupDetailLoading = (state: RootState) =>
  state.groups.groupDetailLoading

export default GroupSlice.reducer
