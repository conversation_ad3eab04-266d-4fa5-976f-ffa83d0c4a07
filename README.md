# VOLKSWAGEN UI

Check out guide to UI here.

## Tools Utilized

This project utilizes the following tools:

- [Vite](https://vitejs.dev) (Please ensure to install the latest version)
- [ReactJS](https://reactjs.org)
- [TypeScript](https://www.typescriptlang.org)
- [NodeJS](https://nodejs.org/en) (Recommended version: 16.x or later)
- [Vitest](https://vitest.dev)
- [Testing Library](https://testing-library.com)
- [Tailwindcss](https://tailwindcss.com)
- [Eslint](https://eslint.org)
- [Prettier](https://prettier.io)

## Getting Started

### Environment Variables

Create a `.env` file in the root of the project and copy content of `.env.sample` in to it as belows:

```text
VITE_API_ENDPOINT=<insert-your-api-url>
VITE_APP_WEBSOCKET_URL=<insert-your-api-url>
```

### Setting up for development

Clone the project.

```bash
git clone https://gitlab.knizsoft.com/delivery/longclawx01/volkswagen/volkswagen-ui.git
```

Navigate into the project directory.

```bash
cd volkswagen-ui
```

Install dependencies.

```bash
npm install
```

Start the development server with hot reloading at <http://localhost:5171>.

```bash
npm run dev
```

### Linting code

```bash
npm run lint
```

### Building for production

```bash
npm run build
```

```

### Testing the app locally

```bash
npm run preview
```

### Running test cases

```bash
npm run test
```

You can also interact with your tests via the UI.

```bash
npm run test:ui
```

### Routes

This application has the following main routes:

Refer to the application's routing documentation for more details.