name: vms
services:
  ui:
    build:
      context: .
      dockerfile: ./Dockerfile
    image: vms-ui:latest
    ports:
      - '5171:5171' # Expose the app on port 3001
    restart: always
    environment:
      VITE_API_ENDPOINT: ${VITE_API_ENDPOINT}
      VITE_SUPABASE_ENDPOINT: ${VITE_SUPABASE_ENDPOINT}
#     networks:
#       - vms-network
# networks:
#   vms-network:
#     external: true
#     name: vms
