{"name": "volkswagen-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "serve": "npx vite preview --port 5171 --host 0.0.0.0", "test": "vitest --run", "coverage": "vitest run --coverage", "docker-run": "docker compose up -d"}, "dependencies": {"@dnd-kit/core": "6.3.1", "@dnd-kit/modifiers": "9.0.0", "@ffmpeg/ffmpeg": "0.11.6", "@headlessui/react": "2.1.8", "@heroicons/react": "2.1.5", "@hookform/error-message": "2.0.1", "@reduxjs/toolkit": "2.2.7", "@tailwindcss/forms": "0.5.7", "@tailwindcss/typography": "0.5.14", "antd": "5.20.2", "axios": "^1.10.0", "dayjs": "1.11.13", "flv.js": "1.6.2", "hls.js": "1.5.15", "js-cookie": "^3.0.5", "m3u8-parser": "7.2.0", "moment": "2.30.1", "npm": "^10.9.2", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "7.52.2", "react-hot-toast": "2.6.0-beta.0", "react-markdown": "9.0.3", "react-redux": "9.1.2", "react-router-dom": "6.26.1", "redux-logger": "3.0.6", "redux-persist": "6.0.0", "remark-gfm": "4.0.0", "uuid": "11.0.3", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "5.0.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/js-cookie": "^3.0.6", "@types/m3u8-parser": "7.2.0", "@types/node": "^22.5.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/redux-logger": "^3.0.13", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.2", "@vitest/ui": "^3.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.20.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^26.0.0", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "typescript": "5.5.3", "typescript-eslint": "^8.23.0", "vite": "6.0.7", "vitest": "^3.0.2"}}