include:
  - template: Jobs/SAST.gitlab-ci.yml

#####################
# RULES #############
#####################
workflow:
  rules:
    # Merge Request Pipeline (CI)
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "main"
      variables:
        PIPELINE_NAME: 'CI'
        ENVIRONMENT: 'development'

    # Main Branch Pipeline (CD - Dev)
    - if: $CI_COMMIT_REF_NAME == "main"
      variables:
        PIPELINE_NAME: 'CD'
        ENVIRONMENT: 'development'

    # Tag Pipeline (CD - Staging/Production)
    - if: $CI_COMMIT_TAG
      variables:
        PIPELINE_NAME: 'CD'
        ENVIRONMENT: 'production'

variables:
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: '/certs'
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: '${DOCKER_TLS_CERTDIR}/client'
  DOCKERFILE: Dockerfile
  MONO_REPO: 'gitlab.knizsoft.com/delivery/longclawx01/volkswagen/mono-repo'
  GIT_TERMINAL_PROMPT: 1
  APPLICATION: vms-ui
  SERVICE_TYPE: ui

.dev:
  variables:
    TARGET_ENV: 'dev'
    LATEST_TAG: 'latest'
    DOCKER_IMAGE_TAG: ${CI_COMMIT_SHORT_SHA}
    SP_USER: $SP_DEV_ID
    SP_PASSWD: $SP_DEV_PASSWD
    DOCKER_REGISTRY: volkswagenacr${TARGET_ENV}.azurecr.io
    REPOSITORY_NAME: ${DOCKER_REGISTRY}/${APPLICATION}
    API_ENDPOINT: 'https://api.volks-${TARGET_ENV}.knizsoft.com/api/v1'
    SUPABASE_ENDPOINT: 'https://auth.volks-${TARGET_ENV}.knizsoft.com'
  rules:
    - if: '($PIPELINE_NAME == "CD" || $PIPELINE_NAME == "CI") && $ENVIRONMENT == "development"'

.stg:
  variables:
    TARGET_ENV: 'stg'
    LATEST_TAG: 'stg'
    DOCKER_IMAGE_TAG: ${CI_COMMIT_TAG}
    SP_USER: $SP_STG_ID
    SP_PASSWD: $SP_STG_PASSWD
    DOCKER_REGISTRY: volkswagenacr${TARGET_ENV}.azurecr.io
    REPOSITORY_NAME: ${DOCKER_REGISTRY}/${APPLICATION}
    API_ENDPOINT: 'https://api.volks-${TARGET_ENV}.knizsoft.com/api/v1'
    SUPABASE_ENDPOINT: 'https://auth.volks-${TARGET_ENV}.knizsoft.com'
  rules:
    - if: $PIPELINE_NAME == "CD" && $ENVIRONMENT == "production"

.raus:
  variables:
    TARGET_ENV: 'raus'
    LATEST_TAG: 'raus'
    DOCKER_IMAGE_TAG: ${CI_COMMIT_TAG}
    SP_USER: $SP_PROD_ID
    SP_PASSWD: $SP_PROD_PASSWD
    DOCKER_REGISTRY: volkswagenacr${TARGET_ENV}.azurecr.io
    REPOSITORY_NAME: ${DOCKER_REGISTRY}/${APPLICATION}
    API_ENDPOINT: 'https://api.volks-${TARGET_ENV}.knovel.org/api/v1'
    SUPABASE_ENDPOINT: 'https://auth.volks-${TARGET_ENV}.knovel.org'
  rules:
    - if: $PIPELINE_NAME == "CD" && $ENVIRONMENT == "production"

stages:
  - code-quality
  - build
  - test
  - deploy
  - security-scan
  - container-scan
  - dependency-check
  - release

.git_kustomize_setup:
  before_script:
    - apk add --no-cache git curl bash
    - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    - export PATH=$PATH:$(pwd)
    - kustomize version
    - git config --global user.name "CI-bot"
    - git config --global user.email "<EMAIL>"
    - git clone https://gitlab-ci-token:${MONOREPO_GITLAB_TOKEN}@${MONO_REPO}.git

#####################
# COMMON CHECK ######
#####################
lint-web:
  image: node:20-alpine
  stage: code-quality
  script:
    - npm install
    - npm run lint
  allow_failure: true
  rules:
    - if: $PIPELINE_NAME == "CI" && $ENVIRONMENT == "development"
    - when: never

owasp:
  image:
    name: owasp/dependency-check
  stage: dependency-check
  cache:
    paths:
      - .dependency-check/data
  before_script:
    - mkdir report
  script:
    - /usr/share/dependency-check/bin/dependency-check.sh --scan . --project "${CI_PROJECT_NAME}" --enableExperimental --format JSON
  allow_failure: true
  artifacts:
    paths:
      - dependency-check-report.json
  rules:
    - if: $PIPELINE_NAME == "CI" && $ENVIRONMENT == "development"
    - when: never

##################
#### DEV #########
##################
.build:
  image: docker:dind
  stage: build
  allow_failure: false
  services:
    - docker:dind
  before_script:
    - echo "$SP_PASSWD" | docker login ${DOCKER_REGISTRY} --username ${SP_USER} --password-stdin
    - docker pull ${REPOSITORY_NAME}:${LATEST_TAG} || echo "Docker image cache is unavailable, skip using layer caching"
  script:
    - |
      echo "Building docker image ${REPOSITORY_NAME}"
      DOCKER_BUILDKIT=1 docker buildx build -f Dockerfile --cache-from=${REPOSITORY_NAME}:${LATEST_TAG} --build-arg API_ENDPOINT=${API_ENDPOINT} --build-arg SUPABASE_ENDPOINT=${SUPABASE_ENDPOINT} -t ${REPOSITORY_NAME}:${DOCKER_IMAGE_TAG} -t ${REPOSITORY_NAME}:${LATEST_TAG} .
    - |
      echo "Pushing docker image ${REPOSITORY_NAME} to registry"
      docker push ${REPOSITORY_NAME}:${LATEST_TAG} && \
      docker push ${REPOSITORY_NAME}:${DOCKER_IMAGE_TAG}
    - echo "REPOSITORY_NAME=${REPOSITORY_NAME}:${DOCKER_IMAGE_TAG}"
    - echo "Clean up docker images"
    - docker rmi ${REPOSITORY_NAME}:${DOCKER_IMAGE_TAG}
    - docker rmi ${REPOSITORY_NAME}:${LATEST_TAG}

build-dev:
  stage: build
  extends:
    - .dev
    - .build

build-stg:
  when: manual
  stage: build
  extends:
    - .stg
    - .build

build-raus:
  when: manual
  stage: build
  extends:
    - .raus
    - .build

.deploy:
  image: alpine:3.21.3
  stage: deploy
  allow_failure: false
  when: on_success
  extends:
    - .git_kustomize_setup
  script:
    - cd mono-repo
    - git fetch
    - git checkout ${MONO_REPO_BRANCH} && git pull
    - |
      cd k8s/applications/${SERVICE_TYPE}/${TARGET_ENV} &&
      kustomize edit set image ${SERVICE_TYPE}=${REPOSITORY_NAME}:${DOCKER_IMAGE_TAG} &&
      git add kustomization.yaml
    - git commit -m "Update ${TARGET_ENV} ${SERVICE_TYPE} images to ${DOCKER_IMAGE_TAG}"
    - git push origin ${MONO_REPO_BRANCH}
    - cd ../ && rm -rf mono-repo

deploy-dev:
  stage: deploy
  needs:
    - build-dev
  extends:
    - .dev
    - .deploy
  rules:
    - if: $PIPELINE_NAME == "CD" && $ENVIRONMENT == "development"

deploy-stg:
  stage: deploy
  needs:
    - build-stg
  extends:
    - .stg
    - .deploy

deploy-raus:
  stage: deploy
  needs:
    - build-raus
  extends:
    - .raus
    - .deploy

####################
# Security Scanning
####################
.install-trivy:
  before_script:
    - export TRIVY_VERSION=$(wget -qO - "https://api.github.com/repos/aquasecurity/trivy/releases/latest" | grep '"tag_name":' | sed -E 's/.*"v([^"]+)".*/\1/')
    - wget --no-verbose https://github.com/aquasecurity/trivy/releases/download/v${TRIVY_VERSION}/trivy_${TRIVY_VERSION}_Linux-64bit.tar.gz -O - | tar -zxvf -
    - wget -O gitlab.tpl https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/gitlab.tpl
    - echo "$SP_PASSWD" | docker login ${DOCKER_REGISTRY} --username ${SP_USER} --password-stdin
    - docker build --pull --build-arg VERSION=${CI_COMMIT_TAG} -t ${CS_IMAGE} -f ${DOCKERFILE} .

semgrep-sast:
  stage: security-scan

container_scanning:
  image: docker:stable
  extends:
    - .dev
    - .install-trivy
  services:
    - name: docker:dind
      entrypoint: ['env', '-u', 'DOCKER_HOST']
      command: ['dockerd-entrypoint.sh']
  stage: container-scan
  variables:
    CS_IMAGE: ${DOCKER_REGISTRY}/${APPLICATION}:${LATEST_TAG}-cs
  script:
    - >
      ./trivy --cache-dir .trivycache/ image 
      --format template --template "@gitlab.tpl" 
      --output gl-container-scanning-report.json 
      --vuln-type os
      ${CS_IMAGE}
    # Fail on severe vulnerabilities
    - ./trivy image --exit-code 1 --vuln-type os --severity CRITICAL,HIGH ${CS_IMAGE}
    - docker rmi ${CS_IMAGE}
  cache:
    paths:
      - .trivycache/
  # Enables https://docs.gitlab.com/ee/user/application_security/container_scanning/ (Container Scanning report is available on GitLab EE Ultimate or GitLab.com Gold)
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json

dependency_scanning:
  image: docker:stable
  extends:
    - .dev
    - .install-trivy
  services:
    - name: docker:dind
      entrypoint: ['env', '-u', 'DOCKER_HOST']
      command: ['dockerd-entrypoint.sh']
  stage: container-scan
  variables:
    CS_IMAGE: ${DOCKER_REGISTRY}/${APPLICATION}:${LATEST_TAG}-cs
  script:
    - >
      ./trivy --cache-dir .trivycache/ image 
      --format template --template "@gitlab.tpl" 
      --output gl-dependency-scanning-report.json 
      --vuln-type library
      ${CS_IMAGE}
    # Fail on severe vulnerabilities
    - ./trivy image --exit-code 1 --vuln-type library --severity CRITICAL,HIGH ${CS_IMAGE}
    - docker rmi ${CS_IMAGE}
  cache:
    paths:
      - .trivycache/
  # Enables https://docs.gitlab.com/ee/user/application_security/container_scanning/ (Container Scanning report is available on GitLab EE Ultimate or GitLab.com Gold)
  artifacts:
    reports:
      container_scanning: gl-dependency-scanning-report.json

#######################
# Additional stuff
#######################
version-bump:
  stage: release
  image: node:20.10.0
  when: manual
  variables:
    #CI_DEBUG_TRACE: "true"
    GITLAB_TOKEN: $GITLAB_TOKEN
    NODE_TLS_REJECT_UNAUTHORIZED: '0'
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v?\d+\.\d+\.\d+$/'
      when: never
  before_script:
    - apt-get update && apt-get install -y --no-install-recommends git-core ca-certificates
    - cp /etc/gitlab-runner/certs/gitlab.knizsoft.com.crt /usr/local/share/ca-certificates/
    - update-ca-certificates
  script:
    - echo "Scan commit message and perform bumping where matched"
    - npm install --save-dev @semantic-release/gitlab@10.1.4
    - npx semantic-release
