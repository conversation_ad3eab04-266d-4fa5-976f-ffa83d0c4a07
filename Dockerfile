# Stage 1: Build the application
FROM node:18-alpine AS builder

ARG API_ENDPOINT
ARG SUPABASE_ENDPOINT
ARG SITE_URL
ARG LOGO_URL
ENV VITE_API_ENDPOINT=$API_ENDPOINT
ENV VITE_SUPABASE_ENDPOINT=$SUPABASE_ENDPOINT
ENV VITE_LOGO_URL=$LOGO_URL

# Set working directories
WORKDIR /app
# Copy only package.json into the container
COPY package.json ./
RUN npm install -g npm@10.9.2
# Install dependencies using npm
RUN npm install

# Copy the rest of the app's source code
COPY . .

# Build the Vite app
RUN npm run build

# Stage 2: Serve with Vite's preview command
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Copy built files from the builder stage
COPY --from=builder /app/dist ./dist

# Install Vite globally to use preview mode
RUN npm install -g vite@6.0.7
RUN npm install -g npm@10.9.2

# Expose the default port Vite uses for preview (5000)
EXPOSE 5171

# Command to preview the app using Vite
CMD ["npm", "run", "serve"]
