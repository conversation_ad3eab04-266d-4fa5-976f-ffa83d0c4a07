/// <reference types="vitest" />

import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { defineConfig } from 'vite'
import svgr from 'vite-plugin-svgr'
import tsconfigPaths from 'vite-tsconfig-paths'
// https://vitejs.dev/config https://vitest.dev/config
export default defineConfig(() => {
  // Create plugins array
  const plugins = [react(), tsconfigPaths(), svgr()]

  return {
    plugins,
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/setupTests.ts' // Optional for global setups
    },
    build: {
      rollupOptions: {
        output: {
          assetFileNames: (assetInfo) => {
            if (!assetInfo.name) return ''
            let extType = assetInfo?.name.split('.').at(1)
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType ?? '')) {
              extType = 'img'
            }
            return `assets/${extType}/[name]-[hash][extname]`
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js'
        }
      },

      target: 'esnext'
    },

    server: {
      headers: {
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Embedder-Policy': 'require-corp'
      },
      watch: {
        usePolling: true
      },
      host: '0.0.0.0',
      strictPort: true,
      port: 5171
    },

    base: '/',
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    }
  }
})
