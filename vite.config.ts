/// <reference types="vitest" />

import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { defineConfig, loadEnv } from 'vite'
import svgr from 'vite-plugin-svgr'
import tsconfigPaths from 'vite-tsconfig-paths'
// https://vitejs.dev/config https://vitest.dev/config
export default defineConfig(({ mode }) => {
  // Load environment variables based on the mode
  const env = loadEnv(mode, process.cwd(), '')

  // Get the favicon URL from environment variables with a fallback
  const faviconUrl = env.VITE_LOGO_URL || './vite.png'

  // Determine if the favicon is a remote URL or a local file
  const isRemoteUrl =
    faviconUrl.startsWith('http://') || faviconUrl.startsWith('https://')

  // For remote URLs, we'll skip the favicon plugin and handle it manually

  // Create plugins array
  const plugins = [react(), tsconfigPaths(), svgr()]

  // Only add favicon plugin if we have a local logo file
  if (isRemoteUrl) {
    // For remote URLs, add a plugin to inject the favicon link
    plugins.push({
      name: 'inject-remote-favicon',
      transformIndexHtml(html) {
        return html.replace(
          '<link rel="icon" type="image/svg+xml" href="/vite.png" />',
          `<link rel="icon" type="image/x-icon" href="${faviconUrl}" />`
        )
      }
    })
  }

  return {
    define: {
      'process.env.VITE_LOGO_URL': JSON.stringify(env.VITE_LOGO_URL)
    },
    plugins,
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/setupTests.ts' // Optional for global setups
    },
    build: {
      rollupOptions: {
        output: {
          assetFileNames: (assetInfo) => {
            if (!assetInfo.name) return ''
            let extType = assetInfo?.name.split('.').at(1)
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType ?? '')) {
              extType = 'img'
            }
            return `assets/${extType}/[name]-[hash][extname]`
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js'
        }
      },

      target: 'esnext'
    },

    server: {
      headers: {
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Embedder-Policy': 'require-corp'
      },
      watch: {
        usePolling: true
      },
      host: '0.0.0.0',
      strictPort: true,
      port: 5171
    },

    base: '/',
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    }
  }
})
