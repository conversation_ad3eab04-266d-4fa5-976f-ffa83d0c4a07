/** @type {import('tailwindcss').Config} */
import defaultTheme from 'tailwindcss/defaultTheme'
export const content = ['./src/**/*.{js,ts,jsx,tsx}']
import forms from '@tailwindcss/forms'
import typography from '@tailwindcss/typography'
export const theme = {
  extend: {
    width: {
      128: '60rem'
    },
    height: {
      90: '21rem'
    },

    flex: {
      2: '2 2 0%'
    },
    fontFamily: {
      sans: ['Inter', ...defaultTheme.fontFamily.sans]
    },
    backgroundColor: {
      defaultBg: '#f7f8fc',
      primary: '#853bac'
      // primary: '#313b80',
    },
    textColor: {
      primary: '#853bac',
      mainBlack: '#272727',
      lightPurple: '#F7F3FF',
      error: '#D81414',
      success: '#D81414',
      grey: '#7A7A7A',
      lightGrey: '#F4F4F4'
    },
    ringColor: {
      strokeGrey: '#7A7A7A',
      primary: '#853bac'
    },
    fill: {
      primary: '#853bac'
    },

    backgroundImage: {
      'default-thumbnail':
        "linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('/images/default_thumbnail.jpeg')"
    }
  }
}
export const base = './'
export const plugins = [forms, typography]
export const corePlugins = {
  preflight: false
}
