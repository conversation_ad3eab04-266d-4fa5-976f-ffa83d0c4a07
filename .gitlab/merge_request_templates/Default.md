### Overview

This MR introduces a new user profile page that displays the user's basic information, avatar, and a list of their recent activities. It also includes a section for the user to update their profile details.

### Changes

- Added `UserProfilePage` component
- Created `getUserProfile` and `updateUserProfile` API endpoints

### Screenshots

[Include relevant screenshots or GIFs to illustrate the changes]

### Testing

- Manually tested the user profile page on Chrome, Firefox, and Safari
- Added unit tests for the `UserProfilePage` component
- Verified API endpoint responses with Postman

### JIRA Tickets

- link to JIRA Ticket

### Related Issues

- link to related MRs (e.g. link to corresponding UI MR)

### Notes

- The user profile page currently displays only basic information. Additional sections for user settings, preferences, and other features can be added in future iterations.
